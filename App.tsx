import { useState } from "react";
import { SignUpRedesign } from "./components/SignUpRedesign";
import { Onboarding } from "./components/Onboarding";
import { BudgetModule } from "./components/BudgetModule";
import { OptimizationPlan } from "./components/OptimizationPlan";
import { ExpenseTracker } from "./components/ExpenseTracker";
import { SavingsInvestment } from "./components/SavingsInvestment";
import { SafetyNet } from "./components/SafetyNet";
import { DebtManager } from "./components/DebtManager";
import { SavingsGoals } from "./components/SavingsGoals";
import { InvestmentGoals } from "./components/InvestmentGoals";
import { Superannuation } from "./components/Superannuation";
import { Insurance } from "./components/Insurance";
import { AssetProtection } from "./components/AssetProtection";
import { EstatePlanning } from "./components/EstatePlanning";
import { Dashboard } from "./components/Dashboard";

interface IncomeData {
  amount: string;
  frequency: "monthly" | "weekly" | "yearly" | "";
  savings?: string;
}

interface Expense {
  id: string;
  name: string;
  amount: number;
  category: string;
  frequency: "weekly" | "monthly" | "yearly";
  icon: string;
  color: string;
}

interface SavingsInvestment {
  amount: number;
  frequency: "weekly" | "monthly" | "yearly";
}

interface ExpenseTrackerData {
  expenses: Expense[];
}

interface SavingsInvestmentData {
  savings: SavingsInvestment | null;
  investment: SavingsInvestment | null;
}

interface SafetyNetData {
  emergencyFund: number;
  bigExpenses: number;
  threeMonthBuffer: number;
  totalTarget: number;
  currentAmount: number;
  monthlyContribution: number;
  isComplete: boolean;
  lastContributionDate?: string;
}

interface Debt {
  id: string;
  name: string;
  totalAmount: number;
  startDate: string;
  remainingBalance: number;
  interestRate: number;
  monthlyPayment: number;
  dueDate: number; // day of month (1-31)
  extraPayment: number;
  estimatedPayoffDate: string;
  lastPaymentDate?: string;
}

interface DebtData {
  debts: Debt[];
  totalDebt: number;
  totalMonthlyPayments: number;
  payoffStrategy: "avalanche" | "snowball";
  extraPaymentAllocation: number;
  estimatedDebtFreeDate: string;
}

interface SavingsGoal {
  id: string;
  name: string;
  targetAmount: number;
  currentAmount: number;
  dueDate?: string;
  imageUrl?: string;
  monthlyContribution: number;
  estimatedCompletionDate: string;
  isCompleted: boolean;
  lastContributionDate?: string;
}

interface SavingsGoalsData {
  goals: SavingsGoal[];
  totalTargetAmount: number;
  totalSavedAmount: number;
  totalMonthlyContribution: number;
}

interface InvestmentGoal {
  id: string;
  name: string;
  investmentType:
    | "stocks"
    | "etfs"
    | "bonds"
    | "reits"
    | "crypto"
    | "index-funds"
    | "mutual-funds";
  targetAmount: number;
  currentAmount: number;
  monthlyContribution: number;
  expectedReturnRate: number; // Annual percentage
  contributionFrequency: "weekly" | "monthly" | "quarterly";
  estimatedCompletionDate: string;
  isCompleted: boolean;
  lastContributionDate?: string;
}

interface InvestmentGoalsData {
  goals: InvestmentGoal[];
  totalTargetAmount: number;
  totalInvestedAmount: number;
  totalMonthlyContribution: number;
  projectedValue: number;
}

interface SuperFund {
  id: string;
  fundName: string;
  currentBalance: number;
  extraContributions: number;
  contributionFrequency:
    | "weekly"
    | "fortnightly"
    | "monthly"
    | "quarterly"
    | "yearly";
  expectedReturnRate: number; // Annual percentage
  lastContributionDate?: string;
}

interface SuperannuationData {
  retirementAge: number;
  targetRetirementIncome: number; // Annual amount
  currentAge: number;
  superFunds: SuperFund[];
  totalCurrentBalance: number;
  totalExtraContributions: number;
  projectedRetirementBalance: number;
  projectedRetirementIncome: number;
  isOnTrack: boolean;
}

interface InsurancePolicy {
  id: string;
  type: string;
  policyNumber: string;
  itemOrMemberInsured: string;
  insuranceCompany: string;
  amountInsured: number;
  insideSuper: boolean;
  premiumAmount: number;
  premiumFrequency:
    | "weekly"
    | "monthly"
    | "quarterly"
    | "yearly";
  renewalDate: string;
  contactNumber: string;
  claimFormLink?: string;
  isActive: boolean;
  lastUpdated: string;
  notes?: string;
}

interface InsuranceData {
  policies: InsurancePolicy[];
  totalCoverage: number;
  totalPremiums: number;
  upcomingRenewals: InsurancePolicy[];
  coverageGaps: string[];
}

interface Asset {
  id: string;
  name: string;
  category: string;
  estimatedValue?: number;
  description?: string;
  isProtected: boolean;
  protectionMethod?: string;
  protectionDetails?: string;
  riskLevel: "low" | "medium" | "high";
  lastReviewDate?: string;
  notes?: string;
  createdDate: string;
  updatedDate: string;
}

interface AssetProtectionData {
  assets: Asset[];
  totalAssetValue: number;
  protectedAssets: number;
  unprotectedAssets: number;
  protectionScore: number;
  recommendations: string[];
}

interface Will {
  id: string;
  hasWill: boolean;
  willType:
    | "simple"
    | "testamentary_trust"
    | "complex"
    | "not_applicable";
  lastUpdated?: string;
  lawyerName?: string;
  lawyerContact?: string;
  executorName?: string;
  executorContact?: string;
  witnessNames?: string[];
  documentUrl?: string;
  fileName?: string;
  uploadDate?: string;
  isVerified: boolean;
  notes?: string;
  createdDate: string;
  updatedDate: string;
}

interface PowerOfAttorney {
  id: string;
  hasFinancialPOA: boolean;
  hasMedicalPOA: boolean;
  financialAttorneyName?: string;
  financialAttorneyContact?: string;
  medicalAttorneyName?: string;
  medicalAttorneyContact?: string;
  documentUrls?: string[];
  lastUpdated?: string;
  isCompleted: boolean;
  notes?: string;
}

interface ChecklistItem {
  id: string;
  title: string;
  description: string;
  category:
    | "will"
    | "power_of_attorney"
    | "beneficiaries"
    | "assets"
    | "tax"
    | "review";
  isCompleted: boolean;
  priority: "high" | "medium" | "low";
  dueDate?: string;
  completedDate?: string;
  notes?: string;
}

interface EstatePlanningData {
  will: Will;
  powerOfAttorney: PowerOfAttorney;
  beneficiaries: any[];
  totalEstateValue: number;
  hasCompletedChecklist: boolean;
  checklistItems: ChecklistItem[];
  completionScore: number;
  recommendations: string[];
  nextReviewDate?: string;
  professionalAdvice: boolean;
}

export default function App() {
  const [currentFlow, setCurrentFlow] = useState<
    | "signup"
    | "onboarding"
    | "budget"
    | "optimization"
    | "expenses"
    | "savings-investment"
    | "safety-net"
    | "debt"
    | "savings-goals"
    | "investment-goals"
    | "superannuation"
    | "insurance"
    | "asset-protection"
    | "estate-planning"
    | "dashboard"
  >("signup");
  const [incomeData, setIncomeData] =
    useState<IncomeData | null>(null);
  const [expenseData, setExpenseData] =
    useState<ExpenseTrackerData>({
      expenses: []
    });
  const [savingsInvestmentData, setSavingsInvestmentData] =
    useState<SavingsInvestmentData>({
      savings: null,
      investment: null
    });
  const [safetyNetData, setSafetyNetData] =
    useState<SafetyNetData | null>(null);
  const [debtData, setDebtData] = useState<DebtData | null>(
    null,
  );
  const [savingsGoalsData, setSavingsGoalsData] =
    useState<SavingsGoalsData | null>(null);
  const [investmentGoalsData, setInvestmentGoalsData] =
    useState<InvestmentGoalsData | null>(null);
  const [superannuationData, setSuperannuationData] =
    useState<SuperannuationData | null>(null);
  const [insuranceData, setInsuranceData] =
    useState<InsuranceData | null>(null);
  const [assetProtectionData, setAssetProtectionData] =
    useState<AssetProtectionData | null>(null);
  const [estatePlanningData, setEstatePlanningData] =
    useState<EstatePlanningData | null>(null);

  const handleIncomeComplete = (data: IncomeData) => {
    setIncomeData(data);
    setCurrentFlow("optimization");
  };

  const handleOptimizationComplete = (data: IncomeData) => {
    setIncomeData(data);
    setCurrentFlow("expenses");
  };

  const handleOptimizationBack = () => {
    setCurrentFlow("budget");
  };

  const handleExpensesComplete = (data: ExpenseTrackerData) => {
    setExpenseData(data);
    setCurrentFlow("savings-investment");
  };

  const handleExpensesBack = () => {
    // If called from dashboard, return to dashboard; otherwise go to optimization
    if (currentFlow === "expenses" && safetyNetData) {
      setCurrentFlow("dashboard");
    } else {
      setCurrentFlow("optimization");
    }
  };

  const handleSavingsInvestmentComplete = (data: SavingsInvestmentData) => {
    setSavingsInvestmentData(data);
    setCurrentFlow("dashboard");
  };

  const handleSavingsInvestmentBack = () => {
    setCurrentFlow("expenses");
  };

  const handleUpdateExpenses = (data: ExpenseTrackerData) => {
    setExpenseData(data);
  };

  const handleStartExpenses = () => {
    setCurrentFlow("expenses");
  };

  const handleStartSafetyNet = () => {
    setCurrentFlow("safety-net");
  };

  const handleSafetyNetComplete = (data: SafetyNetData) => {
    setSafetyNetData(data);
    setCurrentFlow("dashboard");
  };

  const handleSafetyNetBack = () => {
    setCurrentFlow("dashboard");
  };

  const handleStartDebtManager = () => {
    setCurrentFlow("debt");
  };

  const handleDebtComplete = (data: DebtData) => {
    setDebtData(data);
    setCurrentFlow("dashboard");
  };

  const handleDebtBack = () => {
    setCurrentFlow("dashboard");
  };

  const handleUpdateDebt = (data: DebtData) => {
    setDebtData(data);
  };

  const handleStartSavingsGoals = () => {
    setCurrentFlow("savings-goals");
  };

  const handleSavingsGoalsComplete = (
    data: SavingsGoalsData,
  ) => {
    setSavingsGoalsData(data);
    setCurrentFlow("dashboard");
  };

  const handleSavingsGoalsBack = () => {
    setCurrentFlow("dashboard");
  };

  const handleUpdateSavingsGoals = (data: SavingsGoalsData) => {
    setSavingsGoalsData(data);
  };

  const handleStartInvestmentGoals = () => {
    setCurrentFlow("investment-goals");
  };

  const handleInvestmentGoalsComplete = (
    data: InvestmentGoalsData,
  ) => {
    setInvestmentGoalsData(data);
    setCurrentFlow("dashboard");
  };

  const handleInvestmentGoalsBack = () => {
    setCurrentFlow("dashboard");
  };

  const handleUpdateInvestmentGoals = (
    data: InvestmentGoalsData,
  ) => {
    setInvestmentGoalsData(data);
  };

  const handleStartSuperannuation = () => {
    setCurrentFlow("superannuation");
  };

  const handleSuperannuationComplete = (
    data: SuperannuationData,
  ) => {
    setSuperannuationData(data);
    setCurrentFlow("dashboard");
  };

  const handleSuperannuationBack = () => {
    setCurrentFlow("dashboard");
  };

  const handleUpdateSuperannuation = (
    data: SuperannuationData,
  ) => {
    setSuperannuationData(data);
  };

  const handleStartInsurance = () => {
    setCurrentFlow("insurance");
  };

  const handleInsuranceComplete = (data: InsuranceData) => {
    setInsuranceData(data);
    setCurrentFlow("dashboard");
  };

  const handleInsuranceBack = () => {
    setCurrentFlow("dashboard");
  };

  const handleUpdateInsurance = (data: InsuranceData) => {
    setInsuranceData(data);
  };

  const handleStartAssetProtection = () => {
    setCurrentFlow("asset-protection");
  };

  const handleAssetProtectionComplete = (
    data: AssetProtectionData,
  ) => {
    setAssetProtectionData(data);
    setCurrentFlow("dashboard");
  };

  const handleAssetProtectionBack = () => {
    setCurrentFlow("dashboard");
  };

  const handleUpdateAssetProtection = (
    data: AssetProtectionData,
  ) => {
    setAssetProtectionData(data);
  };

  const handleStartEstatePlanning = () => {
    setCurrentFlow("estate-planning");
  };

  const handleEstatePlanningComplete = (
    data: EstatePlanningData,
  ) => {
    setEstatePlanningData(data);
    setCurrentFlow("dashboard");
  };

  const handleEstatePlanningBack = () => {
    setCurrentFlow("dashboard");
  };

  const handleUpdateEstatePlanning = (
    data: EstatePlanningData,
  ) => {
    setEstatePlanningData(data);
  };

  // Handle tab navigation for completed sections
  const handleTabNavigation = (tab: "optimise" | "maximise" | "protect") => {
    switch (tab) {
      case "optimise":
        // Navigate to the first incomplete step in optimise, or last completed if all done
        if (!safetyNetData) {
          setCurrentFlow("safety-net");
        } else if (!debtData) {
          setCurrentFlow("debt");
        } else if (!savingsGoalsData) {
          setCurrentFlow("savings-goals");
        } else {
          // All optimise steps completed, go to last one
          setCurrentFlow("savings-goals");
        }
        break;
      case "maximise":
        // Navigate to the first incomplete step in maximise, or last completed if all done
        if (!investmentGoalsData) {
          setCurrentFlow("investment-goals");
        } else if (!superannuationData) {
          setCurrentFlow("superannuation");
        } else {
          // All maximise steps completed, go to last one
          setCurrentFlow("superannuation");
        }
        break;
      case "protect":
        // Navigate to the first incomplete step in protect, or last completed if all done
        if (!insuranceData) {
          setCurrentFlow("insurance");
        } else if (!assetProtectionData) {
          setCurrentFlow("asset-protection");
        } else if (!estatePlanningData) {
          setCurrentFlow("estate-planning");
        } else {
          // All protect steps completed, go to last one
          setCurrentFlow("estate-planning");
        }
        break;
    }
  };

  const handleLogout = () => {
    // Reset all user data and return to signup
    setIncomeData(null);
    setExpenseData({
      expenses: []
    });
    setSavingsInvestmentData({
      savings: null,
      investment: null
    });
    setSafetyNetData(null);
    setDebtData(null);
    setSavingsGoalsData(null);
    setInvestmentGoalsData(null);
    setSuperannuationData(null);
    setInsuranceData(null);
    setAssetProtectionData(null);
    setEstatePlanningData(null);
    setCurrentFlow("signup");
  };

  // Calculate financial data for all components
  const getFinancialData = () => {
    if (!incomeData)
      return {
        monthlyIncome: 0,
        monthlyExpenses: 0,
        monthlySavings: 0,
        monthlyInvestment: 0,
        availableFromIncome: 0,
        availableCashSavings: 15000, // Default available cash
      };

    const getMonthlyAmount = (
      item: SavingsInvestment | null,
    ) => {
      if (!item) return 0;
      let monthlyAmount = item.amount;
      if (item.frequency === "weekly") monthlyAmount *= 4.33;
      if (item.frequency === "yearly") monthlyAmount /= 12;
      return monthlyAmount;
    };

    const getMonthlyIncome = () => {
      const amount = parseFloat(incomeData.amount) || 0;
      if (incomeData.frequency === "weekly")
        return amount * 4.33;
      if (incomeData.frequency === "yearly") return amount / 12;
      return amount;
    };

    const monthlyIncome = getMonthlyIncome();
    const totalMonthlyExpenses = expenseData.expenses.reduce(
      (total, expense) => {
        let monthlyAmount = expense.amount;
        if (expense.frequency === "weekly")
          monthlyAmount *= 4.33;
        if (expense.frequency === "yearly") monthlyAmount /= 12;
        return total + monthlyAmount;
      },
      0,
    );

    const monthlySavings = getMonthlyAmount(
      savingsInvestmentData.savings,
    );
    const monthlyInvestment = getMonthlyAmount(
      savingsInvestmentData.investment,
    );

    // Factor in safety net monthly contribution if it exists
    const safetyNetContribution =
      safetyNetData?.monthlyContribution || 0;

    // Factor in debt payments if they exist
    const debtPayments = debtData?.totalMonthlyPayments || 0;

    // Factor in savings goals contributions if they exist
    const savingsGoalsContribution =
      savingsGoalsData?.totalMonthlyContribution || 0;

    // Factor in investment goals contributions if they exist
    const investmentGoalsContribution =
      investmentGoalsData?.totalMonthlyContribution || 0;

    // Factor in superannuation extra contributions if they exist
    const superContribution =
      superannuationData?.totalExtraContributions || 0;

    // Factor in insurance premiums if they exist
    const insurancePremiums = insuranceData?.totalPremiums || 0;

    const netAfterExpenses =
      monthlyIncome -
      totalMonthlyExpenses -
      monthlySavings -
      monthlyInvestment -
      safetyNetContribution -
      debtPayments -
      savingsGoalsContribution -
      investmentGoalsContribution -
      superContribution -
      insurancePremiums;

    return {
      monthlyIncome,
      monthlyExpenses: totalMonthlyExpenses,
      monthlySavings,
      monthlyInvestment,
      availableFromIncome: Math.max(0, netAfterExpenses),
      availableCashSavings: 15000, // This could be made dynamic based on user input
    };
  };

  return (
    <>
      {currentFlow === "signup" && (
        <SignUpRedesign
          onComplete={() => setCurrentFlow("onboarding")}
        />
      )}
      {currentFlow === "onboarding" && (
        <Onboarding
          onComplete={() => setCurrentFlow("budget")}
        />
      )}
      {currentFlow === "budget" && (
        <BudgetModule 
          onComplete={handleIncomeComplete}
          initialData={incomeData}
        />
      )}
      {currentFlow === "optimization" && incomeData && (
        <OptimizationPlan
          incomeData={incomeData}
          onComplete={handleOptimizationComplete}
          onBack={handleOptimizationBack}
        />
      )}
      {currentFlow === "expenses" && (
        <ExpenseTracker 
          onComplete={handleExpensesComplete}
          onBack={handleExpensesBack}
          initialData={expenseData}
        />
      )}
      {currentFlow === "savings-investment" && (
        <SavingsInvestment 
          onComplete={handleSavingsInvestmentComplete} 
          onBack={handleSavingsInvestmentBack}
          initialData={savingsInvestmentData}
        />
      )}
      {currentFlow === "safety-net" && (
        <SafetyNet
          financialData={getFinancialData()}
          onComplete={handleSafetyNetComplete}
          onBack={handleSafetyNetBack}
          onTabNavigation={handleTabNavigation}
          initialData={safetyNetData}
        />
      )}
      {currentFlow === "debt" && (
        <DebtManager
          financialData={getFinancialData()}
          onComplete={handleDebtComplete}
          onBack={handleDebtBack}
          onTabNavigation={handleTabNavigation}
          initialData={debtData}
        />
      )}
      {currentFlow === "savings-goals" && (
        <SavingsGoals
          financialData={getFinancialData()}
          onComplete={handleSavingsGoalsComplete}
          onBack={handleSavingsGoalsBack}
          onTabNavigation={handleTabNavigation}
          initialData={savingsGoalsData}
        />
      )}
      {currentFlow === "investment-goals" && (
        <InvestmentGoals
          financialData={getFinancialData()}
          onComplete={handleInvestmentGoalsComplete}
          onBack={handleInvestmentGoalsBack}
          onTabNavigation={handleTabNavigation}
          initialData={investmentGoalsData}
        />
      )}
      {currentFlow === "superannuation" && (
        <Superannuation
          financialData={getFinancialData()}
          onComplete={handleSuperannuationComplete}
          onBack={handleSuperannuationBack}
          onTabNavigation={handleTabNavigation}
          initialData={superannuationData}
        />
      )}
      {currentFlow === "insurance" && (
        <Insurance
          financialData={getFinancialData()}
          currentAge={superannuationData?.currentAge || 30}
          totalAssets={150000} // This could be calculated from other data
          dependents={1} // This could be from user profile
          onComplete={handleInsuranceComplete}
          onBack={handleInsuranceBack}
          onTabNavigation={handleTabNavigation}
          initialData={insuranceData}
        />
      )}
      {currentFlow === "asset-protection" && (
        <AssetProtection
          financialData={getFinancialData()}
          onComplete={handleAssetProtectionComplete}
          onBack={handleAssetProtectionBack}
          onTabNavigation={handleTabNavigation}
          initialData={assetProtectionData}
        />
      )}
      {currentFlow === "estate-planning" && (
        <EstatePlanning
          financialData={getFinancialData()}
          totalAssets={
            assetProtectionData?.totalAssetValue || 150000
          }
          onComplete={handleEstatePlanningComplete}
          onBack={handleEstatePlanningBack}
          onTabNavigation={handleTabNavigation}
          initialData={estatePlanningData}
        />
      )}
      {currentFlow === "dashboard" && incomeData && (
        <Dashboard
          incomeData={incomeData}
          expenseData={{...expenseData, ...savingsInvestmentData}}
          safetyNetData={safetyNetData}
          debtData={debtData}
          savingsGoalsData={savingsGoalsData}
          investmentGoalsData={investmentGoalsData}
          superannuationData={superannuationData}
          insuranceData={insuranceData}
          assetProtectionData={assetProtectionData}
          estatePlanningData={estatePlanningData}
          onUpdateExpenses={handleUpdateExpenses}
          onUpdateDebt={handleUpdateDebt}
          onUpdateSavingsGoals={handleUpdateSavingsGoals}
          onUpdateInvestmentGoals={handleUpdateInvestmentGoals}
          onUpdateSuperannuation={handleUpdateSuperannuation}
          onUpdateInsurance={handleUpdateInsurance}
          onUpdateAssetProtection={handleUpdateAssetProtection}
          onUpdateEstatePlanning={handleUpdateEstatePlanning}
          onStartExpenses={handleStartExpenses}
          onStartSafetyNet={handleStartSafetyNet}
          onStartDebtManager={handleStartDebtManager}
          onStartSavingsGoals={handleStartSavingsGoals}
          onStartInvestmentGoals={handleStartInvestmentGoals}
          onStartSuperannuation={handleStartSuperannuation}
          onStartInsurance={handleStartInsurance}
          onStartAssetProtection={handleStartAssetProtection}
          onStartEstatePlanning={handleStartEstatePlanning}
          onLogout={handleLogout}
        />
      )}
    </>
  );
}