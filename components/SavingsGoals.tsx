import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "motion/react";
import { 
  Target, 
  ArrowLeft, 
  ArrowRight, 
  Star, 
  AlertCircle, 
  CheckCircle, 
  Calendar, 
  DollarSign,
  Camera,
  Plus,
  Minus,
  Info,
  TrendingUp,
  Clock,
  Trash2,
  Edit,
  Upload,
  <PERSON>rk<PERSON>,
  <PERSON>
} from "lucide-react";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Progress } from "./ui/progress";
import { Alert, AlertDescription } from "./ui/alert";
import { Separator } from "./ui/separator";
import { Badge } from "./ui/badge";
import { Textarea } from "./ui/textarea";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "./ui/dialog";
import obieLogo from 'figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png';

interface FinancialData {
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlySavings: number;
  monthlyInvestment: number;
  availableFromIncome: number;
  availableCashSavings: number;
}

interface SavingsGoal {
  id: string;
  name: string;
  targetAmount: number;
  currentAmount: number;
  dueDate?: string;
  imageUrl?: string;
  monthlyContribution: number;
  estimatedCompletionDate: string;
  isCompleted: boolean;
  lastContributionDate?: string;
}

interface SavingsGoalsData {
  goals: SavingsGoal[];
  totalTargetAmount: number;
  totalSavedAmount: number;
  totalMonthlyContribution: number;
}

interface SavingsGoalsProps {
  financialData: FinancialData;
  onComplete: (data: SavingsGoalsData) => void;
  onBack: () => void;
  onTabNavigation?: (tab: "optimise" | "maximise" | "protect") => void;
  initialData?: SavingsGoalsData | null;
}

interface NewGoal {
  name: string;
  targetAmount: string;
  dueDate: string;
  imageSearchQuery: string;
  imageUrl?: string;
  fromCashSavings: number;
  monthlyContribution: number;
}

// Helper functions
const calculateEstimatedCompletionDate = (
  remainingAmount: number, 
  monthlyContribution: number
): string => {
  if (monthlyContribution <= 0) return 'No timeline set';
  
  const months = Math.ceil(remainingAmount / monthlyContribution);
  const completionDate = new Date();
  completionDate.setMonth(completionDate.getMonth() + months);
  return completionDate.toISOString().split('T')[0];
};

const calculateRequiredMonthlyContribution = (
  targetAmount: number,
  currentAmount: number,
  dueDate: string
): number => {
  if (!dueDate) return 0;
  
  const target = new Date(dueDate);
  const now = new Date();
  const monthsRemaining = Math.max(1, Math.ceil((target.getTime() - now.getTime()) / (1000 * 60 * 60 * 24 * 30)));
  
  return Math.ceil((targetAmount - currentAmount) / monthsRemaining);
};

const formatAmount = (amount: number) => `$${amount.toLocaleString()}`;
const formatDate = (dateString: string) => {
  if (!dateString || dateString === 'No timeline set') return dateString;
  return new Date(dateString).toLocaleDateString('en-US', { 
    month: 'short', 
    year: 'numeric' 
  });
};

// Predefined goal images for common goals
const getGoalImage = (goalName: string): string => {
  const goalType = goalName.toLowerCase();
  
  if (goalType.includes('vacation') || goalType.includes('travel') || goalType.includes('trip')) {
    return 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=400&h=300&fit=crop';
  }
  if (goalType.includes('car') || goalType.includes('vehicle')) {
    return 'https://images.unsplash.com/photo-1492144534655-ae79c964c9d7?w=400&h=300&fit=crop';
  }
  if (goalType.includes('house') || goalType.includes('home') || goalType.includes('apartment')) {
    return 'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=400&h=300&fit=crop';
  }
  if (goalType.includes('wedding') || goalType.includes('marriage')) {
    return 'https://images.unsplash.com/photo-1519741497674-611481863552?w=400&h=300&fit=crop';
  }
  if (goalType.includes('education') || goalType.includes('course') || goalType.includes('school')) {
    return 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=300&fit=crop';
  }
  if (goalType.includes('laptop') || goalType.includes('computer') || goalType.includes('tech')) {
    return 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop';
  }
  if (goalType.includes('emergency') || goalType.includes('fund')) {
    return 'https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=400&h=300&fit=crop';
  }
  
  // Default goal image
  return 'https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?w=400&h=300&fit=crop';
};

export function SavingsGoals({ financialData, onComplete, onBack, onTabNavigation, initialData }: SavingsGoalsProps) {
  const [currentStep, setCurrentStep] = useState(initialData ? 2 : 1);
  const [goals, setGoals] = useState<SavingsGoal[]>(initialData?.goals || []);
  const [newGoal, setNewGoal] = useState<NewGoal>({
    name: '',
    targetAmount: '',
    dueDate: '',
    imageSearchQuery: '',
    imageUrl: '',
    fromCashSavings: 0,
    monthlyContribution: 0
  });
  const [showAddGoal, setShowAddGoal] = useState(false);
  const [showAddGoalDialog, setShowAddGoalDialog] = useState(false);
  const [isLoadingImage, setIsLoadingImage] = useState(false);
  const [hasVisitedStep2, setHasVisitedStep2] = useState(false);
  const [goalAllocations, setGoalAllocations] = useState<{[key: string]: {fromCashSavings: number, monthlyContribution: number}}>({});

  // Show form by default when first visiting step 2 and no goals exist (unless coming from existing data)
  useEffect(() => {
    if (currentStep === 2 && !hasVisitedStep2 && goals.length === 0 && !initialData) {
      setShowAddGoal(true);
      setHasVisitedStep2(true);
    }
  }, [currentStep, hasVisitedStep2, goals.length, initialData]);

  // Initialize goal allocations when goals are loaded
  useEffect(() => {
    const newAllocations: {[key: string]: {fromCashSavings: number, monthlyContribution: number}} = {};
    goals.forEach(goal => {
      if (!goalAllocations[goal.id]) {
        newAllocations[goal.id] = {
          fromCashSavings: goal.currentAmount || 0,
          monthlyContribution: goal.monthlyContribution || 0
        };
      }
    });
    
    if (Object.keys(newAllocations).length > 0) {
      setGoalAllocations(prev => ({...prev, ...newAllocations}));
    }
  }, [goals.length]); // Change dependency to avoid infinite loops

  // Calculate totals
  const totalTargetAmount = goals.reduce((sum, goal) => sum + goal.targetAmount, 0);
  const totalSavedAmount = goals.reduce((sum, goal) => sum + goal.currentAmount, 0);
  const totalMonthlyContribution = goals.reduce((sum, goal) => sum + goal.monthlyContribution, 0);

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      onBack();
    }
  };

  const handleSearchImage = async () => {
    if (!newGoal.imageSearchQuery.trim()) return;
    
    setIsLoadingImage(true);
    try {
      // Use Unsplash tool to search for images
      const response = await fetch(`https://api.unsplash.com/search/photos?query=${encodeURIComponent(newGoal.imageSearchQuery)}&per_page=1&client_id=demo`, {
        headers: {
          'Accept': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        if (data.results && data.results.length > 0) {
          setNewGoal({ ...newGoal, imageUrl: data.results[0].urls.small });
        } else {
          // Fallback to predefined image based on goal name
          setNewGoal({ ...newGoal, imageUrl: getGoalImage(newGoal.name) });
        }
      } else {
        // Fallback to predefined image
        setNewGoal({ ...newGoal, imageUrl: getGoalImage(newGoal.name) });
      }
    } catch (error) {
      console.error('Failed to fetch image:', error);
      // Fallback to predefined image based on goal name
      setNewGoal({ ...newGoal, imageUrl: getGoalImage(newGoal.name) });
    } finally {
      setIsLoadingImage(false);
    }
  };

  const handleAddGoal = () => {
    if (!newGoal.name || !newGoal.targetAmount) return;

    const targetAmount = parseFloat(newGoal.targetAmount);
    const currentAmount = newGoal.fromCashSavings;
    const remainingAmount = targetAmount - currentAmount;

    let monthlyContribution = newGoal.monthlyContribution;
    let estimatedCompletionDate = '';

    if (newGoal.dueDate) {
      // Calculate required monthly contribution for due date
      const requiredMonthly = calculateRequiredMonthlyContribution(
        targetAmount,
        currentAmount,
        newGoal.dueDate
      );
      monthlyContribution = Math.max(monthlyContribution, requiredMonthly);
      estimatedCompletionDate = newGoal.dueDate;
    } else {
      // Calculate estimated completion date based on monthly contribution
      estimatedCompletionDate = calculateEstimatedCompletionDate(
        remainingAmount,
        monthlyContribution
      );
    }

    const goal: SavingsGoal = {
      id: Date.now().toString(),
      name: newGoal.name,
      targetAmount,
      currentAmount,
      dueDate: newGoal.dueDate || undefined,
      imageUrl: newGoal.imageUrl || getGoalImage(newGoal.name),
      monthlyContribution,
      estimatedCompletionDate,
      isCompleted: currentAmount >= targetAmount,
      lastContributionDate: new Date().toISOString()
    };

    setGoals([...goals, goal]);
    setNewGoal({
      name: '',
      targetAmount: '',
      dueDate: '',
      imageSearchQuery: '',
      imageUrl: '',
      fromCashSavings: 0,
      monthlyContribution: 0
    });
    setShowAddGoal(false);
    setShowAddGoalDialog(false);
  };

  const handleRemoveGoal = (goalId: string) => {
    setGoals(goals.filter(goal => goal.id !== goalId));
    // Remove allocation data for this goal
    const updatedAllocations = { ...goalAllocations };
    delete updatedAllocations[goalId];
    setGoalAllocations(updatedAllocations);
  };

  const handleUpdateGoalAllocation = (goalId: string, field: 'fromCashSavings' | 'monthlyContribution', value: number) => {
    // Update allocation state
    setGoalAllocations(prev => {
      const currentAllocation = prev[goalId] || { fromCashSavings: 0, monthlyContribution: 0 };
      const updatedAllocation = { ...currentAllocation, [field]: value };
      
      // Also update the goals with the new allocation
      setGoals(prevGoals => prevGoals.map(goal => {
        if (goal.id === goalId) {
          const currentAmount = field === 'fromCashSavings' ? value : currentAllocation.fromCashSavings;
          const monthlyContribution = field === 'monthlyContribution' ? value : currentAllocation.monthlyContribution;
          const remainingAmount = goal.targetAmount - currentAmount;
          
          let estimatedCompletionDate = goal.estimatedCompletionDate;
          if (monthlyContribution > 0) {
            estimatedCompletionDate = calculateEstimatedCompletionDate(remainingAmount, monthlyContribution);
          } else if (goal.dueDate) {
            estimatedCompletionDate = goal.dueDate;
          }
          
          return {
            ...goal,
            currentAmount,
            monthlyContribution,
            estimatedCompletionDate,
            isCompleted: currentAmount >= goal.targetAmount
          };
        }
        return goal;
      }));
      
      return {
        ...prev,
        [goalId]: updatedAllocation
      };
    });
  };

  const handleComplete = () => {
    const savingsGoalsData: SavingsGoalsData = {
      goals,
      totalTargetAmount,
      totalSavedAmount,
      totalMonthlyContribution
    };

    onComplete(savingsGoalsData);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center gap-4">
            <img src={obieLogo} alt="Obiemoney Logo" className="h-10 w-auto" />
            <div>
              <h1 className="text-3xl font-bold text-blue-600">Set your Financial Savings Goals</h1>
              <p className="text-gray-600">Step 4: Turn dreams into achievable targets</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 bg-white/60 rounded-full px-4 py-2">
              <span className="text-sm font-medium text-gray-600">Step {currentStep} of 4</span>
            </div>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <div className="mb-8">
          <Progress value={(currentStep / 4) * 100} className="h-2 bg-white/60" />
          <div className="flex justify-between mt-2 text-sm text-gray-600">
            <span className={currentStep >= 1 ? 'text-blue-600 font-medium' : ''}>Learn</span>
            <span className={currentStep >= 2 ? 'text-blue-600 font-medium' : ''}>Create Goals</span>
            <span className={currentStep >= 3 ? 'text-blue-600 font-medium' : ''}>Fund Goals</span>
            <span className={currentStep >= 4 ? 'text-blue-600 font-medium' : ''}>Summary</span>
          </div>
        </div>

        <AnimatePresence mode="wait">
          {/* Step 1: Education */}
          {currentStep === 1 && (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-8"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-blue-100 rounded-full mb-4">
                    <Target className="h-8 w-8 text-blue-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">
                    The Power of Financial Goal Setting
                  </h2>
                  <p className="text-gray-600 max-w-2xl mx-auto">
                    Transform your dreams into reality with strategic savings goals. Research shows that people who write down their goals are 42% more likely to achieve them.
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  <Card className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-blue-100 rounded-full mb-4">
                        <Target className="h-6 w-6 text-blue-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Short-term Goals</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Goals you can achieve within 1-2 years. Vacation, electronics, course, emergency fund top-up.
                      </p>
                      <div className="text-xs text-blue-600 bg-blue-50 rounded-lg p-2">
                        Timeline: 1-24 months
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6 bg-gradient-to-br from-indigo-50 to-purple-50 border-indigo-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-indigo-100 rounded-full mb-4">
                        <Calendar className="h-6 w-6 text-indigo-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Medium-term Goals</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Goals for the next 2-5 years. Car down payment, home renovation, wedding expenses.
                      </p>
                      <div className="text-xs text-indigo-600 bg-indigo-50 rounded-lg p-2">
                        Timeline: 2-5 years
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6 bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-purple-100 rounded-full mb-4">
                        <Star className="h-6 w-6 text-purple-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Dream Goals</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Your biggest aspirations. Home down payment, starting a business, world travel.
                      </p>
                      <div className="text-xs text-purple-600 bg-purple-50 rounded-lg p-2">
                        Timeline: 5+ years
                      </div>
                    </div>
                  </Card>
                </div>

                <div className="space-y-6 mb-8">
                  <h3 className="font-bold text-gray-800 text-center">Why Savings Goals Work</h3>
                  
                  <div className="grid md:grid-cols-2 gap-4">
                    <Card className="p-4 bg-green-50 border-green-200">
                      <div className="flex items-start gap-3">
                        <div className="p-2 bg-green-100 rounded-lg">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-800">Clear Direction</h4>
                          <p className="text-sm text-gray-600">Specific goals give your savings purpose and make it easier to stay motivated when faced with spending temptations.</p>
                        </div>
                      </div>
                    </Card>
                    
                    <Card className="p-4 bg-orange-50 border-orange-200">
                      <div className="flex items-start gap-3">
                        <div className="p-2 bg-orange-100 rounded-lg">
                          <Clock className="h-4 w-4 text-orange-600" />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-800">Smart Timeline</h4>
                          <p className="text-sm text-gray-600">Break down big dreams into manageable monthly contributions. $50/month can become $1,200 in just two years!</p>
                        </div>
                      </div>
                    </Card>
                    
                    <Card className="p-4 bg-purple-50 border-purple-200">
                      <div className="flex items-start gap-3">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <TrendingUp className="h-4 w-4 text-purple-600" />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-800">Progress Tracking</h4>
                          <p className="text-sm text-gray-600">Visual progress keeps you motivated. Watching your goal meter fill up creates positive reinforcement.</p>
                        </div>
                      </div>
                    </Card>
                    
                    <Card className="p-4 bg-blue-50 border-blue-200">
                      <div className="flex items-start gap-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Sparkles className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-800">Achievement Joy</h4>
                          <p className="text-sm text-gray-600">Reaching a savings goal gives you confidence and momentum to tackle even bigger financial challenges.</p>
                        </div>
                      </div>
                    </Card>
                  </div>
                </div>

                <Alert className="border-blue-200 bg-blue-50">
                  <Info className="h-4 w-4 text-blue-600" />
                  <AlertDescription className="text-blue-800">
                    <strong>Pro Tip:</strong> Start with 1-2 meaningful goals rather than many small ones. Focus creates momentum and success builds confidence!
                  </AlertDescription>
                </Alert>
              </Card>
            </motion.div>
          )}

          {/* Step 3: Fund Goals */}
          {currentStep === 3 && (
            <motion.div
              key="step3"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-green-100 rounded-full mb-4">
                    <DollarSign className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">
                    Fund Your Goals
                  </h2>
                  <p className="text-gray-600 max-w-2xl mx-auto">
                    Allocate your available savings and set monthly contributions for each goal.
                  </p>
                </div>

                {/* Available Funds */}
                <Card className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 mb-8">
                  <div className="flex items-center gap-2 mb-4">
                    <DollarSign className="h-5 w-5 text-green-600" />
                    <h3 className="font-bold text-gray-800">Available Funds</h3>
                  </div>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Available Cash Savings</p>
                      <p className="text-2xl font-bold text-green-600">{formatAmount(financialData.availableCashSavings)}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600 mb-1">Available Monthly Income</p>
                      <p className="text-2xl font-bold text-blue-600">{formatAmount(financialData.availableFromIncome)}</p>
                    </div>
                  </div>
                </Card>

                {/* Allocate Funds to Goals */}
                <div className="space-y-6">
                  <h3 className="font-bold text-gray-800">Allocate Funds to Your Goals</h3>
                  
                  {goals.map((goal, index) => {
                    const allocation = goalAllocations[goal.id] || { fromCashSavings: 0, monthlyContribution: 0 };
                    const remainingAmount = goal.targetAmount - allocation.fromCashSavings;
                    const requiredMonthly = goal.dueDate 
                      ? calculateRequiredMonthlyContribution(goal.targetAmount, allocation.fromCashSavings, goal.dueDate)
                      : 0;
                    
                    return (
                      <Card key={goal.id} className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
                        <div className="flex items-start gap-4 mb-4">
                          <div className="w-16 h-16 rounded-lg bg-blue-100 flex items-center justify-center overflow-hidden flex-shrink-0">
                            {goal.imageUrl ? (
                              <img src={goal.imageUrl} alt={goal.name} className="w-full h-full object-cover" />
                            ) : (
                              <Target className="h-8 w-8 text-blue-600" />
                            )}
                          </div>
                          
                          <div className="flex-1">
                            <h4 className="font-bold text-gray-800 mb-1">{goal.name}</h4>
                            <p className="text-sm text-gray-600">Target: {formatAmount(goal.targetAmount)}</p>
                            
                            <div className="grid md:grid-cols-2 gap-6 mt-4">
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                  Jump-start from Cash Savings
                                </label>
                                <Input
                                  type="number"
                                  min="0"
                                  max={financialData.availableCashSavings}
                                  value={allocation.fromCashSavings || ''}
                                  onChange={(e) => {
                                    const value = parseFloat(e.target.value) || 0;
                                    handleUpdateGoalAllocation(goal.id, 'fromCashSavings', value);
                                  }}
                                  placeholder="0"
                                />
                              </div>
                              
                              <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                  Monthly Contribution
                                </label>
                                <Input
                                  type="number"
                                  min="0"
                                  value={allocation.monthlyContribution || ''}
                                  onChange={(e) => {
                                    const value = parseFloat(e.target.value) || 0;
                                    handleUpdateGoalAllocation(goal.id, 'monthlyContribution', value);
                                  }}
                                  placeholder="0"
                                />
                              </div>
                            </div>
                            
                            {goal.dueDate && (
                              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                                <p className="text-sm text-blue-800">
                                  <strong>Required monthly:</strong> {formatAmount(requiredMonthly)} to reach target by {formatDate(goal.dueDate)}
                                </p>
                              </div>
                            )}
                          </div>
                        </div>
                      </Card>
                    );
                  })}
                </div>

                {/* Navigation Buttons */}
                <div className="flex justify-between mt-8">
                  <Button
                    variant="outline"
                    onClick={handleBack}
                    className="flex items-center gap-2"
                  >
                    <ArrowLeft size={16} />
                    Back
                  </Button>
                  
                  <Button
                    onClick={handleNext}
                    className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                    disabled={goals.length === 0}
                  >
                    Continue
                    <ArrowRight size={16} />
                  </Button>
                </div>
              </Card>
            </motion.div>
          )}

          {/* Step 2: Create Goals */}
          {currentStep === 2 && (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="flex justify-between items-center mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">Create Your Savings Goals</h2>
                    <p className="text-gray-600">
                      Add goals that inspire you. Give each one a name, target amount, and optional deadline.
                    </p>
                  </div>
                  {goals.length > 0 && !showAddGoal && (
                    <Dialog open={showAddGoalDialog} onOpenChange={setShowAddGoalDialog}>
                      <DialogTrigger asChild>
                        <Button className="bg-blue-600 hover:bg-blue-700">
                          <Plus size={16} className="mr-2" />
                          Add Goal
                        </Button>
                      </DialogTrigger>
                    </Dialog>
                  )}
                </div>

                {/* Existing Goals */}
                <div className="space-y-4 mb-6">
                  {goals.length > 0 && (
                    goals.map((goal) => (
                      <Card key={goal.id} className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
                        <div className="flex items-start gap-4">
                          <div className="w-16 h-16 rounded-lg bg-blue-100 flex items-center justify-center overflow-hidden flex-shrink-0">
                            {goal.imageUrl ? (
                              <img src={goal.imageUrl} alt={goal.name} className="w-full h-full object-cover" />
                            ) : (
                              <Target className="h-8 w-8 text-blue-600" />
                            )}
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-start justify-between mb-2">
                              <div>
                                <h3 className="font-bold text-gray-800 mb-1">{goal.name}</h3>
                                <div className="flex items-center gap-4 text-sm text-gray-600">
                                  <span>Target: {formatAmount(goal.targetAmount)}</span>
                                  {goal.dueDate && (
                                    <span>Due: {formatDate(goal.dueDate)}</span>
                                  )}
                                </div>
                              </div>
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleRemoveGoal(goal.id)}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <Trash2 size={16} />
                              </Button>
                            </div>
                            
                            <div className="space-y-2">
                              <div className="flex justify-between text-sm">
                                <span className="text-gray-600">Progress</span>
                                <span className="font-medium">
                                  {formatAmount(goal.currentAmount)} / {formatAmount(goal.targetAmount)}
                                </span>
                              </div>
                              <Progress value={(goal.currentAmount / goal.targetAmount) * 100} className="h-2" />
                              <div className="flex justify-between text-xs text-gray-500">
                                <span>{Math.round((goal.currentAmount / goal.targetAmount) * 100)}% complete</span>
                                <span>
                                  {goal.monthlyContribution > 0 && 
                                    `${formatAmount(goal.monthlyContribution)}/month`
                                  }
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))
                  )}
                  
                  {/* Empty State - only show if no goals and form is not visible */}
                  {goals.length === 0 && !showAddGoal && (
                    <Card className="p-8 text-center bg-gray-50">
                      <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-4">No goals created yet</p>
                      <Button 
                        onClick={() => setShowAddGoal(true)}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        Create Your First Goal
                      </Button>
                    </Card>
                  )}
                </div>

                {/* Add Goal Form - Show by default on first visit or when clicked */}
                {showAddGoal && (
                  <Card className="p-6 bg-gradient-to-r from-indigo-50 to-blue-50 border-indigo-200 mb-6">
                    <h3 className="font-bold text-gray-800 mb-4">Add New Savings Goal</h3>
                    <div className="space-y-4">
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Goal Name *
                          </label>
                          <Input
                            placeholder="e.g., Dream Vacation to Japan"
                            value={newGoal.name}
                            onChange={(e) => setNewGoal({...newGoal, name: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Target Amount *
                          </label>
                          <Input
                            type="number"
                            placeholder="5000"
                            value={newGoal.targetAmount}
                            onChange={(e) => setNewGoal({...newGoal, targetAmount: e.target.value})}
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Target Date (Optional)
                        </label>
                        <Input
                          type="date"
                          placeholder="dd/mm/yyyy"
                          value={newGoal.dueDate}
                          onChange={(e) => setNewGoal({...newGoal, dueDate: e.target.value})}
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          If set, we'll calculate how much you need to save monthly
                        </p>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Add Inspiration Image (Optional)
                        </label>
                        <div className="flex gap-2">
                          <Input
                            placeholder="e.g., Tokyo cherry blossoms"
                            value={newGoal.imageSearchQuery}
                            onChange={(e) => setNewGoal({...newGoal, imageSearchQuery: e.target.value})}
                          />
                          <Button 
                            variant="outline"
                            onClick={handleSearchImage}
                            disabled={isLoadingImage || !newGoal.imageSearchQuery.trim()}
                          >
                            {isLoadingImage ? (
                              <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
                            ) : (
                              <Camera size={16} />
                            )}
                          </Button>
                        </div>
                        {newGoal.imageUrl && (
                          <div className="mt-2">
                            <img 
                              src={newGoal.imageUrl} 
                              alt="Goal inspiration" 
                              className="w-20 h-20 rounded-lg object-cover"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex gap-3 mt-6">
                      <Button 
                        onClick={handleAddGoal}
                        disabled={!newGoal.name || !newGoal.targetAmount}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        Add Goal
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => setShowAddGoal(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </Card>
                )}

                {/* Add Goal Dialog */}
                <Dialog open={showAddGoalDialog} onOpenChange={setShowAddGoalDialog}>
                  <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle className="text-xl font-bold text-gray-800">Add New Savings Goal</DialogTitle>
                    </DialogHeader>
                    
                    <div className="space-y-4 py-4">
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Goal Name *
                          </label>
                          <Input
                            placeholder="e.g., Dream Vacation to Japan"
                            value={newGoal.name}
                            onChange={(e) => setNewGoal({...newGoal, name: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Target Amount *
                          </label>
                          <Input
                            type="number"
                            placeholder="5000"
                            value={newGoal.targetAmount}
                            onChange={(e) => setNewGoal({...newGoal, targetAmount: e.target.value})}
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Target Date (Optional)
                        </label>
                        <Input
                          type="date"
                          placeholder="dd/mm/yyyy"
                          value={newGoal.dueDate}
                          onChange={(e) => setNewGoal({...newGoal, dueDate: e.target.value})}
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          If set, we'll calculate how much you need to save monthly
                        </p>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Add Inspiration Image (Optional)
                        </label>
                        <div className="flex gap-2">
                          <Input
                            placeholder="e.g., Tokyo cherry blossoms"
                            value={newGoal.imageSearchQuery}
                            onChange={(e) => setNewGoal({...newGoal, imageSearchQuery: e.target.value})}
                          />
                          <Button 
                            variant="outline"
                            onClick={handleSearchImage}
                            disabled={isLoadingImage || !newGoal.imageSearchQuery.trim()}
                          >
                            {isLoadingImage ? (
                              <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin" />
                            ) : (
                              <Camera size={16} />
                            )}
                          </Button>
                        </div>
                        {newGoal.imageUrl && (
                          <div className="mt-2">
                            <img 
                              src={newGoal.imageUrl} 
                              alt="Goal inspiration" 
                              className="w-20 h-20 rounded-lg object-cover"
                            />
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <DialogFooter>
                      <Button 
                        variant="outline"
                        onClick={() => setShowAddGoalDialog(false)}
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={handleAddGoal}
                        disabled={!newGoal.name || !newGoal.targetAmount}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        Add Goal
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                {/* Summary */}
                {goals.length > 0 && (
                  <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                    <div className="grid md:grid-cols-3 gap-6 text-center">
                      <div>
                        <p className="text-sm text-gray-600">Total Target</p>
                        <p className="text-2xl font-bold text-blue-600">{formatAmount(totalTargetAmount)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Currently Saved</p>
                        <p className="text-2xl font-bold text-green-600">{formatAmount(totalSavedAmount)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Monthly Contribution</p>
                        <p className="text-2xl font-bold text-purple-600">{formatAmount(totalMonthlyContribution)}</p>
                      </div>
                    </div>
                  </Card>
                )}
              </Card>
            </motion.div>
          )}

          

          {/* Step 4: Summary */}
          {currentStep === 4 && (
            <motion.div
              key="step4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-green-100 rounded-full mb-4">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Your Savings Goals Plan</h2>
                  <p className="text-gray-600">
                    Review your goals and start building your future, one dollar at a time.
                  </p>
                </div>

                <div className="space-y-6">
                  {/* Goals Summary */}
                  {goals.length > 0 ? (
                    <>
                      <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                        <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                          <Target className="h-5 w-5 text-blue-600" />
                          Goals Overview
                        </h3>
                        <div className="grid md:grid-cols-3 gap-4 text-center">
                          <div>
                            <p className="text-sm text-gray-600">Total Target Amount</p>
                            <p className="text-2xl font-bold text-blue-600">{formatAmount(totalTargetAmount)}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Currently Saved</p>
                            <p className="text-2xl font-bold text-green-600">{formatAmount(totalSavedAmount)}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">Monthly Contributions</p>
                            <p className="text-2xl font-bold text-purple-600">{formatAmount(totalMonthlyContribution)}</p>
                          </div>
                        </div>
                      </Card>

                      {/* Individual Goals */}
                      <div className="space-y-4">
                        <h3 className="font-bold text-gray-800">Your Goals Timeline</h3>
                        {goals.map((goal) => (
                          <Card key={goal.id} className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
                            <div className="flex items-start gap-4">
                              <div className="w-16 h-16 rounded-lg bg-green-100 flex items-center justify-center overflow-hidden flex-shrink-0">
                                {goal.imageUrl ? (
                                  <img src={goal.imageUrl} alt={goal.name} className="w-full h-full object-cover" />
                                ) : (
                                  <Target className="h-8 w-8 text-green-600" />
                                )}
                              </div>
                              
                              <div className="flex-1">
                                <div className="flex justify-between items-start mb-3">
                                  <div>
                                    <h4 className="font-bold text-gray-800">{goal.name}</h4>
                                    <p className="text-sm text-gray-600">
                                      {formatAmount(goal.currentAmount)} / {formatAmount(goal.targetAmount)}
                                    </p>
                                  </div>
                                  <div className="text-right">
                                    <p className="text-sm text-gray-600">Target Date</p>
                                    <p className="font-bold text-green-600">
                                      {goal.dueDate ? formatDate(goal.dueDate) : formatDate(goal.estimatedCompletionDate)}
                                    </p>
                                  </div>
                                </div>
                                
                                <Progress value={(goal.currentAmount / goal.targetAmount) * 100} className="h-3 mb-2" />
                                
                                <div className="flex justify-between text-sm text-gray-600">
                                  <span>{Math.round((goal.currentAmount / goal.targetAmount) * 100)}% complete</span>
                                  <span>{formatAmount(goal.monthlyContribution)}/month</span>
                                </div>
                              </div>
                            </div>
                          </Card>
                        ))}
                      </div>
                    </>
                  ) : (
                    <Card className="p-8 text-center bg-gray-50">
                      <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">No goals created yet. Your future self will thank you for starting today!</p>
                    </Card>
                  )}

                  {/* Next Steps */}
                  <Alert className="border-blue-200 bg-blue-50">
                    <Info className="h-4 w-4 text-blue-600" />
                    <AlertDescription className="text-blue-800">
                      <strong>Next Steps:</strong> Set up automatic transfers to dedicated savings accounts for each goal. Review progress monthly and celebrate milestones! 🎉
                    </AlertDescription>
                  </Alert>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8">
          <Button
            variant="outline"
            onClick={handleBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {currentStep === 1 ? 'Back to Dashboard' : 'Back'}
          </Button>

          <div className="flex items-center gap-4">
            {currentStep < 4 ? (
              <Button
                onClick={handleNext}
                disabled={currentStep === 2 && goals.length === 0}
                className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
              >
                Continue
                <ArrowRight className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={handleComplete}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              >
                Complete Savings Goals
                <CheckCircle className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}