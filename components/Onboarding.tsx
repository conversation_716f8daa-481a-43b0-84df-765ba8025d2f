import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { ChevronRight, ChevronLeft, Sparkles, Target, Heart, Home, CreditCard, TrendingUp, DollarSign, Shield, Car, PiggyBank, Zap } from "lucide-react";
import { Progress } from "./ui/progress";
import { Button } from "./ui/button";
import { Card } from "./ui/card";

interface Question {
  id: string;
  title: string;
  subtitle: string;
  type: 'single' | 'multiple';
  options: {
    value: string;
    label: string;
    description?: string;
    icon?: React.ReactNode;
    emoji?: string;
  }[];
}

const questions: Question[] = [
  {
    id: 'motivation',
    title: "What brings you to your financial future?",
    subtitle: "Let's understand what sparked this amazing decision!",
    type: 'single',
    options: [
      {
        value: 'more_from_money',
        label: "I want to get more from my money",
        description: "Turn every dollar into multiple dollars",
        icon: <TrendingUp className="text-green-500" size={24} />,
        // emoji: "📈"
      },
      {
        value: 'financial_security',
        label: "I want to build my financial security",
        description: "Sleep better knowing you're covered",
        icon: <Shield className="text-blue-500" size={24} />,
       // emoji: "🛡️"
      },
      {
        value: 'knowledge_confidence',
        label: "I want to improve my financial knowledge and confidence",
        description: "Become the financial guru you've always wanted to be",
        icon: <Sparkles className="text-purple-500" size={24} />,
        // emoji: "🧠"
      }
    ]
  },
  {
    id: 'current_state',
    title: "Where are you on your money journey?",
    subtitle: "No judgment here - we're all learning! Every expert was once a beginner",
    type: 'single',
    options: [
      {
        value: 'optimizing',
        label: "I'm already managing well, let's optimize!",
        description: "Ready to level up from good to great",
        icon: <Target className="text-orange-500" size={24} />,
        // emoji: "🎯"
      },
      {
        value: 'starting_out',
        label: "I'm just starting and could use guidance",
        description: "Perfect! The best time to start was yesterday, second best is now",
        icon: <Sparkles className="text-green-500" size={24} />,
        // emoji: "🚀"
      },
      {
        value: 'overwhelmed',
        label: "I don't know where to start!",
        description: "That's exactly why we're here - let's turn confusion into clarity",
        icon: <Heart className="text-pink-500" size={24} />,
        // emoji: "🤗"
      }
    ]
  },
  {
    id: 'financial_drivers',
    title: "What's your 'why'? What drives you financially?",
    subtitle: "Dream big! Whether it's a family, a Ferrari, or both - we're here for it",
    type: 'multiple',
    options: [
      {
        value: 'security',
        label: "Feeling financially secure",
        description: "That peaceful sleep knowing you're covered",
        icon: <Shield className="text-blue-500" size={20} />,
        // emoji: "🛡️"
      },
      {
        value: 'lifestyle',
        label: "A dream lifestyle",
        description: "Living life on your own terms",
        icon: <Car className="text-red-500" size={20} />,
        // emoji: "🌟"
      },
      {
        value: 'work_balance',
        label: "Work-life balance",
        description: "Work to live, not live to work",
        icon: <Heart className="text-pink-500" size={20} />,
        // emoji: "⚖️"
      },
      {
        value: 'dream_home',
        label: "Buying a dream home",
        description: "Your castle awaits",
        icon: <Home className="text-green-500" size={20} />,
        // emoji: "🏡"
      },
      {
        value: 'debt_free',
        label: "Paying down debt",
        description: "Freedom from financial stress",
        icon: <CreditCard className="text-purple-500" size={20} />,
        // emoji: "💳"
      }
    ]
  },
  {
    id: 'biggest_challenge',
    title: "What's your biggest money challenge right now?",
    subtitle: "Let's tackle this together - every challenge is just a solution waiting to happen!",
    type: 'multiple',
    options: [
      {
        value: 'overspending',
        label: "Spending more than I earn",
        description: "Let's get that balance back on track",
        icon: <CreditCard className="text-red-500" size={20} />,
        // emoji: "💸"
      },
      {
        value: 'unexpected_costs',
        label: "Unexpected costs",
        description: "Build that safety net",
        icon: <Zap className="text-yellow-500" size={20} />,
        // emoji: "⚡"
      },
      {
        value: 'debt',
        label: "Getting out of debt",
        description: "Your path to freedom starts here",
        icon: <Target className="text-purple-500" size={20} />,
        // emoji: "🎯"
      },
      {
        value: 'savings_dipping',
        label: "Dipping into savings",
        description: "Let's protect that nest egg",
        icon: <PiggyBank className="text-pink-500" size={20} />,
        // emoji: "🐷"
      },
      {
        value: 'not_investing',
        label: "Not investing",
        description: "Time to make your money work for you",
        icon: <TrendingUp className="text-green-500" size={20} />,
        // emoji: "📈"
      },
      {
        value: 'something_else',
        label: "Something else",
        description: "We'll figure it out together",
        icon: <Sparkles className="text-blue-500" size={20} />,
        // emoji: "✨"
      }
    ]
  },
  {
    id: 'skills_to_master',
    title: "What financial superpowers do you want to master?",
    subtitle: "Choose your adventure! We'll create your personalized roadmap to success",
    type: 'multiple',
    options: [
      {
        value: 'pay_management',
        label: "Managing my pay to spend less",
        description: "Master the art of conscious spending",
        icon: <DollarSign className="text-green-500" size={20} />,
        // emoji: "💰"
      },
      {
        value: 'emergency_fund',
        label: "Building an emergency fund",
        description: "Your financial safety net",
        icon: <Shield className="text-blue-500" size={20} />,
        // emoji: "🛡️"
      },
      {
        value: 'debt_elimination',
        label: "Getting out of debt fast",
        description: "Accelerate your path to freedom",
        icon: <Zap className="text-yellow-500" size={20} />,
        // emoji: "⚡"
      },
      {
        value: 'save_more',
        label: "Saving more each pay",
        description: "Watch your wealth grow automatically",
        icon: <PiggyBank className="text-pink-500" size={20} />,
        // emoji: "📊"
      },
      {
        value: 'investments',
        label: "Starting or improving investments",
        description: "Make your money multiply",
        icon: <TrendingUp className="text-purple-500" size={20} />,
        // emoji: "📈"
      },
      {
        value: 'custom',
        label: "Something specific to me",
        description: "We'll create a custom plan just for you",
        icon: <Sparkles className="text-orange-500" size={20} />,
        // emoji: "✨"
      }
    ]
  }
];

function OptionCard({ 
  option, 
  isSelected, 
  onSelect, 
  type 
}: { 
  option: Question['options'][0]; 
  isSelected: boolean; 
  onSelect: () => void;
  type: 'single' | 'multiple';
}) {
  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card 
        className={`p-4 cursor-pointer transition-all duration-200 border-2 ${
          isSelected 
            ? 'border-purple-500 bg-purple-50 shadow-lg' 
            : 'border-gray-200 hover:border-purple-300 hover:shadow-md'
        }`}
        onClick={onSelect}
      >
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0 mt-1">
            {option.emoji && (
              <span className="text-2xl">{option.emoji}</span>
            )}
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2 mb-1">
              <h3 className={`font-semibold ${isSelected ? 'text-purple-700' : 'text-gray-800'}`}>
                {option.label}
              </h3>
              {type === 'multiple' && (
                <div className={`w-5 h-5 rounded border-2 flex items-center justify-center ${
                  isSelected ? 'bg-purple-500 border-purple-500' : 'border-gray-300'
                }`}>
                  {isSelected && <div className="w-2 h-2 bg-white rounded-sm" />}
                </div>
              )}
            </div>
            {option.description && (
              <p className={`text-sm ${isSelected ? 'text-purple-600' : 'text-gray-600'}`}>
                {option.description}
              </p>
            )}
          </div>
        </div>
      </Card>
    </motion.div>
  );
}

export function Onboarding({ onComplete }: { onComplete: () => void }) {
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState<Record<string, string[]>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const currentQuestion = questions[currentStep];
  const currentAnswers = answers[currentQuestion.id] || [];
  const progress = ((currentStep + 1) / questions.length) * 100;

  const handleOptionSelect = (optionValue: string) => {
    const questionId = currentQuestion.id;
    
    if (currentQuestion.type === 'single') {
      setAnswers(prev => ({ ...prev, [questionId]: [optionValue] }));
      // Auto-advance for single select after a brief delay
      setTimeout(() => {
        if (currentStep < questions.length - 1) {
          setCurrentStep(prev => prev + 1);
        }
      }, 800);
    } else {
      setAnswers(prev => {
        const currentAnswers = prev[questionId] || [];
        const isSelected = currentAnswers.includes(optionValue);
        
        if (isSelected) {
          return { ...prev, [questionId]: currentAnswers.filter(val => val !== optionValue) };
        } else {
          return { ...prev, [questionId]: [...currentAnswers, optionValue] };
        }
      });
    }
  };

  const canProceed = currentAnswers.length > 0;

  const handleNext = () => {
    if (currentStep < questions.length - 1) {
      setCurrentStep(prev => prev + 1);
    } else {
      handleSubmit();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    // Simulate processing
    await new Promise(resolve => setTimeout(resolve, 2000));
    setIsSubmitting(false);
    onComplete();
  };

  const getEncouragementMessage = () => {
    const messages = [
      "This is where your wealth story begins!",
      "You're already making smart choices!", 
      "Your financial confidence is growing!",
      "We're building something amazing together!",
      "Almost there - your personalized plan awaits!"
    ];
    return messages[currentStep] || messages[0];
  };

  const getStepTitle = () => {
    const titles = [
      "Step 1: Your Financial Why",
      "Step 2: Where You Are Now", 
      "Step 3: Your Dreams & Goals",
      "Step 4: Current Challenges",
      "Step 5: Skills to Master"
    ];
    return titles[currentStep] || "";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-blue-50 to-orange-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center gap-2 bg-purple-100 text-purple-700 px-4 py-2 rounded-full text-sm font-medium mb-4">
            {/* <Sparkles size={16} /> */}
            {getStepTitle()}
          </div>
          <h1 className="text-3xl font-bold text-purple-600 mb-2">
            Let's personalize your wealth journey! 
          </h1>
          <p className="text-gray-600 text-lg">
            {getEncouragementMessage()}
          </p>
        </motion.div>

        {/* Progress Bar */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-8"
        >
          <Progress value={progress} className="h-3 bg-gray-200" />
          <div className="flex justify-between mt-2 text-sm text-gray-500">
            <span>Getting started</span>
            <span>Ready to build wealth!</span>
          </div>
        </motion.div>

        {/* Question */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.4 }}
            className="bg-white rounded-2xl shadow-xl p-8 mb-8"
          >
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-gray-800 mb-3">
                {currentQuestion.title}
              </h2>
              <p className="text-gray-600 text-lg">
                {currentQuestion.subtitle}
              </p>
            </div>

            <div className="space-y-3">
              {currentQuestion.options.map((option, index) => (
                <motion.div
                  key={option.value}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <OptionCard
                    option={option}
                    isSelected={currentAnswers.includes(option.value)}
                    onSelect={() => handleOptionSelect(option.value)}
                    type={currentQuestion.type}
                  />
                </motion.div>
              ))}
            </div>

            {currentQuestion.type === 'multiple' && currentAnswers.length > 0 && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="mt-6 p-4 bg-gradient-to-r from-purple-50 to-blue-50 rounded-xl text-center border border-purple-200"
              >
                <div className="flex items-center justify-center gap-2">
                  <div className="flex gap-1">
                    {[...Array(Math.min(currentAnswers.length, 5))].map((_, i) => (
                      <motion.span
                        key={i}
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ delay: i * 0.1 }}
                        className="text-lg"
                      >
                        ⭐
                      </motion.span>
                    ))}
                  </div>
                  <p className="text-purple-700 font-semibold">
                    {currentAnswers.length === 1 
                      ? 'Perfect choice!' 
                      : currentAnswers.length === 2
                      ? 'Great combination!'
                      : `${currentAnswers.length} selections - you're really thinking this through!`
                    } 
                  </p>
                </div>
                <p className="text-purple-600 text-sm mt-1">
                  The more we know, the better we can help you succeed!
                </p>
              </motion.div>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-between items-center"
        >
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 0}
            className="flex items-center gap-2"
          >
            <ChevronLeft size={20} />
            Previous
          </Button>

          <div className="flex gap-2">
            {questions.map((_, index) => (
              <div
                key={index}
                className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                  index === currentStep
                    ? 'bg-purple-500'
                    : index < currentStep
                    ? 'bg-green-500'
                    : 'bg-gray-300'
                }`}
              />
            ))}
          </div>

          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => onComplete()}
              className="flex items-center gap-2 text-purple-600 border-purple-300 hover:bg-purple-50"
            >
              Skip - Let's go!
            </Button>
            <Button
              onClick={handleNext}
              disabled={!canProceed || isSubmitting}
              className="flex items-center gap-2 bg-purple-600 hover:bg-purple-700"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  Creating Your Plan...
                </>
              ) : currentStep === questions.length - 1 ? (
                <>
                  Complete Setup
                </>
              ) : (
                <>
                  Next
                  <ChevronRight size={20} />
                </>
              )}
            </Button>
          </div>
        </motion.div>


      </div>
    </div>
  );
}