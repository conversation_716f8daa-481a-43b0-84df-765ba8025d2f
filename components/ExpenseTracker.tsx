import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { 
  Plus, 
  Home, 
  Car, 
  ShoppingCart, 
  Zap, 
  Heart, 
  Plane, 
  GamepadIcon, 
  Shirt,
  ChevronRight,
  CheckCircle,
  PartyPopper
} from "lucide-react";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import obieLogo from 'figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png';

interface Expense {
  id: string;
  name: string;
  amount: number;
  category: string;
  frequency: 'weekly' | 'monthly' | 'yearly';
  icon: string;
  color: string;
}

interface SavingsInvestment {
  amount: number;
  frequency: 'weekly' | 'monthly' | 'yearly';
}

interface ExpenseCategory {
  id: string;
  name: string;
  icon: React.ReactNode;
  color: string;
  gradient: string;
  examples: string[];
}

const expenseCategories: ExpenseCategory[] = [
  {
    id: 'housing',
    name: 'Housing',
    icon: <Home size={24} />,
    color: '#FF6B6B',
    gradient: 'from-red-400 to-red-600',
    examples: ['Rent', 'Mortgage', 'Property tax', 'Home insurance']
  },
  {
    id: 'transportation',
    name: 'Transport',
    icon: <Car size={24} />,
    color: '#4ECDC4',
    gradient: 'from-teal-400 to-teal-600',
    examples: ['Gas', 'Car payment', 'Insurance', 'Uber/Taxi']
  },
  {
    id: 'food',
    name: 'Food & Dining',
    icon: <ShoppingCart size={24} />,
    color: '#45B7D1',
    gradient: 'from-blue-400 to-blue-600',
    examples: ['Groceries', 'Restaurants', 'Coffee', 'Snacks']
  },
  {
    id: 'utilities',
    name: 'Utilities',
    icon: <Zap size={24} />,
    color: '#F9CA24',
    gradient: 'from-yellow-400 to-orange-500',
    examples: ['Electricity', 'Water', 'Internet', 'Phone']
  },
  {
    id: 'healthcare',
    name: 'Healthcare',
    icon: <Heart size={24} />,
    color: '#6C5CE7',
    gradient: 'from-purple-400 to-purple-600',
    examples: ['Insurance', 'Doctor visits', 'Medications', 'Dental']
  },
  {
    id: 'entertainment',
    name: 'Entertainment',
    icon: <GamepadIcon size={24} />,
    color: '#FD79A8',
    gradient: 'from-pink-400 to-pink-600',
    examples: ['Netflix', 'Movies', 'Games', 'Hobbies']
  },
  {
    id: 'shopping',
    name: 'Shopping',
    icon: <Shirt size={24} />,
    color: '#00B894',
    gradient: 'from-green-400 to-green-600',
    examples: ['Clothes', 'Electronics', 'Personal items', 'Gifts']
  },
  {
    id: 'travel',
    name: 'Travel',
    icon: <Plane size={24} />,
    color: '#A29BFE',
    gradient: 'from-indigo-400 to-indigo-600',
    examples: ['Flights', 'Hotels', 'Vacation', 'Weekend trips']
  }
];



function CategoryCard({ 
  category, 
  isSelected, 
  onSelect,
  count
}: { 
  category: ExpenseCategory; 
  isSelected: boolean; 
  onSelect: () => void;
  count: number;
}) {
  return (
    <motion.div
      whileHover={{ scale: 1.05, y: -8 }}
      whileTap={{ scale: 0.95 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card 
        className={`relative p-6 cursor-pointer transition-all duration-300 border-2 overflow-hidden ${
          isSelected 
            ? 'border-purple-500 shadow-2xl transform scale-105' 
            : 'border-gray-200 hover:border-purple-300 hover:shadow-xl'
        }`}
        onClick={onSelect}
        style={{
          background: isSelected 
            ? `linear-gradient(135deg, ${category.color}15, ${category.color}25)` 
            : 'white'
        }}
      >
        <div className="relative z-10">
          <div className="flex items-center justify-between mb-4">
            <div 
              className="p-3 rounded-2xl text-white shadow-lg"
              style={{
                background: `linear-gradient(135deg, ${category.color}, ${category.color}CC)`
              }}
            >
              {category.icon}
            </div>
            {count > 0 && (
              <div className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-semibold">
                {count} added
              </div>
            )}
          </div>
          
          <h3 className="font-semibold text-gray-800 mb-2">{category.name}</h3>
          
          <div className="text-xs text-gray-500 space-y-1">
            {category.examples.slice(0, 2).map((example, index) => (
              <div key={index} className="flex items-center gap-1">
                <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
                <span>{example}</span>
              </div>
            ))}
          </div>
        </div>
        
        {/* Background decoration */}
        <div 
          className="absolute -top-6 -right-6 w-20 h-20 rounded-full opacity-20"
          style={{ backgroundColor: category.color }}
        />
      </Card>
    </motion.div>
  );
}




interface ExpenseTrackerProps {
  onComplete: (data: { expenses: Expense[] }) => void;
  initialData?: { expenses: Expense[] };
  onBack?: () => void;
}

export function ExpenseTracker({ onComplete, initialData, onBack }: ExpenseTrackerProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [expenses, setExpenses] = useState<Expense[]>(initialData?.expenses || []);
  const [newExpense, setNewExpense] = useState({
    name: '',
    amount: '',
    frequency: 'monthly' as const
  });
  const [showCelebration, setShowCelebration] = useState(false);

  const addExpense = (expense: Partial<Expense>) => {
    const id = Date.now().toString();
    const category = expenseCategories.find(cat => cat.id === selectedCategory);
    
    const newExp: Expense = {
      id,
      name: expense.name || newExpense.name,
      amount: expense.amount || parseFloat(newExpense.amount),
      category: selectedCategory,
      frequency: expense.frequency || newExpense.frequency,
      icon: category?.examples[0] || '💰',
      color: category?.color || '#6C5CE7'
    };

    setExpenses(prev => [...prev, newExp]);
    setNewExpense({ name: '', amount: '', frequency: 'monthly' });
    
    // Show celebration
    setShowCelebration(true);
    setTimeout(() => setShowCelebration(false), 2000);
  };



  const getCategoryCount = (categoryId: string) => {
    return expenses.filter(exp => exp.category === categoryId).length;
  };

  const canProceed = expenses.length >= 3;

  return (
    <div className="min-h-screen py-8" style={{ backgroundColor: '#F8FAFC' }}>
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex justify-center mb-6">
            <img 
              src={obieLogo} 
              alt="Obiemoney Logo" 
              className="h-10 w-auto"
            />
          </div>
          
          <div className="flex justify-between items-center mb-6">
            <span className="px-4 py-2 rounded-full text-sm font-medium" style={{ 
              backgroundColor: '#A37AF9' + '20', 
              color: '#A37AF9' 
            }}>
              Stage 1: Optimise • Budget
            </span>
            <div className="flex gap-2">
              { /*{onBack && (
                <Button
                  variant="outline"
                  onClick={onBack}
                  className="flex items-center gap-2 text-gray-600 border-gray-300 hover:bg-gray-50"
                >
                  ← Back to Dashboard
                </Button>
              )} */}
              <Button
                variant="outline"
                onClick={() => onComplete({ expenses: [] })}
                className="flex items-center gap-2 text-purple-600 border-purple-300 hover:bg-purple-50"
              >
                Skip All Expenses
              </Button>
            </div>
          </div>

          <h1 className="text-4xl font-bold mb-4" style={{ color: '#A37AF9' }}>
            Let's track your expenses!
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-6">
            Don't worry, this is actually fun! We'll help you understand where your money goes 
            so you can make it work harder for you.
          </p>

        </motion.div>



        {/* Category Selection */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <div className="bg-white rounded-2xl p-6 shadow-sm">
            <h3 className="text-lg font-semibold text-gray-800 mb-6">
              Choose a category to add your expenses
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {expenseCategories.map((category, index) => (
                <motion.div
                  key={category.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                >
                  <CategoryCard
                    category={category}
                    isSelected={selectedCategory === category.id}
                    onSelect={() => setSelectedCategory(category.id)}
                    count={getCategoryCount(category.id)}
                  />
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>

        {/* Add Expense Form */}
        <AnimatePresence>
          {selectedCategory && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-8"
            >
              <div className="bg-white rounded-2xl p-6 shadow-sm">
                <div className="flex items-center gap-3 mb-6">
                  {expenseCategories.find(cat => cat.id === selectedCategory)?.icon}
                  <h3 className="text-lg font-semibold text-gray-800">
                    Add {expenseCategories.find(cat => cat.id === selectedCategory)?.name} Expense
                  </h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                  <Input
                    placeholder="Expense name"
                    value={newExpense.name}
                    onChange={(e) => setNewExpense(prev => ({ ...prev, name: e.target.value }))}
                    className="border-2 focus:border-purple-500"
                  />
                  <Input
                    type="number"
                    placeholder="Amount ($)"
                    value={newExpense.amount}
                    onChange={(e) => setNewExpense(prev => ({ ...prev, amount: e.target.value }))}
                    className="border-2 focus:border-purple-500"
                  />
                  <Select 
                    value={newExpense.frequency} 
                    onValueChange={(value: 'weekly' | 'monthly' | 'yearly') => 
                      setNewExpense(prev => ({ ...prev, frequency: value }))
                    }
                  >
                    <SelectTrigger className="border-2 focus:border-purple-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="yearly">Yearly</SelectItem>
                    </SelectContent>
                  </Select>
                  <Button
                    onClick={() => addExpense({})}
                    disabled={!newExpense.name || !newExpense.amount}
                    className="bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600"
                  >
                    <Plus size={20} className="mr-2" />
                    Add Expense
                  </Button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Added Expenses List */}
        {expenses.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <div className="bg-white rounded-2xl p-6 shadow-sm">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">Your Expenses ({expenses.length})</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {expenses.map((expense, index) => (
                  <motion.div
                    key={expense.id}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.05 }}
                    className="p-4 border-2 border-gray-100 rounded-xl"
                  >
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-10 h-10 rounded-full flex items-center justify-center text-white text-lg"
                        style={{ backgroundColor: expense.color }}
                      >
                        {expense.icon}
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-800">{expense.name}</p>
                        <p className="text-sm text-gray-500">
                          ${expense.amount.toLocaleString()} • {expense.frequency}
                        </p>
                      </div>
                      <CheckCircle size={16} className="text-green-500" />
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        )}



        {/* Continue Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-center"
        >
          <Button
            onClick={() => onComplete({ expenses })}
            disabled={!canProceed}
            className={`px-12 py-4 text-lg font-semibold rounded-2xl transition-all duration-300 ${
              canProceed 
                ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 shadow-lg hover:shadow-xl' 
                : 'bg-gray-300 text-gray-500'
            }`}
          >
            {canProceed ? (
              <>
                Continue to Savings & Investment
                <ChevronRight size={20} className="ml-2" />
              </>
            ) : (
              `Add at least 3 expenses to continue (${expenses.length}/3 added)`
            )}
          </Button>
        </motion.div>

        {/* Celebration Animation */}
        <AnimatePresence>
          {showCelebration && (
            <motion.div
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.5 }}
              className="fixed inset-0 flex items-center justify-center pointer-events-none z-50"
            >
              <div className="bg-white rounded-2xl p-8 shadow-2xl text-center">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: [0, 1.2, 1] }}
                  transition={{ duration: 0.5 }}
                >
                  <PartyPopper size={48} className="text-yellow-500 mx-auto mb-4" />
                </motion.div>
                <h3 className="text-xl font-bold text-gray-800 mb-2">Awesome! 🎉</h3>
                <p className="text-gray-600">Expense added successfully!</p>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
}