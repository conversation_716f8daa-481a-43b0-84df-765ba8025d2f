import { useState } from "react";
import { Plus, Home, Car, ShoppingCart, Coffee, Music, Zap, Trash2 } from "lucide-react";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "./ui/dialog";

interface Expense {
  id: string;
  name: string;
  amount: number;
  category: string;
  frequency: 'weekly' | 'monthly' | 'yearly';
  icon: string;
  color: string;
}

interface CurrentMonthExpensesProps {
  expenses: Expense[];
  onUpdateExpenses: (expenses: Expense[]) => void;
}

const expenseIcons = {
  Home: Home,
  Car: Car,
  ShoppingCart: ShoppingCart,
  Coffee: Coffee,
  Music: Music,
  Zap: Zap,
};

const expenseCategories = [
  { name: "Housing", icon: "Home", color: "text-red-500" },
  { name: "Transportation", icon: "Car", color: "text-blue-500" },
  { name: "Food", icon: "ShoppingCart", color: "text-green-500" },
  { name: "Entertainment", icon: "Coffee", color: "text-purple-500" },
  { name: "Subscriptions", icon: "Music", color: "text-pink-500" },
  { name: "Utilities", icon: "Zap", color: "text-yellow-500" },
];

export function CurrentMonthExpenses({ expenses, onUpdateExpenses }: CurrentMonthExpensesProps) {
  const [showAddExpense, setShowAddExpense] = useState(false);
  const [newExpense, setNewExpense] = useState({
    name: "",
    amount: "",
    category: "",
    frequency: "monthly" as 'weekly' | 'monthly' | 'yearly'
  });

  const handleAddExpense = () => {
    if (!newExpense.name || !newExpense.amount || !newExpense.category) return;

    const category = expenseCategories.find(cat => cat.name === newExpense.category);
    if (!category) return;

    const expense: Expense = {
      id: Date.now().toString(),
      name: newExpense.name,
      amount: parseFloat(newExpense.amount),
      category: newExpense.category,
      frequency: newExpense.frequency,
      icon: category.icon,
      color: category.color
    };

    onUpdateExpenses([...expenses, expense]);
    setNewExpense({ name: "", amount: "", category: "", frequency: "monthly" });
    setShowAddExpense(false);
  };

  const handleRemoveExpense = (expenseId: string) => {
    onUpdateExpenses(expenses.filter(expense => expense.id !== expenseId));
  };

  const getMonthlyAmount = (expense: Expense) => {
    let amount = expense.amount;
    if (expense.frequency === 'weekly') amount *= 4.33;
    if (expense.frequency === 'yearly') amount /= 12;
    return amount;
  };

  // Show only current month expenses (filter or show all for demo)
  const currentMonthExpenses = expenses.slice(0, 5); // Show first 5 expenses for demo

  return (
    <Card className="p-6 bg-white">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-bold text-gray-800">Current Month Expenses</h3>
        <Dialog open={showAddExpense} onOpenChange={setShowAddExpense}>
          <DialogTrigger asChild>
            <Button 
              size="sm" 
              className="bg-purple-600 hover:bg-purple-700"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Expense
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Expense</DialogTitle>
              <DialogDescription>
                Add a new expense to track your monthly spending. Fill in the details below.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Expense Name
                </label>
                <Input
                  placeholder="e.g., Rent, Groceries"
                  value={newExpense.name}
                  onChange={(e) => setNewExpense({...newExpense, name: e.target.value})}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount
                </label>
                <Input
                  type="number"
                  placeholder="100"
                  value={newExpense.amount}
                  onChange={(e) => setNewExpense({...newExpense, amount: e.target.value})}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Category
                </label>
                <Select
                  value={newExpense.category}
                  onValueChange={(value) => setNewExpense({...newExpense, category: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {expenseCategories.map(category => (
                      <SelectItem key={category.name} value={category.name}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Frequency
                </label>
                <Select
                  value={newExpense.frequency}
                  onValueChange={(value: 'weekly' | 'monthly' | 'yearly') => setNewExpense({...newExpense, frequency: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="yearly">Yearly</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex gap-3 pt-4">
                <Button 
                  onClick={handleAddExpense}
                  disabled={!newExpense.name || !newExpense.amount || !newExpense.category}
                  className="bg-purple-600 hover:bg-purple-700 flex-1"
                >
                  Add Expense
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => setShowAddExpense(false)}
                >
                  Cancel
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <div className="space-y-3">
        {currentMonthExpenses.map((expense) => {
          const IconComponent = expenseIcons[expense.icon as keyof typeof expenseIcons] || Home;
          const monthlyAmount = getMonthlyAmount(expense);
          
          return (
            <div key={expense.id} className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className={`w-10 h-10 rounded-full bg-red-100 flex items-center justify-center`}>
                  <IconComponent className={`h-5 w-5 ${expense.color}`} />
                </div>
                <div>
                  <p className="font-medium text-gray-800">{expense.name}</p>
                  <p className="text-sm text-gray-500">{expense.frequency}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-bold text-gray-800">
                  ${monthlyAmount.toLocaleString()}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveExpense(expense.id)}
                  className="text-gray-400 hover:text-red-500"
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
          );
        })}

        {currentMonthExpenses.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <ShoppingCart className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p>No expenses tracked yet</p>
            <p className="text-sm">Click "Add Expense" to get started</p>
          </div>
        )}
      </div>
    </Card>
  );
}