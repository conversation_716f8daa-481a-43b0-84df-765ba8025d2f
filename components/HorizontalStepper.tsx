import svgPaths from "../imports/svg-ckccvkfnjv";

export interface StepData {
  id: string;
  name: string;
  status: "completed" | "current" | "future";
}

interface HorizontalStepperProps {
  steps: StepData[];
}

export function HorizontalStepper({ steps }: HorizontalStepperProps) {
  return (
    <div className="flex items-center justify-between w-full">
      {steps.map((step, index) => (
        <div key={step.id} className="flex items-center flex-1">
          {/* Step Circle */}
          <div className="flex flex-col items-center flex-shrink-0">
            <div className={`relative w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 ${
              step.status === "completed" 
                ? "bg-gradient-to-br from-purple-600 to-purple-700 shadow-md" 
                : step.status === "current"
                ? "bg-white border-2 border-purple-600 shadow-md"
                : "bg-gray-200"
            }`}>
              {step.status === "completed" && (
                <svg className="w-4 h-4" fill="none" viewBox="0 0 12 12">
                  <path d={svgPaths.p23738880} fill="white" />
                </svg>
              )}
              {step.status === "current" && (
                <div className="w-3 h-3 bg-gradient-to-br from-purple-600 to-purple-700 rounded-full" />
              )}
            </div>
            
            {/* Step Label */}
            <div className={`mt-2 text-xs font-medium transition-colors duration-300 text-center max-w-[80px] ${
              step.status === "completed" ? "text-gray-900" : 
              step.status === "current" ? "text-purple-600" : 
              "text-gray-400"
            }`}>
              {step.name}
            </div>
          </div>
          
          {/* Connector Line */}
          {index < steps.length - 1 && (
            <div className={`flex-1 h-px mx-4 transition-colors duration-300 ${
              step.status === "completed" ? "bg-purple-300" : "bg-gray-300"
            }`} />
          )}
        </div>
      ))}
    </div>
  );
}