import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "motion/react";
import { 
  Plus, 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  PiggyBank, 
  Target, 
  AlertCircle, 
  CheckCircle, 
  BarChart3, 
  DollarSign,
  Bell,
  Settings,
  Eye,
  EyeOff,
  Shield,
  ArrowRight,
  CreditCard,
  Trash2,
  Star,
  Trophy,
  LogOut,
  LineChart,
  Building,
  Home,
  FileText,
  Users,
  Heart
} from "lucide-react";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "./ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "./ui/alert-dialog";
import { Alert, AlertDescription } from "./ui/alert";
import { Progress } from "./ui/progress";
import { ProgressTabs } from "./ProgressTabs";
import { VerticalStepper } from "./VerticalStepper";
import { CurrentMonthExpenses } from "./CurrentMonthExpenses";
import { CumulativeSavingsPool } from "./CumulativeSavingsPool";
import { FinancialHealthScore } from "./FinancialHealthScore";
import { QuickActions } from "./QuickActions";
import { OPTIMISE_VERTICAL_STEPS, MAXIMISE_VERTICAL_STEPS, PROTECT_VERTICAL_STEPS } from "../constants/steps";
import obieLogo from 'figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png';

interface IncomeData {
  amount: string;
  frequency: 'monthly' | 'weekly' | 'yearly' | '';
}

interface Expense {
  id: string;
  name: string;
  amount: number;
  category: string;
  frequency: 'weekly' | 'monthly' | 'yearly';
  icon: string;
  color: string;
  date?: string;
}

interface SavingsInvestment {
  amount: number;
  frequency: 'weekly' | 'monthly' | 'yearly';
}

interface ExpenseTrackerData {
  expenses: Expense[];
  savings: SavingsInvestment | null;
  investment: SavingsInvestment | null;
}

interface SafetyNetData {
  emergencyFund: number;
  bigExpenses: number;
  threeMonthBuffer: number;
  totalTarget: number;
  currentAmount: number;
  monthlyContribution: number;
  isComplete: boolean;
  lastContributionDate?: string;
}

interface Debt {
  id: string;
  name: string;
  totalAmount: number;
  startDate: string;
  remainingBalance: number;
  interestRate: number;
  monthlyPayment: number;
  dueDate: number;
  extraPayment: number;
  estimatedPayoffDate: string;
  lastPaymentDate?: string;
}

interface DebtData {
  debts: Debt[];
  totalDebt: number;
  totalMonthlyPayments: number;
  payoffStrategy: 'avalanche' | 'snowball';
  extraPaymentAllocation: number;
  estimatedDebtFreeDate: string;
}

interface SavingsGoal {
  id: string;
  name: string;
  targetAmount: number;
  currentAmount: number;
  dueDate?: string;
  imageUrl?: string;
  monthlyContribution: number;
  estimatedCompletionDate: string;
  isCompleted: boolean;
  lastContributionDate?: string;
}

interface SavingsGoalsData {
  goals: SavingsGoal[];
  totalTargetAmount: number;
  totalSavedAmount: number;
  totalMonthlyContribution: number;
}

interface InvestmentGoal {
  id: string;
  name: string;
  investmentType: 'stocks' | 'etfs' | 'bonds' | 'reits' | 'crypto' | 'index-funds' | 'mutual-funds';
  targetAmount: number;
  currentAmount: number;
  monthlyContribution: number;
  expectedReturnRate: number;
  contributionFrequency: 'weekly' | 'monthly' | 'quarterly';
  estimatedCompletionDate: string;
  isCompleted: boolean;
  lastContributionDate?: string;
}

interface InvestmentGoalsData {
  goals: InvestmentGoal[];
  totalTargetAmount: number;
  totalInvestedAmount: number;
  totalMonthlyContribution: number;
  projectedValue: number;
}

interface SuperFund {
  id: string;
  fundName: string;
  currentBalance: number;
  extraContributions: number;
  contributionFrequency: 'weekly' | 'fortnightly' | 'monthly' | 'quarterly' | 'yearly';
  expectedReturnRate: number;
  lastContributionDate?: string;
}

interface SuperannuationData {
  retirementAge: number;
  targetRetirementIncome: number;
  currentAge: number;
  superFunds: SuperFund[];
  totalCurrentBalance: number;
  totalExtraContributions: number;
  projectedRetirementBalance: number;
  projectedRetirementIncome: number;
  isOnTrack: boolean;
}

interface InsuranceData {
  policies: any[];
  totalCoverage: number;
  totalPremiums: number;
  upcomingRenewals: any[];
  coverageGaps: string[];
}

interface AssetProtectionData {
  assets: any[];
  totalAssetValue: number;
  protectedAssets: number;
  unprotectedAssets: number;
  protectionScore: number;
  recommendations: string[];
}

interface EstatePlanningData {
  will: any;
  powerOfAttorney: any;
  beneficiaries: any[];
  totalEstateValue: number;
  hasCompletedChecklist: boolean;
  checklistItems: any[];
  completionScore: number;
  recommendations: string[];
  nextReviewDate?: string;
  professionalAdvice: boolean;
}

interface DashboardProps {
  incomeData: IncomeData;
  expenseData: ExpenseTrackerData;
  safetyNetData?: SafetyNetData | null;
  debtData?: DebtData | null;
  savingsGoalsData?: SavingsGoalsData | null;
  investmentGoalsData?: InvestmentGoalsData | null;
  superannuationData?: SuperannuationData | null;
  insuranceData?: InsuranceData | null;
  assetProtectionData?: AssetProtectionData | null;
  estatePlanningData?: EstatePlanningData | null;
  onUpdateExpenses: (data: ExpenseTrackerData) => void;
  onUpdateDebt?: (data: DebtData) => void;
  onUpdateSavingsGoals?: (data: SavingsGoalsData) => void;
  onUpdateInvestmentGoals?: (data: InvestmentGoalsData) => void;
  onUpdateSuperannuation?: (data: SuperannuationData) => void;
  onUpdateInsurance?: (data: InsuranceData) => void;
  onUpdateAssetProtection?: (data: AssetProtectionData) => void;
  onUpdateEstatePlanning?: (data: EstatePlanningData) => void;
  onStartExpenses?: () => void;
  onStartSafetyNet?: () => void;
  onStartDebtManager?: () => void;
  onStartSavingsGoals?: () => void;
  onStartInvestmentGoals?: () => void;
  onStartSuperannuation?: () => void;
  onStartInsurance?: () => void;
  onStartAssetProtection?: () => void;
  onStartEstatePlanning?: () => void;
  onLogout?: () => void;
}

// Helper function to safely convert to number and handle NaN
const safeNumber = (value: number | string | null | undefined, fallback: number = 0): number => {
  if (value === null || value === undefined) return fallback;
  const num = typeof value === 'string' ? parseFloat(value) : value;
  return isNaN(num) || !isFinite(num) ? fallback : num;
};

// Helper function to safely calculate percentages
const safePercentage = (numerator: number, denominator: number, fallback: number = 0): number => {
  if (denominator === 0 || isNaN(numerator) || isNaN(denominator)) return fallback;
  const result = (numerator / denominator) * 100;
  return isNaN(result) || !isFinite(result) ? fallback : Math.round(result);
};

export function Dashboard({ 
  incomeData, 
  expenseData, 
  safetyNetData, 
  debtData, 
  savingsGoalsData,
  investmentGoalsData,
  superannuationData,
  insuranceData,
  assetProtectionData,
  estatePlanningData,
  onUpdateExpenses, 
  onUpdateDebt, 
  onUpdateSavingsGoals,
  onUpdateInvestmentGoals,
  onUpdateSuperannuation,
  onUpdateInsurance,
  onUpdateAssetProtection,
  onUpdateEstatePlanning,
  onStartExpenses,
  onStartSafetyNet, 
  onStartDebtManager,
  onStartSavingsGoals,
  onStartInvestmentGoals,
  onStartSuperannuation,
  onStartInsurance,
  onStartAssetProtection,
  onStartEstatePlanning,
  onLogout
}: DashboardProps) {
  const [hideAmounts, setHideAmounts] = useState(false);
  const [cumulativeSavings, setCumulativeSavings] = useState(5000); // Demo starting value

  // Calculate monthly amounts with safe number handling
  const getMonthlyAmount = (item: SavingsInvestment | null): number => {
    if (!item) return 0;
    const amount = safeNumber(item.amount, 0);
    if (item.frequency === 'weekly') return amount * 4.33;
    if (item.frequency === 'yearly') return amount / 12;
    return amount;
  };

  const getMonthlyIncome = (): number => {
    const amount = safeNumber(incomeData.amount, 0);
    if (incomeData.frequency === 'weekly') return amount * 4.33;
    if (incomeData.frequency === 'yearly') return amount / 12;
    return amount;
  };

  const monthlyIncome = getMonthlyIncome();
  const totalMonthlyExpenses = expenseData.expenses.reduce((total, expense) => {
    const amount = safeNumber(expense.amount, 0);
    let monthlyAmount = amount;
    if (expense.frequency === 'weekly') monthlyAmount *= 4.33;
    if (expense.frequency === 'yearly') monthlyAmount /= 12;
    return total + monthlyAmount;
  }, 0);
  
  const monthlySavings = getMonthlyAmount(expenseData.savings);
  const monthlyInvestment = getMonthlyAmount(expenseData.investment);
  const safetyNetContribution = safetyNetData?.monthlyContribution || 0;
  const debtPayments = debtData?.totalMonthlyPayments || 0;
  const savingsGoalsContribution = savingsGoalsData?.totalMonthlyContribution || 0;
  const investmentGoalsContribution = investmentGoalsData?.totalMonthlyContribution || 0;
  const superContribution = superannuationData?.totalExtraContributions || 0;
  const insurancePremiums = insuranceData?.totalPremiums || 0;
  const netRemaining = monthlyIncome - totalMonthlyExpenses - monthlySavings - monthlyInvestment - safetyNetContribution - debtPayments - savingsGoalsContribution - investmentGoalsContribution - superContribution - insurancePremiums;

  // Determine current stage and step completion status
  const getCurrentStage = () => {
    // Stage 1: Optimise (Budget, Safety Net, Debt, Save)
    if (!safetyNetData || !debtData || !savingsGoalsData) {
      return "optimise";
    }
    // Stage 2: Maximise (Invest, Superannuation) 
    if (!investmentGoalsData || !superannuationData) {
      return "maximise";
    }
    // Stage 3: Protect (Insurance, Asset Protection, Estate Planning)
    return "protect";
  };

  const getActiveTabForProgressTabs = () => {
    // Show optimise as active until all optimise steps are complete
    if (!safetyNetData || !debtData || !savingsGoalsData) {
      return "optimise";
    }
    // Show maximise as active until all maximise steps are complete (Invest first, then Superannuation)
    if (!investmentGoalsData || !superannuationData) {
      return "maximise";
    }
    // Show protect as active for protect steps
    return "protect";
  };

  const getCompletedSteps = (stage: string) => {
    if (stage === "optimise") {
      const completed = ["Budget"]; // Budget is always completed when reaching dashboard
      if (safetyNetData) completed.push("Safety Net");
      if (debtData) completed.push("Debt");
      if (savingsGoalsData) completed.push("Save");
      return completed;
    }
    
    if (stage === "maximise") {
      const completed: string[] = [];
      if (investmentGoalsData) completed.push("Invest");
      if (superannuationData) completed.push("Superannuation");
      return completed;
    }
    
    if (stage === "protect") {
      const completed: string[] = [];
      if (insuranceData) completed.push("Insurance");
      if (assetProtectionData) completed.push("Asset Protection");
      if (estatePlanningData) completed.push("Estate Planning");
      return completed;
    }
    
    return [];
  };

  const getCurrentStep = (stage: string) => {
    if (stage === "optimise") {
      if (!safetyNetData) return "Safety Net";
      if (!debtData) return "Debt"; 
      if (!savingsGoalsData) return "Save";
      return "Save";
    }
    
    if (stage === "maximise") {
      if (!investmentGoalsData) return "Invest";
      if (!superannuationData) return "Superannuation";
      return "Superannuation";
    }
    
    if (stage === "protect") {
      if (!insuranceData) return "Insurance";
      if (!assetProtectionData) return "Asset Protection";
      if (!estatePlanningData) return "Estate Planning";
      return "Estate Planning";
    }
    
    return "";
  };

  const getStepsForStage = (stage: string) => {
    if (stage === "optimise") return OPTIMISE_VERTICAL_STEPS;
    if (stage === "maximise") return MAXIMISE_VERTICAL_STEPS;
    if (stage === "protect") return PROTECT_VERTICAL_STEPS;
    return OPTIMISE_VERTICAL_STEPS;
  };

  // Handle step clicks - navigate to the appropriate flow for completed steps
  const handleStepClick = (stepName: string) => {
    switch (stepName) {
      case "Budget":
        // Navigate to expenses tracker to edit expenses
        if (onStartExpenses) {
          onStartExpenses();
        }
        break;
      case "Safety Net":
        if (safetyNetData && onStartSafetyNet) {
          onStartSafetyNet();
        }
        break;
      case "Debt":
        if (debtData && onStartDebtManager) {
          onStartDebtManager();
        }
        break;
      case "Save":
        if (savingsGoalsData && onStartSavingsGoals) {
          onStartSavingsGoals();
        }
        break;
      case "Invest":
        if (investmentGoalsData && onStartInvestmentGoals) {
          onStartInvestmentGoals();
        }
        break;
      case "Superannuation":
        if (superannuationData && onStartSuperannuation) {
          onStartSuperannuation();
        }
        break;
      case "Insurance":
        if (insuranceData && onStartInsurance) {
          onStartInsurance();
        }
        break;
      case "Asset Protection":
        if (assetProtectionData && onStartAssetProtection) {
          onStartAssetProtection();
        }
        break;
      case "Estate Planning":
        if (estatePlanningData && onStartEstatePlanning) {
          onStartEstatePlanning();
        }
        break;
      default:
        break;
    }
  };

  const currentStage = getCurrentStage();
  const activeTab = getActiveTabForProgressTabs();
  const completedSteps = getCompletedSteps(currentStage);
  const currentStep = getCurrentStep(currentStage);
  const stepsForCurrentStage = getStepsForStage(currentStage);

  // Determine which step card to show (corrected order: Invest before Superannuation)
  const showSafetyNetCard = !safetyNetData;
  const showDebtCard = safetyNetData && !debtData;
  const showSavingsGoalsCard = safetyNetData && debtData && !savingsGoalsData;
  const showInvestmentGoalsCard = safetyNetData && debtData && savingsGoalsData && !investmentGoalsData;
  const showSuperannuationCard = safetyNetData && debtData && savingsGoalsData && investmentGoalsData && !superannuationData;
  const showInsuranceCard = safetyNetData && debtData && savingsGoalsData && investmentGoalsData && superannuationData && !insuranceData;
  const showAssetProtectionCard = safetyNetData && debtData && savingsGoalsData && investmentGoalsData && superannuationData && insuranceData && !assetProtectionData;
  const showEstatePlanningCard = safetyNetData && debtData && savingsGoalsData && investmentGoalsData && superannuationData && insuranceData && assetProtectionData && !estatePlanningData;

  const formatAmount = (amount: number) => {
    if (hideAmounts) return '••••';
    const safeAmount = safeNumber(amount, 0);
    return `${safeAmount.toLocaleString()}`;
  };

  // Safe calculations for percentages and ratios
  const savingsRate = safePercentage(monthlySavings + monthlyInvestment, monthlyIncome, 0);
  const safetyNetProgress = safetyNetData ? safePercentage(safetyNetData.currentAmount, safetyNetData.totalTarget, 0) : 0;

  // Handlers for new components
  const handleUpdateExpensesData = (expenses: Expense[]) => {
    const updatedExpenseData = { ...expenseData, expenses };
    onUpdateExpenses(updatedExpenseData);
  };

  const handleContributeSavings = (amount: number) => {
    setCumulativeSavings(prev => prev + amount);
  };

  const handleViewMonthlyReport = () => {
    console.log("View monthly report clicked");
    // This could navigate to a monthly report page or show a dialog
  };

  const handleSetFinancialGoals = () => {
    // Navigate to appropriate goal setting based on current stage
    if (!savingsGoalsData && onStartSavingsGoals) {
      onStartSavingsGoals();
    } else if (!investmentGoalsData && onStartInvestmentGoals) {
      onStartInvestmentGoals();
    } else {
      console.log("Set financial goals clicked");
    }
  };

  const handleManageReminders = () => {
    console.log("Manage reminders clicked");
    // This could show a reminders management dialog
  };

  // Get completed tabs for ProgressTabs
  const getCompletedTabs = (): ("optimise" | "maximise" | "protect")[] => {
    const completed: ("optimise" | "maximise" | "protect")[] = [];
    
    // Optimise is completed when all its steps are done
    if (safetyNetData && debtData && savingsGoalsData) {
      completed.push("optimise");
    }
    
    // Maximise is completed when all its steps are done
    if (investmentGoalsData && superannuationData) {
      completed.push("maximise");
    }
    
    // Protect is completed when all its steps are done
    if (insuranceData && assetProtectionData && estatePlanningData) {
      completed.push("protect");
    }
    
    return completed;
  };

  // Handle tab navigation from ProgressTabs
  const handleTabNavigation = (tab: "optimise" | "maximise" | "protect") => {
    switch (tab) {
      case "optimise":
        // Navigate to the first incomplete step in optimise, or last completed if all done
        if (!safetyNetData) {
          onStartSafetyNet?.();
        } else if (!debtData) {
          onStartDebtManager?.();
        } else if (!savingsGoalsData) {
          onStartSavingsGoals?.();
        } else {
          // All optimise steps completed, go to last one for editing
          onStartSavingsGoals?.();
        }
        break;
      case "maximise":
        // Navigate to the first incomplete step in maximise, or last completed if all done
        if (!investmentGoalsData) {
          onStartInvestmentGoals?.();
        } else if (!superannuationData) {
          onStartSuperannuation?.();
        } else {
          // All maximise steps completed, go to last one for editing
          onStartSuperannuation?.();
        }
        break;
      case "protect":
        // Navigate to the first incomplete step in protect, or last completed if all done
        if (!insuranceData) {
          onStartInsurance?.();
        } else if (!assetProtectionData) {
          onStartAssetProtection?.();
        } else if (!estatePlanningData) {
          onStartEstatePlanning?.();
        } else {
          // All protect steps completed, go to last one for editing
          onStartEstatePlanning?.();
        }
        break;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-50 py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-between items-center mb-8"
        >
          <div className="flex items-center gap-4">
            <img src={obieLogo} alt="Obiemoney Logo" className="h-10 w-auto" />
            <div>
              <h1 className="text-3xl font-bold text-purple-600">Financial Dashboard</h1>
              <p className="text-gray-600">Manage your wealth, track your progress</p>
            </div>
          </div>
          
          <div className="flex items-center gap-4">
            {/* <Button
              variant="outline"
              size="sm"
              onClick={() => setHideAmounts(!hideAmounts)}
              className="flex items-center gap-2"
            >
              {hideAmounts ? <Eye size={16} /> : <EyeOff size={16} />}
              {hideAmounts ? 'Show' : 'Hide'} Amounts
            </Button> */}
            <Button variant="outline" size="sm">
              <Settings size={16} className="mr-2" />
              Settings
            </Button>
            
            {/* Logout Button with Confirmation */}
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200">
                  <LogOut size={16} className="mr-2" />
                  Logout
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Are you sure you want to logout?</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will end your current session and you'll need to sign up again to access your financial data. 
                    Your progress will not be saved permanently.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction 
                    onClick={onLogout}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    Yes, Logout
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </motion.div>

        {/* Quick Stats Cards */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8"
        >
          <Card className="p-6 bg-gradient-to-r from-green-50 to-green-100 border-green-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-green-600 mb-1">Monthly Income</p>
                <p className="text-2xl font-bold text-green-700">{formatAmount(monthlyIncome)}</p>
                <p className="text-xs text-green-500">{incomeData.frequency} payment</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
          </Card>

          <Card className="p-6 bg-gradient-to-r from-red-50 to-red-100 border-red-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-red-600 mb-1">Monthly Expenses</p>
                <p className="text-2xl font-bold text-red-700">{formatAmount(totalMonthlyExpenses)}</p>
                <p className="text-xs text-red-500">{expenseData.expenses.length} items tracked</p>
              </div>
              <TrendingDown className="h-8 w-8 text-red-600" />
            </div>
          </Card>

          <Card className="p-6 bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-blue-600 mb-1">Total Savings</p>
                <p className="text-2xl font-bold text-blue-700">{formatAmount(monthlySavings + monthlyInvestment)}</p>
                <p className="text-xs text-blue-500">Monthly contribution</p>
              </div>
              <PiggyBank className="h-8 w-8 text-blue-600" />
            </div>
          </Card>
        </motion.div>

        {/* Step Cards with Journey Progress Layout */}
        
        {/* Safety Net Card */}
        {showSafetyNetCard && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card className="p-6 bg-gradient-to-br from-orange-50/70 via-white to-red-50/50 border border-orange-200/40 relative overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-24 h-24 bg-orange-100/20 rounded-full -mr-12 -mt-12"></div>
              <div className="absolute bottom-0 left-0 w-20 h-20 bg-red-100/20 rounded-full -ml-10 -mb-10"></div>
              
              <div className="relative z-10">
                {/* Header with Progress Tabs */}
                <div className="mb-6 text-center">
                  <ProgressTabs activeTab={activeTab} />
                </div>

                {/* Main Content with Sidebar Layout */}
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Left Sidebar - Vertical Steps */}
                  <div className="lg:w-[220px] flex-shrink-0">
                    <VerticalStepper 
                      steps={stepsForCurrentStage} 
                      completedSteps={completedSteps}
                      currentStep={currentStep}
                      onStepClick={handleStepClick}
                    />
                  </div>

                  {/* Main Content Area - Safety Net Call-to-Action */}
                  <div className="flex-1">
                    <div className="bg-white/70 rounded-xl p-6 border border-white/80 shadow-sm h-full">
                      <div className="flex items-start gap-4 mb-4">
                        <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl shadow-md flex-shrink-0">
                          <Shield className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 mb-1">Step 2: Create Your Safety Net</h3>
                          <p className="text-orange-600 font-medium">Create your Safety Net</p>
                        </div>
                      </div>
                      
                      <p className="text-gray-700 mb-5 leading-relaxed">
                        Build a robust financial safety net with emergency funds, coverage for big expenses, and 3-month living expenses. 
                        Get peace of mind knowing you're prepared for anything life throws your way.
                      </p>
                      
                      <div className="flex flex-col sm:flex-row gap-3 items-start">
                        <Button 
                          onClick={onStartSafetyNet}
                          className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto"
                        >
                          Start Building Safety Net
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                        
                        <div className="text-sm text-gray-500 sm:ml-3">
                          <span className="font-medium">Pro tip:</span> A solid safety net gives you confidence to take on bigger financial goals
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Debt Card */}
        {showDebtCard && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card className="p-6 bg-gradient-to-br from-red-50/70 via-white to-yellow-50/50 border border-red-200/40 relative overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-24 h-24 bg-red-100/20 rounded-full -mr-12 -mt-12"></div>
              <div className="absolute bottom-0 left-0 w-20 h-20 bg-yellow-100/20 rounded-full -ml-10 -mb-10"></div>
              
              <div className="relative z-10">
                {/* Header with Progress Tabs */}
                <div className="mb-6 text-center">
                  <ProgressTabs activeTab={activeTab} />
                </div>

                {/* Main Content with Sidebar Layout */}
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Left Sidebar - Vertical Steps */}
                  <div className="lg:w-[220px] flex-shrink-0">
                    <VerticalStepper 
                      steps={stepsForCurrentStage} 
                      completedSteps={completedSteps}
                      currentStep={currentStep}
                      onStepClick={handleStepClick}
                    />
                  </div>

                  {/* Main Content Area - Debt Call-to-Action */}
                  <div className="flex-1">
                    <div className="bg-white/70 rounded-xl p-6 border border-white/80 shadow-sm h-full">
                      <div className="flex items-start gap-4 mb-4">
                        <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-red-500 to-yellow-500 rounded-xl shadow-md flex-shrink-0">
                          <CreditCard className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 mb-1">Step 3: Deal with your Debt</h3>
                          <p className="text-red-600 font-medium">Deal with your Debt</p>
                        </div>
                      </div>
                      
                      <p className="text-gray-700 mb-5 leading-relaxed">
                        Take control of your debt with strategic repayment plans, extra payment allocations, and proven debt elimination methods. 
                        Track your progress and accelerate your path to financial freedom.
                      </p>
                      
                      <div className="flex flex-col sm:flex-row gap-3 items-start">
                        <Button 
                          onClick={onStartDebtManager}
                          className="bg-gradient-to-r from-red-500 to-yellow-500 hover:from-red-600 hover:to-yellow-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto"
                        >
                          Start Managing Debt
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                        
                        <div className="text-sm text-gray-500 sm:ml-3">
                          <span className="font-medium">Pro tip:</span> Tackle high-interest debt first for maximum savings
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Savings Goals Card */}
        {showSavingsGoalsCard && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card className="p-6 bg-gradient-to-br from-blue-50/70 via-white to-purple-50/50 border border-blue-200/40 relative overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-24 h-24 bg-blue-100/20 rounded-full -mr-12 -mt-12"></div>
              <div className="absolute bottom-0 left-0 w-20 h-20 bg-purple-100/20 rounded-full -ml-10 -mb-10"></div>
              
              <div className="relative z-10">
                {/* Header with Progress Tabs */}
                <div className="mb-6 text-center">
                  <ProgressTabs activeTab={activeTab} />
                </div>

                {/* Main Content with Sidebar Layout */}
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Left Sidebar - Vertical Steps */}
                  <div className="lg:w-[220px] flex-shrink-0">
                    <VerticalStepper 
                      steps={stepsForCurrentStage} 
                      completedSteps={completedSteps}
                      currentStep={currentStep}
                      onStepClick={handleStepClick}
                    />
                  </div>

                  {/* Main Content Area - Savings Goals Call-to-Action */}
                  <div className="flex-1">
                    <div className="bg-white/70 rounded-xl p-6 border border-white/80 shadow-sm h-full">
                      <div className="flex items-start gap-4 mb-4">
                        <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl shadow-md flex-shrink-0">
                          <Target className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 mb-1">Step 4: Set your Financial Savings Goals</h3>
                          <p className="text-blue-600 font-medium">Set your Financial Savings Goals</p>
                        </div>
                      </div>
                      
                      <p className="text-gray-700 mb-5 leading-relaxed">
                        Create personalized savings goals for your dreams and aspirations. Set target amounts, 
                        timelines, and track your progress with smart monthly contributions. Make your financial 
                        goals a reality!
                      </p>
                      
                      <div className="flex flex-col sm:flex-row gap-3 items-start">
                        <Button 
                          onClick={onStartSavingsGoals}
                          className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto"
                        >
                          Start Setting Goals
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                        
                        <div className="text-sm text-gray-500 sm:ml-3">
                          <span className="font-medium">Pro tip:</span> Start with 1-2 meaningful goals for better focus
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Investment Goals Card */}
        {showInvestmentGoalsCard && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card className="p-6 bg-gradient-to-br from-green-50/70 via-white to-emerald-50/50 border border-green-200/40 relative overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-24 h-24 bg-green-100/20 rounded-full -mr-12 -mt-12"></div>
              <div className="absolute bottom-0 left-0 w-20 h-20 bg-emerald-100/20 rounded-full -ml-10 -mb-10"></div>
              
              <div className="relative z-10">
                {/* Header with Progress Tabs */}
                <div className="mb-6 text-center">
                  <ProgressTabs activeTab={activeTab} />
                </div>

                {/* Main Content with Sidebar Layout */}
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Left Sidebar - Vertical Steps */}
                  <div className="lg:w-[220px] flex-shrink-0">
                    <VerticalStepper 
                      steps={stepsForCurrentStage} 
                      completedSteps={completedSteps}
                      currentStep={currentStep}
                      onStepClick={handleStepClick}
                    />
                  </div>

                  {/* Main Content Area - Investment Goals Call-to-Action */}
                  <div className="flex-1">
                    <div className="bg-white/70 rounded-xl p-6 border border-white/80 shadow-sm h-full">
                      <div className="flex items-start gap-4 mb-4">
                        <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl shadow-md flex-shrink-0">
                          <LineChart className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 mb-1">Step 5: Invest</h3>
                          <p className="text-green-600 font-medium">Small amounts over time make a big difference</p>
                        </div>
                      </div>
                      
                      <p className="text-gray-700 mb-5 leading-relaxed">
                        Now that you've optimized your finances, it's time to maximize your wealth! Create strategic investment goals 
                        with compound growth calculations, track your portfolio progress, and build long-term financial success.
                      </p>
                      
                      <div className="flex flex-col sm:flex-row gap-3 items-start">
                        <Button 
                          onClick={onStartInvestmentGoals}
                          className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto"
                        >
                          Start Investing
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                        
                        <div className="text-sm text-gray-500 sm:ml-3">
                          💡 <span className="font-medium">Pro tip:</span> Time in the market beats timing the market
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Superannuation Card */}
        {showSuperannuationCard && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card className="p-6 bg-gradient-to-br from-indigo-50/70 via-white to-cyan-50/50 border border-indigo-200/40 relative overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-24 h-24 bg-indigo-100/20 rounded-full -mr-12 -mt-12"></div>
              <div className="absolute bottom-0 left-0 w-20 h-20 bg-cyan-100/20 rounded-full -ml-10 -mb-10"></div>
              
              <div className="relative z-10">
                {/* Header with Progress Tabs */}
                <div className="mb-6 text-center">
                  <ProgressTabs activeTab={activeTab} />
                </div>

                {/* Main Content with Sidebar Layout */}
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Left Sidebar - Vertical Steps */}
                  <div className="lg:w-[220px] flex-shrink-0">
                    <VerticalStepper 
                      steps={stepsForCurrentStage} 
                      completedSteps={completedSteps}
                      currentStep={currentStep}
                      onStepClick={handleStepClick}
                    />
                  </div>

                  {/* Main Content Area - Superannuation Call-to-Action */}
                  <div className="flex-1">
                    <div className="bg-white/70 rounded-xl p-6 border border-white/80 shadow-sm h-full">
                      <div className="flex items-start gap-4 mb-4">
                        <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-indigo-500 to-cyan-500 rounded-xl shadow-md flex-shrink-0">
                          <Building className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 mb-1">Step 6: Superannuation</h3>
                          <p className="text-indigo-600 font-medium">Super? Enough to retire on?</p>
                        </div>
                      </div>
                      
                      <p className="text-gray-700 mb-5 leading-relaxed">
                        Complete your wealth maximization strategy! Conduct a superannuation health check, set retirement income goals, 
                        and optimize your super funds with extra contributions for a comfortable retirement.
                      </p>
                      
                      <div className="flex flex-col sm:flex-row gap-3 items-start">
                        <Button 
                          onClick={onStartSuperannuation}
                          className="bg-gradient-to-r from-indigo-500 to-cyan-500 hover:from-indigo-600 hover:to-cyan-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto"
                        >
                          Check My Super
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                        
                        <div className="text-sm text-gray-500 sm:ml-3">
                          <span className="font-medium">Pro tip:</span> Small extra contributions now create huge retirement gains
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Insurance Card */}
        {showInsuranceCard && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card className="p-6 bg-gradient-to-br from-emerald-50/70 via-white to-teal-50/50 border border-emerald-200/40 relative overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-24 h-24 bg-emerald-100/20 rounded-full -mr-12 -mt-12"></div>
              <div className="absolute bottom-0 left-0 w-20 h-20 bg-teal-100/20 rounded-full -ml-10 -mb-10"></div>
              
              <div className="relative z-10">
                {/* Header with Progress Tabs */}
                <div className="mb-6 text-center">
                  <ProgressTabs activeTab={activeTab} />
                </div>

                {/* Main Content with Sidebar Layout */}
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Left Sidebar - Vertical Steps */}
                  <div className="lg:w-[220px] flex-shrink-0">
                    <VerticalStepper 
                      steps={stepsForCurrentStage} 
                      completedSteps={completedSteps}
                      currentStep={currentStep}
                      onStepClick={handleStepClick}
                    />
                  </div>

                  {/* Main Content Area - Insurance Call-to-Action */}
                  <div className="flex-1">
                    <div className="bg-white/70 rounded-xl p-6 border border-white/80 shadow-sm h-full">
                      <div className="flex items-start gap-4 mb-4">
                        <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-xl shadow-md flex-shrink-0">
                          <Shield className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 mb-1">Step 7: Insurance</h3>
                          <p className="text-emerald-600 font-medium">Do I have Insurance I need?</p>
                        </div>
                      </div>
                      
                      <p className="text-gray-700 mb-5 leading-relaxed">
                        Now it's time to protect your wealth! Review your insurance coverage, identify gaps, and ensure your family and assets 
                        are protected against life's unexpected events. Insurance is your financial safety net.
                      </p>
                      
                      <div className="flex flex-col sm:flex-row gap-3 items-start">
                        <Button 
                          onClick={onStartInsurance}
                          className="bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto"
                        >
                          Review Insurance
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                        
                        <div className="text-sm text-gray-500 sm:ml-3">
                          <span className="font-medium">Pro tip:</span> Review policies annually to ensure adequate coverage
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Asset Protection Card */}
        {showAssetProtectionCard && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card className="p-6 bg-gradient-to-br from-amber-50/70 via-white to-orange-50/50 border border-amber-200/40 relative overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-24 h-24 bg-amber-100/20 rounded-full -mr-12 -mt-12"></div>
              <div className="absolute bottom-0 left-0 w-20 h-20 bg-orange-100/20 rounded-full -ml-10 -mb-10"></div>
              
              <div className="relative z-10">
                {/* Header with Progress Tabs */}
                <div className="mb-6 text-center">
                  <ProgressTabs activeTab={activeTab} />
                </div>

                {/* Main Content with Sidebar Layout */}
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Left Sidebar - Vertical Steps */}
                  <div className="lg:w-[220px] flex-shrink-0">
                    <VerticalStepper 
                      steps={stepsForCurrentStage} 
                      completedSteps={completedSteps}
                      currentStep={currentStep}
                      onStepClick={handleStepClick}
                    />
                  </div>

                  {/* Main Content Area - Asset Protection Call-to-Action */}
                  <div className="flex-1">
                    <div className="bg-white/70 rounded-xl p-6 border border-white/80 shadow-sm h-full">
                      <div className="flex items-start gap-4 mb-4">
                        <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-amber-500 to-orange-500 rounded-xl shadow-md flex-shrink-0">
                          <Shield className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 mb-1">Step 8: Asset Protection</h3>
                          <p className="text-amber-600 font-medium">Are my assets protected?</p>
                        </div>
                      </div>
                      
                      <p className="text-gray-700 mb-5 leading-relaxed">
                        Complete your protection strategy! Catalog your assets, assess protection status, and implement strategies 
                        to safeguard your wealth from legal claims, creditors, and unforeseen risks.
                      </p>
                      
                      <div className="flex flex-col sm:flex-row gap-3 items-start">
                        <Button 
                          onClick={onStartAssetProtection}
                          className="bg-gradient-to-r from-amber-500 to-orange-500 hover:from-amber-600 hover:to-orange-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto"
                        >
                          Protect My Assets
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                        
                        <div className="text-sm text-gray-500 sm:ml-3">
                          <span className="font-medium">Pro tip:</span> Asset protection should be proactive, not reactive
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Estate Planning Card */}
        {showEstatePlanningCard && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-8"
          >
            <Card className="p-6 bg-gradient-to-br from-purple-50/70 via-white to-indigo-50/50 border border-purple-200/40 relative overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="absolute top-0 right-0 w-24 h-24 bg-purple-100/20 rounded-full -mr-12 -mt-12"></div>
              <div className="absolute bottom-0 left-0 w-20 h-20 bg-indigo-100/20 rounded-full -ml-10 -mb-10"></div>
              
              <div className="relative z-10">
                {/* Header with Progress Tabs */}
                <div className="mb-6 text-center">
                  <ProgressTabs activeTab={activeTab} />
                </div>

                {/* Main Content with Sidebar Layout */}
                <div className="flex flex-col lg:flex-row gap-6">
                  {/* Left Sidebar - Vertical Steps */}
                  <div className="lg:w-[220px] flex-shrink-0">
                    <VerticalStepper 
                      steps={stepsForCurrentStage} 
                      completedSteps={completedSteps}
                      currentStep={currentStep}
                      onStepClick={handleStepClick}
                    />
                  </div>

                  {/* Main Content Area - Estate Planning Call-to-Action */}
                  <div className="flex-1">
                    <div className="bg-white/70 rounded-xl p-6 border border-white/80 shadow-sm h-full">
                      <div className="flex items-start gap-4 mb-4">
                        <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-purple-500 to-indigo-500 rounded-xl shadow-md flex-shrink-0">
                          <FileText className="h-6 w-6 text-white" />
                        </div>
                        <div className="flex-1">
                          <h3 className="text-xl font-bold text-gray-900 mb-1">Step 9: Estate Planning</h3>
                          <p className="text-purple-600 font-medium">Who's in charge when I can't be?</p>
                        </div>
                      </div>
                      
                      <p className="text-gray-700 mb-5 leading-relaxed">
                        Secure your family's future and ensure your wishes are followed! Create essential estate planning documents, 
                        appoint trusted decision-makers, and establish clear instructions for when you cannot be in control.
                      </p>
                      
                      <div className="flex flex-col sm:flex-row gap-3 items-start">
                        <Button 
                          onClick={onStartEstatePlanning}
                          className="bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto"
                        >
                          Complete Estate Planning
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Button>
                        
                        <div className="text-sm text-gray-500 sm:ml-3">
                          <span className="font-medium">Pro tip:</span> Estate planning protects your family's future
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* 9-Step Journey Complete Celebration */}
        {estatePlanningData && estatePlanningData.hasCompletedChecklist && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="mb-8"
          >
            <Card className="p-8 bg-gradient-to-r from-green-50 via-emerald-50 to-teal-50 border-green-200">
              <div className="text-center">
                <div className="inline-flex p-4 bg-green-100 rounded-full mb-4">
                  <Trophy className="h-8 w-8 text-green-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800 mb-2">🎉 Congratulations!</h2>
                <p className="text-xl text-green-600 font-medium mb-4">
                  You've completed your 9-step financial journey!
                </p>
                <p className="text-gray-700 max-w-2xl mx-auto mb-6">
                  From budget optimization to estate planning, you've built a comprehensive financial strategy. 
                  Your money is now working smarter, your wealth is protected, and your legacy is secured. 
                  Well done on mastering your financial future!
                </p>
                <div className="flex justify-center items-center gap-2 mb-6">
                  <span className="text-sm font-medium text-gray-600">Your Journey:</span>
                  <div className="flex -space-x-1">
                    {[1,2,3,4,5,6,7,8,9].map(step => (
                      <div key={step} className="w-8 h-8 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                        <CheckCircle className="h-4 w-4 text-white" />
                      </div>
                    ))}
                  </div>
                </div>
                <div className="grid md:grid-cols-3 gap-4 text-center">
                  <div>
                    <p className="text-2xl font-bold text-orange-600">Stage 1</p>
                    <p className="text-sm text-gray-600">Optimise Complete</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-blue-600">Stage 2</p>
                    <p className="text-sm text-gray-600">Maximise Complete</p>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-purple-600">Stage 3</p>
                    <p className="text-sm text-gray-600">Protect Complete</p>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        {/* Summary Progress Cards for completed sections */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Safety Net Progress Card (if completed) */}
          {safetyNetData && (
            <Card className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 rounded-full">
                    <Shield className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-800">Safety Net</h3>
                    <p className="text-sm text-green-600">
                      {formatAmount(safetyNetData.currentAmount)} of {formatAmount(safetyNetData.totalTarget)}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-green-600">{safetyNetProgress}%</p>
                  <p className="text-xs text-gray-500">Complete</p>
                </div>
              </div>
              <Progress value={safetyNetProgress} className="h-2" />
            </Card>
          )}

          {/* Add similar cards for other completed sections */}
          {debtData && (
            <Card className="p-6 bg-gradient-to-r from-red-50 to-orange-50 border-red-200">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-red-100 rounded-full">
                    <CreditCard className="h-5 w-5 text-red-600" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-800">Debt Management</h3>
                    <p className="text-sm text-red-600">
                      {formatAmount(debtData.totalDebt)} • {debtData.debts.length} accounts
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          )}

          {savingsGoalsData && savingsGoalsData.goals.length > 0 && (
            <Card className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-full">
                    <Target className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-800">Savings Goals</h3>
                    <p className="text-sm text-blue-600">
                      {savingsGoalsData.goals.length} active goals
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          )}
        </div>

        {/* New Dashboard Components Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-8"
        >
          {/* Left Column - Current Month Expenses (Takes up 2 columns) */}
          <div className="lg:col-span-2">
            <CurrentMonthExpenses 
              expenses={expenseData.expenses} 
              onUpdateExpenses={handleUpdateExpensesData}
            />
          </div>
          
          {/* Right Column - Other 3 components stacked */}
          <div className="lg:col-span-1 space-y-6">
            <CumulativeSavingsPool 
              totalSavings={cumulativeSavings}
              onContributeSavings={handleContributeSavings}
            />
            
            <FinancialHealthScore
              monthlyIncome={monthlyIncome}
              monthlyExpenses={totalMonthlyExpenses}
              monthlySavings={monthlySavings}
              monthlyInvestment={monthlyInvestment}
            />
            
            <QuickActions
              onViewMonthlyReport={handleViewMonthlyReport}
              onSetFinancialGoals={handleSetFinancialGoals}
              onManageReminders={handleManageReminders}
            />
          </div>
        </motion.div>

      </div>
    </div>
  );
}