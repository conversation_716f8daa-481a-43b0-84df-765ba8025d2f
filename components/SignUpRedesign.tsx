import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "motion/react";
import { Eye, EyeOff, CheckCircle, AlertCircle, Sparkles, TrendingUp, Shield, Heart, Zap, Star, Target, Users, Award } from "lucide-react";
import { ImageWithFallback } from "./figma/ImageWithFallback";
import obieLogo from 'figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png';
import newLoginImage from 'figma:asset/4c490d685a3bff778e00fa06db2182cf12d60844.png';

interface FormField {
  name: string;
  value: string;
  error?: string;
  success?: string;
  touched: boolean;
}

const empoweringMessages = [
  "You're taking control of your financial future!",
  "Every millionaire started with their first dollar", 
  "Your future self is already thanking you!",
  "Financial freedom is your birthright",
  "You're building something incredible!",
  "This is your moment to shine financially!",
  "Small steps today, giant leaps tomorrow!"
];

const trustIndicators = [
  { icon: <Shield size={16} />, text: "Bank-level security", color: "#6828E8" },
  { icon: <Users size={16} />, text: "Trusted by 50k+ users", color: "#064345" },
  { icon: <Award size={16} />, text: "#1 rated money app", color: "#FF4B00" }
];

const achievementStats = [
  { number: "50K+", label: "Users building wealth", icon: <Users size={24} />, color: "#064345" },
  { number: "$2.5B+", label: "Wealth created", icon: <TrendingUp size={24} />, color: "#FF4B00" },
  { number: "9", label: "Simple steps to success", icon: <Target size={24} />, color: "#6828E8" }
];

function FloatingShape({ 
  children, 
  className = "", 
  delay = 0 
}: { 
  children: React.ReactNode; 
  className?: string; 
  delay?: number;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0 }}
      animate={{ 
        opacity: [0.3, 0.6, 0.3],
        scale: [1, 1.1, 1],
        rotate: [0, 180, 360]
      }}
      transition={{
        duration: 6,
        repeat: Infinity,
        delay,
        ease: "easeInOut"
      }}
      className={`absolute ${className}`}
    >
      {children}
    </motion.div>
  );
}

function FormInput({
  label,
  type = "text",
  value,
  onChange,
  onBlur,
  placeholder,
  error,
  success,
  hint,
  icon
}: {
  label: string;
  type?: string;
  value: string;
  onChange: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  error?: string;
  success?: string;
  hint?: string;
  icon?: React.ReactNode;
}) {
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const inputType = type === "password" && showPassword ? "text" : type;

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="space-y-2"
    >
      <label className="block text-sm font-semibold mb-1" style={{ color: "#064345" }}>
        {label}
      </label>
      <div className="relative">
        <div
          className="relative border-2 rounded-xl transition-all duration-300"
          style={{
            borderColor: error 
              ? '#FF4B00' 
              : success 
              ? '#064345' 
              : isFocused 
              ? '#6828E8' 
              : '#E5E5E5',
            backgroundColor: error 
              ? '#FEF6ED' 
              : success 
              ? '#E9F7F4' 
              : isFocused 
              ? '#A37AF9' + '10' 
              : 'white'
          }}
        >
          <input
            type={inputType}
            value={value}
            onChange={(e) => onChange(e.target.value)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => {
              setIsFocused(false);
              onBlur?.();
            }}
            placeholder={placeholder}
            className="w-full px-4 py-3 bg-transparent border-none outline-none font-medium placeholder:opacity-60"
            style={{ color: "#064345" }}
          />
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-2">
            {type === "password" && (
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="p-1 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              >
                {showPassword ? <EyeOff size={18} className="text-gray-400" /> : <Eye size={18} className="text-gray-400" />}
              </button>
            )}
            {icon && <div className="text-gray-400">{icon}</div>}
            {success && <CheckCircle size={18} className="text-green-500" />}
            {error && <AlertCircle size={18} className="text-red-500" />}
          </div>
        </div>
      </div>
      
      <AnimatePresence>
        {(error || success || hint) && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            className="text-sm"
          >
            {error && (
              <div className="flex items-center gap-2 p-3 rounded-lg" style={{ color: "#FF4B00", backgroundColor: "#FEF6ED" }}>
                <AlertCircle size={16} />
                <span className="font-medium">{error}</span>
              </div>
            )}
            {success && (
              <div className="flex items-center gap-2 p-3 rounded-lg" style={{ color: "#064345", backgroundColor: "#E9F7F4" }}>
                <CheckCircle size={16} />
                <span className="font-medium">{success}</span>
              </div>
            )}
            {hint && !error && !success && (
              <div className="p-3 rounded-lg font-medium" style={{ color: "#6828E8", backgroundColor: "#FEF6ED" }}>
                {hint}
              </div>
            )}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

export function SignUpRedesign({ onComplete }: { onComplete: () => void }) {
  const [formFields, setFormFields] = useState<Record<string, FormField>>({
    firstName: { name: 'firstName', value: '', touched: false },
    lastName: { name: 'lastName', value: '', touched: false },
    email: { name: 'email', value: '', touched: false },
    password: { name: 'password', value: '', touched: false },
    confirmPassword: { name: 'confirmPassword', value: '', touched: false }
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentMessage, setCurrentMessage] = useState(0);

  // Rotate empowering messages
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentMessage(prev => (prev + 1) % empoweringMessages.length);
    }, 4000);
    return () => clearInterval(interval);
  }, []);

  const updateField = (fieldName: string, value: string) => {
    setFormFields(prev => {
      const updatedFields = {
        ...prev,
        [fieldName]: { ...prev[fieldName], value, touched: true }
      };
      
      // Validate field with updated state
      return validateField(fieldName, value, updatedFields);
    });
  };

  const validateField = (fieldName: string, value: string, currentFields?: Record<string, FormField>) => {
    const newFields = { ...(currentFields || formFields) };
    
    // Update the field value
    newFields[fieldName] = { ...newFields[fieldName], value };
    
    switch (fieldName) {
      case 'firstName':
        if (!value.trim()) {
          newFields.firstName.error = "We'd love to know your name!";
          newFields.firstName.success = undefined;
        } else {
          newFields.firstName.error = undefined;
          newFields.firstName.success = `Nice to meet you, ${value}!`;
        }
        break;
      
      case 'lastName':
        if (!value.trim()) {
          newFields.lastName.error = "Your last name helps us personalize your experience!";
          newFields.lastName.success = undefined;
        } else {
          newFields.lastName.error = undefined;
          newFields.lastName.success = "Perfect!";
        }
        break;
        
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!value) {
          newFields.email.error = "Your email is your gateway to wealth building!";
          newFields.email.success = undefined;
        } else if (!emailRegex.test(value)) {
          newFields.email.error = "Hmm, that doesn't look quite right";
          newFields.email.success = undefined;
        } else {
          newFields.email.error = undefined;
          newFields.email.success = "Great! We'll send you wealth-building tips here";
        }
        break;
        
      case 'password':
        if (!value) {
          newFields.password.error = "A strong password protects your financial journey! 🔒";
          newFields.password.success = undefined;
        } else if (value.length < 8) {
          newFields.password.error = "Make it at least 8 characters - future you will thank you!";
          newFields.password.success = undefined;
        } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
          newFields.password.error = "Mix it up! Try uppercase, lowercase, and numbers";
          newFields.password.success = undefined;
        } else {
          newFields.password.error = undefined;
          newFields.password.success = "That's a fortress-level password!";
        }
        break;
        
      case 'confirmPassword':
        if (!value) {
          newFields.confirmPassword.error = "Just double-checking for security!";
          newFields.confirmPassword.success = undefined;
        } else if (value !== newFields.password.value) {
          newFields.confirmPassword.error = "Passwords don't match - let's try that again!";
          newFields.confirmPassword.success = undefined;
        } else {
          newFields.confirmPassword.error = undefined;
          newFields.confirmPassword.success = "Perfect match! You're all set!";
        }
        break;
    }
    
    return newFields;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    onComplete();
  };

  const isFormValid = Object.values(formFields).every(field => 
    field.value && !field.error
  );
  
  const completedFields = Object.values(formFields).filter(field => 
    field.value && !field.error
  ).length;
  
  const progressPercentage = (completedFields / Object.keys(formFields).length) * 100;

  return (
    <div className="min-h-screen relative overflow-hidden" style={{ background: `linear-gradient(135deg, #E9F7F4 0%, #FEF6ED 50%, #E9F7F4 100%)` }}>
      {/* Floating background elements */}
      <FloatingShape className="top-20 left-20" delay={0}>
        <div className="w-16 h-16 rounded-full opacity-30" style={{ background: `linear-gradient(45deg, #A37AF9, #6828E8)` }} />
      </FloatingShape>
      
      <FloatingShape className="top-40 right-32" delay={1}>
        <Sparkles style={{ color: "#A37AF9" }} size={24} />
      </FloatingShape>
      
      <FloatingShape className="bottom-32 left-16" delay={2}>
        <TrendingUp style={{ color: "#FF4B00" }} size={28} />
      </FloatingShape>
      
      <FloatingShape className="bottom-20 right-20" delay={3}>
        <div className="w-12 h-12 rounded-lg opacity-30" style={{ background: `linear-gradient(45deg, #FF4B00, #A37AF9)` }} />
      </FloatingShape>
      
      <FloatingShape className="top-1/2 left-10" delay={4}>
        <div className="w-8 h-8 rounded-full opacity-40" style={{ backgroundColor: "#064345" }} />
      </FloatingShape>

      <div className="relative z-10 min-h-screen flex">
        {/* Left side - Inspiration */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          className="flex-1 flex flex-col justify-center items-center p-12 relative overflow-hidden"
          style={{ background: `linear-gradient(135deg, #E9F7F4 0%, rgba(233, 247, 244, 0.8) 100%)` }}
        >
          <div className="max-w-2xl w-full space-y-10">
            {/* Logo and Brand */}
            <motion.div
              initial={{ opacity: 0, y: -30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-center space-y-4"
            >
              <div className="flex justify-center mb-6">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.4, type: "spring", stiffness: 200 }}
                  className="relative"
                >
                  <ImageWithFallback 
                    src={obieLogo} 
                    alt="Obiemoney Logo" 
                    className="h-16 w-auto"
                  />
                </motion.div>
              </div>
              
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="text-4xl font-bold"
                style={{ color: "#064345" }}
              >
                Master Your Money with
                <span className="block mt-2" style={{ color: "#FF4B00" }}>
                  9 Simple Steps
                </span>
              </motion.h1>
              
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.8 }}
                className="text-xl font-medium"
                style={{ color: "#6828E8" }}
              >
                Turn your income into your wealth!
              </motion.p>
            </motion.div>

            {/* Achievement Stats */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1 }}
              className="grid grid-cols-3 gap-6"
            >
              {achievementStats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 1.2 + index * 0.2 }}
                  whileHover={{ scale: 1.05 }}
                  className="text-center p-6 rounded-2xl shadow-lg border-2"
                  style={{ 
                    backgroundColor: "rgba(255, 255, 255, 0.8)",
                    borderColor: stat.color + "20"
                  }}
                >
                  <motion.div
                    animate={{ y: [-5, 5, -5] }}
                    transition={{ duration: 3, repeat: Infinity, delay: index * 0.5 }}
                    className="inline-flex items-center justify-center w-12 h-12 rounded-full mb-3"
                    style={{ backgroundColor: stat.color + "15" }}
                  >
                    <div style={{ color: stat.color }}>
                      {stat.icon}
                    </div>
                  </motion.div>
                  <div className="text-2xl font-bold mb-1" style={{ color: stat.color }}>
                    {stat.number}
                  </div>
                  <div className="text-sm font-medium" style={{ color: "#064345" }}>
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </motion.div>

            {/* Hero Visual Section */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 1, delay: 0.3 }}
              className="relative"
            >
              <div className="w-full max-w-md mx-auto rounded-3xl overflow-hidden relative">
                <ImageWithFallback
                  src={newLoginImage}
                  alt="Financial collaboration illustration"
                  className="w-full h-80 object-contain"
                />
              </div>
              
              {/* Floating success indicators */}
              <motion.div
                animate={{ y: [-10, 10, -10] }}
                transition={{ duration: 3, repeat: Infinity }}
                className="absolute -top-4 -right-4 text-white p-4 rounded-full shadow-2xl border-4 border-white"
                style={{ backgroundColor: "#FF4B00" }}
              >
                <TrendingUp size={24} />
              </motion.div>
              
              <motion.div
                animate={{ y: [10, -10, 10] }}
                transition={{ duration: 4, repeat: Infinity }}
                className="absolute -bottom-4 -left-4 text-white p-4 rounded-full shadow-2xl border-4 border-white"
                style={{ backgroundColor: "#6828E8" }}
              >
                <Sparkles size={24} />
              </motion.div>
              
              <motion.div
                animate={{ x: [-5, 5, -5] }}
                transition={{ duration: 5, repeat: Infinity }}
                className="absolute top-1/2 -right-6 text-white p-3 rounded-full shadow-xl border-2 border-white"
                style={{ backgroundColor: "#A37AF9" }}
              >
                <Target size={20} />
              </motion.div>
            </motion.div>

            {/* Rotating empowering message */}
            <AnimatePresence mode="wait">
              <motion.div
                key={currentMessage}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5 }}
                className="p-8 rounded-3xl shadow-lg border-2 relative overflow-hidden"
                style={{ 
                  backgroundColor: "rgba(255, 255, 255, 0.9)",
                  borderColor: "#A37AF9" + "30"
                }}
              >
                <div 
                  className="absolute top-0 left-0 w-full h-1"
                  style={{ background: `linear-gradient(90deg, #FF4B00, #A37AF9, #6828E8)` }}
                />
                <p className="text-xl font-semibold text-center" style={{ color: "#064345" }}>
                  {empoweringMessages[currentMessage]}
                </p>
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                  className="absolute -bottom-2 -right-2 w-8 h-8 rounded-full opacity-30"
                  style={{ background: `linear-gradient(45deg, #FF4B00, #A37AF9)` }}
                />
              </motion.div>
            </AnimatePresence>


          </div>
        </motion.div>

        {/* Right side - Sign up form */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="flex-1 flex items-center justify-center p-8"
          style={{ background: `linear-gradient(135deg, #FEF6ED 0%, rgba(254, 246, 237, 0.8) 100%)` }}
        >
          <div className="w-full max-w-lg">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="rounded-3xl shadow-2xl p-10 border-2"
              style={{ 
                backgroundColor: "rgba(255, 255, 255, 0.95)",
                borderColor: "#6828E8" + "20"
              }}
            >
              {/* Header */}
              <div className="text-center mb-8">

                
                <h1 className="text-4xl font-bold mb-3" style={{ color: "#064345" }}>
                  Your wealth story starts now!
                </h1>
                <p className="text-lg font-medium" style={{ color: "#6828E8" }}>
                  Join the financial revolution.
                </p>
                
                {/* Progress indicator */}
                {completedFields > 0 && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="mt-6 p-4 rounded-xl border-2"
                    style={{ 
                      backgroundColor: "#E9F7F4",
                      borderColor: "#A37AF9" + "30"
                    }}
                  >
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm font-semibold" style={{ color: "#064345" }}>
                        Progress: {completedFields}/{Object.keys(formFields).length} fields
                      </span>
                      <span className="text-sm font-medium" style={{ color: "#6828E8" }}>
                        {Math.round(progressPercentage)}% complete
                      </span>
                    </div>
                    <div className="w-full rounded-full h-3" style={{ backgroundColor: "#E9F7F4" }}>
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${progressPercentage}%` }}
                        transition={{ duration: 0.5 }}
                        className="h-3 rounded-full"
                        style={{ background: `linear-gradient(90deg, #FF4B00, #A37AF9, #6828E8)` }}
                      />
                    </div>
                    {completedFields === Object.keys(formFields).length && (
                      <motion.p
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="font-semibold text-sm mt-3 flex items-center justify-center gap-2"
                        style={{ color: "#FF4B00" }}
                      >
                        <CheckCircle size={16} />
                        Ready to launch your financial journey!
                      </motion.p>
                    )}
                  </motion.div>
                )}
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <FormInput
                    label="First name"
                    value={formFields.firstName.value}
                    onChange={(value) => updateField('firstName', value)}
                    placeholder="Your awesome name"
                    error={formFields.firstName.error}
                    success={formFields.firstName.success}
                  />
                  
                  <FormInput
                    label="Last name"
                    value={formFields.lastName.value}
                    onChange={(value) => updateField('lastName', value)}
                    placeholder="Family name"
                    error={formFields.lastName.error}
                    success={formFields.lastName.success}
                  />
                </div>

                <FormInput
                  label="Email address"
                  type="email"
                  value={formFields.email.value}
                  onChange={(value) => updateField('email', value)}
                  placeholder="<EMAIL>"
                  error={formFields.email.error}
                  success={formFields.email.success}
                />

                <div className="grid grid-cols-2 gap-4">
                  <FormInput
                    label="Create password"
                    type="password"
                    value={formFields.password.value}
                    onChange={(value) => updateField('password', value)}
                    placeholder="Strong password"
                    error={formFields.password.error}
                    success={formFields.password.success}
                  />

                  <FormInput
                    label="Confirm password"
                    type="password"
                    value={formFields.confirmPassword.value}
                    onChange={(value) => updateField('confirmPassword', value)}
                    placeholder="Confirm password"
                    error={formFields.confirmPassword.error}
                    success={formFields.confirmPassword.success}
                  />
                </div>

                {/* Submit and Skip buttons */}
                <div className="space-y-4">
                  <motion.button
                    whileHover={{ scale: isFormValid ? 1.02 : 1 }}
                    whileTap={{ scale: isFormValid ? 0.98 : 1 }}
                    type="submit"
                    disabled={!isFormValid || isSubmitting}
                    className="w-full py-5 rounded-xl font-bold text-white transition-all duration-300 shadow-lg hover:shadow-xl text-lg"
                    style={{
                      background: isFormValid 
                        ? `linear-gradient(135deg, #FF4B00, #A37AF9)` 
                        : '#E5E5E5',
                      cursor: isFormValid ? 'pointer' : 'not-allowed'
                    }}
                  >
                    {isSubmitting ? (
                      <div className="flex items-center justify-center gap-2">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        <span>Creating your wealth account...</span>
                      </div>
                    ) : (
                      <div className="flex items-center justify-center gap-2">
                        <span>Start building wealth today!</span>
                      </div>
                    )}
                  </motion.button>

                  <motion.button
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    type="button"
                    onClick={onComplete}
                    className="w-full py-4 rounded-xl font-medium transition-all duration-300 border-2"
                    style={{
                      background: 'white',
                      borderColor: '#6828E8',
                      color: '#6828E8'
                    }}
                  >
                    Skip for now - Let's build wealth!
                  </motion.button>
                </div>

                {/* Social signup */}
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <div className="w-full border-t border-gray-200"></div>
                  </div>
                  <div className="relative flex justify-center text-sm">
                    <span className="px-4 bg-white text-gray-500">or continue with</span>
                  </div>
                </div>

                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  type="button"
                  className="w-full py-4 border-2 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center gap-3"
                  style={{ 
                    borderColor: "#6828E8" + "30",
                    color: "#064345",
                    backgroundColor: "white"
                  }}
                >
                  <div className="w-5 h-5 rounded"></div>
                  <span>Continue with Google</span>
                </motion.button>

                {/* Terms */}
                <p className="text-xs text-center leading-relaxed" style={{ color: "#064345" }}>
                  By creating an account, you agree to our{" "}
                  <a href="#" className="underline font-medium hover:opacity-80" style={{ color: "#6828E8" }}>
                    Terms of Service
                  </a>{" "}
                  and{" "}
                  <a href="#" className="underline font-medium hover:opacity-80" style={{ color: "#6828E8" }}>
                    Privacy Policy
                  </a>
                  . We're committed to keeping your data secure and never sharing it without your permission.
                </p>
              </form>

              {/* Login link */}
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
                className="text-center mt-8"
              >
                <p style={{ color: "#064345" }}>
                  Already building wealth with us?{" "}
                  <a href="#" className="font-bold underline hover:opacity-80" style={{ color: "#FF4B00" }}>
                    Sign in here
                  </a>
                </p>
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </div>
  );
}