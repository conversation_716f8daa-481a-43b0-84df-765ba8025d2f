import { useState } from "react";
import { PiggyBank, Plus } from "lucide-react";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "./ui/dialog";

interface CumulativeSavingsPoolProps {
  totalSavings: number;
  onContributeSavings: (amount: number) => void;
}

export function CumulativeSavingsPool({ totalSavings, onContributeSavings }: CumulativeSavingsPoolProps) {
  const [showContribute, setShowContribute] = useState(false);
  const [contributeAmount, setContributeAmount] = useState("");

  const handleContribute = () => {
    const amount = parseFloat(contributeAmount);
    if (amount && amount > 0) {
      onContributeSavings(amount);
      setContributeAmount("");
      setShowContribute(false);
    }
  };

  return (
    <Card className="p-6 bg-white">
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-bold text-gray-800">Cumulative Savings Pool</h3>
        <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
          <PiggyBank className="h-5 w-5 text-green-600" />
        </div>
      </div>

      <div className="mb-6">
        <p className="text-3xl font-bold text-green-600 mb-1">
          ${totalSavings.toLocaleString()}
        </p>
        <p className="text-gray-600">Total accumulated wealth</p>
      </div>

      <Dialog open={showContribute} onOpenChange={setShowContribute}>
        <DialogTrigger asChild>
          <Button className="w-full bg-green-600 hover:bg-green-700">
            <Plus className="h-4 w-4 mr-2" />
            Contribute Savings
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Contribute to Savings Pool</DialogTitle>
            <DialogDescription>
              Add money to your cumulative savings pool to track your total accumulated wealth.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Contribution Amount
              </label>
              <Input
                type="number"
                placeholder="Enter amount to contribute"
                value={contributeAmount}
                onChange={(e) => setContributeAmount(e.target.value)}
                min="0"
                step="0.01"
              />
            </div>
            <div className="flex gap-3 pt-4">
              <Button 
                onClick={handleContribute}
                disabled={!contributeAmount || parseFloat(contributeAmount) <= 0}
                className="bg-green-600 hover:bg-green-700 flex-1"
              >
                Contribute ${contributeAmount || "0"}
              </Button>
              <Button 
                variant="outline"
                onClick={() => setShowContribute(false)}
              >
                Cancel
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </Card>
  );
}