import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "motion/react";
import { 
  CreditCard, 
  ArrowLeft, 
  ArrowRight, 
  Target, 
  AlertCircle, 
  CheckCircle, 
  Calendar, 
  DollarSign,
  TrendingDown,
  Plus,
  Minus,
  Info,
  TrendingUp,
  Clock,
  Trash2,
  Edit,
  ExternalLink,
  Calculator,
  Zap
} from "lucide-react";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Progress } from "./ui/progress";
import { Alert, AlertDescription } from "./ui/alert";
import { Separator } from "./ui/separator";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Badge } from "./ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "./ui/dialog";
import obieLogo from 'figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png';

interface FinancialData {
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlySavings: number;
  monthlyInvestment: number;
  availableFromIncome: number;
  availableCashSavings: number;
}

interface Debt {
  id: string;
  name: string;
  totalAmount: number;
  startDate: string;
  remainingBalance: number;
  interestRate: number;
  monthlyPayment: number;
  dueDate: number; // day of month (1-31)
  extraPayment: number;
  estimatedPayoffDate: string;
  lastPaymentDate?: string;
}

interface DebtData {
  debts: Debt[];
  totalDebt: number;
  totalMonthlyPayments: number;
  payoffStrategy: 'avalanche' | 'snowball';
  extraPaymentAllocation: number;
  estimatedDebtFreeDate: string;
}

interface DebtManagerProps {
  financialData: FinancialData;
  onComplete: (data: DebtData) => void;
  onBack: () => void;
  onTabNavigation?: (tab: "optimise" | "maximise" | "protect") => void;
  initialData?: DebtData | null;
}

interface NewDebt {
  name: string;
  totalAmount: string;
  startDate: string;
  remainingBalance: string;
  interestRate: string;
  monthlyPayment: string;
  dueDate: string;
}

// Helper functions
const calculatePayoffDate = (balance: number, monthlyPayment: number, interestRate: number): string => {
  if (monthlyPayment <= 0) return 'Never';
  
  const monthlyInterestRate = interestRate / 100 / 12;
  if (monthlyInterestRate === 0) {
    const months = Math.ceil(balance / monthlyPayment);
    const payoffDate = new Date();
    payoffDate.setMonth(payoffDate.getMonth() + months);
    return payoffDate.toISOString().split('T')[0];
  }
  
  const months = Math.ceil(
    -Math.log(1 - (balance * monthlyInterestRate) / monthlyPayment) / 
    Math.log(1 + monthlyInterestRate)
  );
  
  const payoffDate = new Date();
  payoffDate.setMonth(payoffDate.getMonth() + months);
  return payoffDate.toISOString().split('T')[0];
};

const formatAmount = (amount: number) => `$${amount.toLocaleString()}`;
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', { 
    month: 'short', 
    year: 'numeric' 
  });
};

export function DebtManager({ financialData, onComplete, onBack, onTabNavigation, initialData }: DebtManagerProps) {
  const [currentStep, setCurrentStep] = useState(initialData ? 2 : 1);
  const [debts, setDebts] = useState<Debt[]>(initialData?.debts || []);
  const [payoffStrategy, setPayoffStrategy] = useState<'avalanche' | 'snowball'>(initialData?.payoffStrategy || 'avalanche');
  const [extraPaymentAllocation, setExtraPaymentAllocation] = useState(initialData?.extraPaymentAllocation || 0);
  const [newDebt, setNewDebt] = useState<NewDebt>({
    name: '',
    totalAmount: '',
    startDate: '',
    remainingBalance: '',
    interestRate: '',
    monthlyPayment: '',
    dueDate: '1'
  });
  const [showAddDebt, setShowAddDebt] = useState(false);
  const [showAddDebtDialog, setShowAddDebtDialog] = useState(false);
  const [hasVisitedStep2, setHasVisitedStep2] = useState(false);

  // Show form by default when first visiting step 2 and no debts exist (unless coming from existing data)
  useEffect(() => {
    if (currentStep === 2 && !hasVisitedStep2 && debts.length === 0 && !initialData) {
      setShowAddDebt(true);
      setHasVisitedStep2(true);
    }
  }, [currentStep, hasVisitedStep2, debts.length, initialData]);

  // Calculate totals
  const totalDebt = debts.reduce((sum, debt) => sum + debt.remainingBalance, 0);
  const totalMonthlyPayments = debts.reduce((sum, debt) => sum + debt.monthlyPayment + debt.extraPayment, 0);

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      onBack();
    }
  };

  const handleAddDebt = () => {
    if (!newDebt.name || !newDebt.remainingBalance || !newDebt.monthlyPayment) return;

    const debt: Debt = {
      id: Date.now().toString(),
      name: newDebt.name,
      totalAmount: parseFloat(newDebt.totalAmount) || parseFloat(newDebt.remainingBalance),
      startDate: newDebt.startDate || new Date().toISOString().split('T')[0],
      remainingBalance: parseFloat(newDebt.remainingBalance),
      interestRate: parseFloat(newDebt.interestRate) || 0,
      monthlyPayment: parseFloat(newDebt.monthlyPayment),
      dueDate: parseInt(newDebt.dueDate) || 1,
      extraPayment: 0,
      estimatedPayoffDate: calculatePayoffDate(
        parseFloat(newDebt.remainingBalance),
        parseFloat(newDebt.monthlyPayment),
        parseFloat(newDebt.interestRate) || 0
      )
    };

    setDebts([...debts, debt]);
    setNewDebt({
      name: '',
      totalAmount: '',
      startDate: '',
      remainingBalance: '',
      interestRate: '',
      monthlyPayment: '',
      dueDate: '1'
    });
    setShowAddDebt(false);
    setShowAddDebtDialog(false);
  };

  const handleRemoveDebt = (debtId: string) => {
    setDebts(debts.filter(debt => debt.id !== debtId));
  };

  const handleStrategyChange = (strategy: 'avalanche' | 'snowball') => {
    setPayoffStrategy(strategy);
    // Redistribute extra payments based on strategy
    redistributeExtraPayments(strategy);
  };

  const redistributeExtraPayments = (strategy: 'avalanche' | 'snowball') => {
    if (extraPaymentAllocation === 0 || debts.length === 0) return;

    // Sort debts based on strategy
    let sortedDebts = [...debts];
    if (strategy === 'avalanche') {
      sortedDebts.sort((a, b) => b.interestRate - a.interestRate);
    } else {
      sortedDebts.sort((a, b) => a.remainingBalance - b.remainingBalance);
    }

    // Reset all extra payments
    const updatedDebts = debts.map(debt => ({ ...debt, extraPayment: 0 }));
    
    // Allocate extra payment to the first debt in strategy order
    if (sortedDebts.length > 0) {
      const targetDebtId = sortedDebts[0].id;
      const debtIndex = updatedDebts.findIndex(debt => debt.id === targetDebtId);
      if (debtIndex !== -1) {
        updatedDebts[debtIndex].extraPayment = extraPaymentAllocation;
        updatedDebts[debtIndex].estimatedPayoffDate = calculatePayoffDate(
          updatedDebts[debtIndex].remainingBalance,
          updatedDebts[debtIndex].monthlyPayment + updatedDebts[debtIndex].extraPayment,
          updatedDebts[debtIndex].interestRate
        );
      }
    }

    setDebts(updatedDebts);
  };

  const handleComplete = () => {
    const estimatedDebtFreeDate = debts.length > 0 
      ? debts.reduce((latest, debt) => 
          new Date(debt.estimatedPayoffDate) > new Date(latest) 
            ? debt.estimatedPayoffDate 
            : latest, 
          debts[0].estimatedPayoffDate
        )
      : '';

    const debtData: DebtData = {
      debts,
      totalDebt,
      totalMonthlyPayments,
      payoffStrategy,
      extraPaymentAllocation,
      estimatedDebtFreeDate
    };

    onComplete(debtData);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 via-orange-50 to-yellow-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center gap-4">
            <img src={obieLogo} alt="Obiemoney Logo" className="h-10 w-auto" />
            <div>
              <h1 className="text-3xl font-bold text-red-600">Deal with your Debt</h1>
              <p className="text-gray-600">Step 3: Become debt-free and build wealth</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 bg-white/60 rounded-full px-4 py-2">
              <span className="text-sm font-medium text-gray-600">Step {currentStep} of 4</span>
            </div>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <div className="mb-8">
          <Progress value={(currentStep / 4) * 100} className="h-2 bg-white/60" />
          <div className="flex justify-between mt-2 text-sm text-gray-600">
            <span className={currentStep >= 1 ? 'text-red-600 font-medium' : ''}>Learn</span>
            <span className={currentStep >= 2 ? 'text-red-600 font-medium' : ''}>Add Debts</span>
            <span className={currentStep >= 3 ? 'text-red-600 font-medium' : ''}>Strategy</span>
            <span className={currentStep >= 4 ? 'text-red-600 font-medium' : ''}>Summary</span>
          </div>
        </div>

        <AnimatePresence mode="wait">
          {/* Step 1: Education */}
          {currentStep === 1 && (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-8"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-red-100 rounded-full mb-4">
                    <CreditCard className="h-8 w-8 text-red-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">
                    Your Journey to Financial Freedom
                  </h2>
                  <p className="text-gray-600 max-w-2xl mx-auto">
                    Debt doesn't define you, but eliminating it will transform your financial future. Let's create a strategic plan to become debt-free.
                  </p>
                </div>

                <div className="grid md:grid-cols-2 gap-6 mb-8">
                  <Card className="p-6 bg-gradient-to-br from-red-50 to-orange-50 border-red-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-red-100 rounded-full mb-4">
                        <AlertCircle className="h-6 w-6 text-red-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">The Cost of Debt</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        High-interest debt can cost you thousands in interest payments and prevent you from building wealth.
                      </p>
                      <div className="text-xs text-red-600 bg-red-50 rounded-lg p-2">
                        Average credit card APR: 25%+
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-green-100 rounded-full mb-4">
                        <TrendingUp className="h-6 w-6 text-green-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">The Power of Being Debt-Free</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Eliminate debt payments and redirect that money toward savings, investments, and your dreams.
                      </p>
                      <div className="text-xs text-green-600 bg-green-50 rounded-lg p-2">
                        More money for your future
                      </div>
                    </div>
                  </Card>
                </div>

                <div className="space-y-4 mb-8">
                  <h3 className="font-bold text-gray-800 text-center">Proven Debt Elimination Strategies</h3>
                  
                  <div className="grid md:grid-cols-2 gap-4">
                    <Card className="p-4 bg-blue-50 border-blue-200">
                      <div className="flex items-start gap-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                          <Calculator className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-800">Debt Avalanche</h4>
                          <p className="text-sm text-gray-600">Pay minimums on all debts, put extra money toward highest interest rate debt first. Saves more money mathematically.</p>
                        </div>
                      </div>
                    </Card>
                    
                    <Card className="p-4 bg-purple-50 border-purple-200">
                      <div className="flex items-start gap-3">
                        <div className="p-2 bg-purple-100 rounded-lg">
                          <Zap className="h-4 w-4 text-purple-600" />
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-800">Debt Snowball</h4>
                          <p className="text-sm text-gray-600">Pay minimums on all debts, put extra money toward smallest balance first. Builds momentum and motivation.</p>
                        </div>
                      </div>
                    </Card>
                  </div>
                </div>

                <Alert className="border-orange-200 bg-orange-50">
                  <Info className="h-4 w-4 text-orange-600" />
                  <AlertDescription className="text-orange-800">
                    <strong>Remember:</strong> The best debt payoff strategy is the one you'll stick with. We'll help you choose and stay motivated!
                  </AlertDescription>
                </Alert>
              </Card>
            </motion.div>
          )}

          {/* Step 2: Add Debts */}
          {currentStep === 2 && (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="flex justify-between items-center mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">Add Your Debts</h2>
                    <p className="text-gray-600">
                      List all your debts so we can create your personalized payoff plan.
                    </p>
                  </div>
                  {debts.length > 0 && !showAddDebt && (
                    <Dialog open={showAddDebtDialog} onOpenChange={setShowAddDebtDialog}>
                      <DialogTrigger asChild>
                        <Button className="bg-red-600 hover:bg-red-700">
                          <Plus size={16} className="mr-2" />
                          Add Debt
                        </Button>
                      </DialogTrigger>
                    </Dialog>
                  )}
                </div>

                {/* Existing Debts */}
                <div className="space-y-4 mb-6">
                  {debts.length > 0 && (
                    debts.map((debt) => (
                      <Card key={debt.id} className="p-6 bg-gradient-to-r from-red-50 to-orange-50 border-red-200">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <h3 className="font-bold text-gray-800">{debt.name}</h3>
                              <Badge variant="outline" className="text-red-600 border-red-300">
                                {debt.interestRate}% APR
                              </Badge>
                            </div>
                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                              <div>
                                <p className="text-gray-600">Balance</p>
                                <p className="font-bold text-red-600">{formatAmount(debt.remainingBalance)}</p>
                              </div>
                              <div>
                                <p className="text-gray-600">Monthly Payment</p>
                                <p className="font-bold">{formatAmount(debt.monthlyPayment)}</p>
                              </div>
                              <div>
                                <p className="text-gray-600">Due Date</p>
                                <p className="font-bold">{debt.dueDate}{debt.dueDate === 1 ? 'st' : debt.dueDate === 2 ? 'nd' : debt.dueDate === 3 ? 'rd' : 'th'}</p>
                              </div>
                              <div>
                                <p className="text-gray-600">Payoff Date</p>
                                <p className="font-bold text-green-600">{formatDate(debt.estimatedPayoffDate)}</p>
                              </div>
                            </div>
                          </div>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleRemoveDebt(debt.id)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 size={16} />
                          </Button>
                        </div>
                      </Card>
                    ))
                  )}
                  
                  {/* Empty State - only show if no debts and form is not visible */}
                  {debts.length === 0 && !showAddDebt && (
                    <Card className="p-8 text-center bg-gray-50">
                      <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-4">No debts added yet</p>
                      <Button 
                        onClick={() => setShowAddDebt(true)}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        Add Your First Debt
                      </Button>
                    </Card>
                  )}
                </div>

                {/* Add Debt Form - Show by default on first visit or when clicked */}
                {showAddDebt && (
                  <Card className="p-6 bg-blue-50 border-blue-200 mb-6">
                    <h3 className="font-bold text-gray-800 mb-4">Add New Debt</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Debt Name *
                          </label>
                          <Input
                            placeholder="e.g., Chase Credit Card"
                            value={newDebt.name}
                            onChange={(e) => setNewDebt({...newDebt, name: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Total Debt
                          </label>
                          <Input
                            type="number"
                            placeholder="5000"
                            value={newDebt.totalAmount}
                            onChange={(e) => setNewDebt({...newDebt, totalAmount: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Remaining Balance *
                          </label>
                          <Input
                            type="number"
                            placeholder="3500"
                            value={newDebt.remainingBalance}
                            onChange={(e) => setNewDebt({...newDebt, remainingBalance: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Debt Start Date
                          </label>
                          <Input
                            type="date"
                            placeholder="dd/mm/yyyy"
                            value={newDebt.startDate}
                            onChange={(e) => setNewDebt({...newDebt, startDate: e.target.value})}
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Interest Rate (APR)
                          </label>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="18.99"
                            value={newDebt.interestRate}
                            onChange={(e) => setNewDebt({...newDebt, interestRate: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Monthly Repayment Amount*
                          </label>
                          <Input
                            type="number"
                            placeholder="150"
                            value={newDebt.monthlyPayment}
                            onChange={(e) => setNewDebt({...newDebt, monthlyPayment: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Installment Due Date (Day of Month)
                          </label>
                          <Select value={newDebt.dueDate} onValueChange={(value) => setNewDebt({...newDebt, dueDate: value})}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {Array.from({length: 31}, (_, i) => i + 1).map(day => (
                                <SelectItem key={day} value={day.toString()}>{day}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-3 mt-6">
                      <Button 
                        onClick={handleAddDebt}
                        disabled={!newDebt.name || !newDebt.remainingBalance || !newDebt.monthlyPayment}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        Add Debt
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => setShowAddDebt(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </Card>
                )}

                {/* Add Debt Dialog */}
                <Dialog open={showAddDebtDialog} onOpenChange={setShowAddDebtDialog}>
                  <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                    <DialogHeader>
                      <DialogTitle className="text-xl font-bold text-gray-800">Add New Debt</DialogTitle>
                    </DialogHeader>
                    
                    <div className="grid md:grid-cols-2 gap-6 py-4">
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Debt Name *
                          </label>
                          <Input
                            placeholder="e.g., Chase Credit Card"
                            value={newDebt.name}
                            onChange={(e) => setNewDebt({...newDebt, name: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Total Debt
                          </label>
                          <Input
                            type="number"
                            placeholder="5000"
                            value={newDebt.totalAmount}
                            onChange={(e) => setNewDebt({...newDebt, totalAmount: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Remaining Balance *
                          </label>
                          <Input
                            type="number"
                            placeholder="3500"
                            value={newDebt.remainingBalance}
                            onChange={(e) => setNewDebt({...newDebt, remainingBalance: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Debt Start Date
                          </label>
                          <Input
                            type="date"
                            placeholder="dd/mm/yyyy"
                            value={newDebt.startDate}
                            onChange={(e) => setNewDebt({...newDebt, startDate: e.target.value})}
                          />
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Interest Rate (APR)
                          </label>
                          <Input
                            type="number"
                            step="0.01"
                            placeholder="18.99"
                            value={newDebt.interestRate}
                            onChange={(e) => setNewDebt({...newDebt, interestRate: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Monthly Repayment Amount *
                          </label>
                          <Input
                            type="number"
                            placeholder="150"
                            value={newDebt.monthlyPayment}
                            onChange={(e) => setNewDebt({...newDebt, monthlyPayment: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Installment Due Date (Day of Month)
                          </label>
                          <Select value={newDebt.dueDate} onValueChange={(value) => setNewDebt({...newDebt, dueDate: value})}>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {Array.from({length: 31}, (_, i) => i + 1).map(day => (
                                <SelectItem key={day} value={day.toString()}>{day}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>
                    
                    <DialogFooter>
                      <Button 
                        variant="outline"
                        onClick={() => setShowAddDebtDialog(false)}
                      >
                        Cancel
                      </Button>
                      <Button 
                        onClick={handleAddDebt}
                        disabled={!newDebt.name || !newDebt.remainingBalance || !newDebt.monthlyPayment}
                        className="bg-red-600 hover:bg-red-700"
                      >
                        Add Debt
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                {/* Summary */}
                {debts.length > 0 && (
                  <Card className="p-6 bg-gradient-to-r from-orange-50 to-yellow-50 border-orange-200">
                    <div className="grid md:grid-cols-3 gap-6 text-center">
                      <div>
                        <p className="text-sm text-gray-600">Total Debt</p>
                        <p className="text-2xl font-bold text-red-600">{formatAmount(totalDebt)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Monthly Payments</p>
                        <p className="text-2xl font-bold text-orange-600">{formatAmount(totalMonthlyPayments)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Number of Debts</p>
                        <p className="text-2xl font-bold text-blue-600">{debts.length}</p>
                      </div>
                    </div>
                  </Card>
                )}
              </Card>
            </motion.div>
          )}

          {/* Step 3: Strategy & Extra Payments */}
          {currentStep === 3 && (
            <motion.div
              key="step3"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-green-100 rounded-full mb-4">
                    <Target className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Choose Your Strategy</h2>
                  <p className="text-gray-600">
                    Select your debt payoff strategy and allocate extra payments to accelerate your progress.
                  </p>
                </div>

                <div className="space-y-8">
                  {/* Strategy Selection */}
                  <div>
                    <h3 className="font-bold text-gray-800 mb-4">Debt Payoff Strategy</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      <Card 
                        className={`p-6 cursor-pointer transition-all ${
                          payoffStrategy === 'avalanche' 
                            ? 'border-blue-500 bg-blue-50' 
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => handleStrategyChange('avalanche')}
                      >
                        <div className="flex items-center gap-3 mb-4">
                          <div className={`p-2 rounded-lg ${
                            payoffStrategy === 'avalanche' ? 'bg-blue-100' : 'bg-gray-100'
                          }`}>
                            <Calculator className={`h-5 w-5 ${
                              payoffStrategy === 'avalanche' ? 'text-blue-600' : 'text-gray-600'
                            }`} />
                          </div>
                          <div>
                            <h4 className="font-bold text-gray-800">Debt Avalanche</h4>
                            <Badge variant="outline" className="text-green-600 border-green-300">
                              Saves Most Money
                            </Badge>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600">
                          Focus on highest interest rate debt first. Mathematically optimal - saves the most money in interest payments.
                        </p>
                      </Card>

                      <Card 
                        className={`p-6 cursor-pointer transition-all ${
                          payoffStrategy === 'snowball' 
                            ? 'border-purple-500 bg-purple-50' 
                            : 'hover:bg-gray-50'
                        }`}
                        onClick={() => handleStrategyChange('snowball')}
                      >
                        <div className="flex items-center gap-3 mb-4">
                          <div className={`p-2 rounded-lg ${
                            payoffStrategy === 'snowball' ? 'bg-purple-100' : 'bg-gray-100'
                          }`}>
                            <Zap className={`h-5 w-5 ${
                              payoffStrategy === 'snowball' ? 'text-purple-600' : 'text-gray-600'
                            }`} />
                          </div>
                          <div>
                            <h4 className="font-bold text-gray-800">Debt Snowball</h4>
                            <Badge variant="outline" className="text-blue-600 border-blue-300">
                              Builds Momentum
                            </Badge>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600">
                          Focus on smallest debt first. Psychologically motivating - you'll see debts disappear faster.
                        </p>
                      </Card>
                    </div>
                  </div>

                  {/* Extra Payment Allocation */}
                  <div>
                    <h3 className="font-bold text-gray-800 mb-4">Extra Payment Allocation</h3>
                    <Card className="p-6 bg-green-50 border-green-200">
                      <div className="flex items-center justify-between mb-6">
                        <div className="flex items-center gap-3">
                          <DollarSign className="h-6 w-6 text-green-600" />
                          <div>
                            <h4 className="font-bold text-gray-800">Monthly Extra Payment</h4>
                            <p className="text-sm text-gray-600">Available: {formatAmount(financialData.availableFromIncome)}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="text-2xl font-bold text-green-600">{formatAmount(extraPaymentAllocation)}</p>
                          <p className="text-sm text-gray-600">Per month</p>
                        </div>
                      </div>
                      
                      <div className="space-y-4">
                        <Input
                          type="number"
                          placeholder="0"
                          value={extraPaymentAllocation}
                          onChange={(e) => setExtraPaymentAllocation(parseFloat(e.target.value) || 0)}
                          max={financialData.availableFromIncome}
                          className="text-lg font-medium"
                        />
                        
                        <div className="flex gap-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setExtraPaymentAllocation(Math.round(financialData.availableFromIncome * 0.25))}
                          >
                            25% of available
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setExtraPaymentAllocation(Math.round(financialData.availableFromIncome * 0.5))}
                          >
                            50% of available
                          </Button>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setExtraPaymentAllocation(Math.round(financialData.availableFromIncome))}
                          >
                            All available
                          </Button>
                        </div>
                      </div>
                    </Card>
                  </div>

                  {/* Debt Priority List */}
                  {debts.length > 0 && (
                    <div>
                      <h3 className="font-bold text-gray-800 mb-4">Your Debt Payoff Order</h3>
                      <div className="space-y-3">
                        {[...debts]
                          .sort((a, b) => payoffStrategy === 'avalanche' 
                            ? b.interestRate - a.interestRate 
                            : a.remainingBalance - b.remainingBalance
                          )
                          .map((debt, index) => (
                            <Card key={debt.id} className={`p-4 ${
                              index === 0 ? 'bg-gradient-to-r from-yellow-50 to-orange-50 border-orange-200' : 'bg-gray-50'
                            }`}>
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-3">
                                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                                    index === 0 ? 'bg-orange-500 text-white' : 'bg-gray-300 text-gray-600'
                                  }`}>
                                    {index + 1}
                                  </div>
                                  <div>
                                    <h4 className="font-bold text-gray-800">{debt.name}</h4>
                                    <p className="text-sm text-gray-600">
                                      {formatAmount(debt.remainingBalance)} at {debt.interestRate}% APR
                                    </p>
                                  </div>
                                </div>
                                <div className="text-right">
                                  <p className="font-bold text-gray-800">
                                    {formatAmount(debt.monthlyPayment + (index === 0 ? extraPaymentAllocation : 0))}
                                  </p>
                                  <p className="text-sm text-gray-600">Monthly payment</p>
                                </div>
                              </div>
                            </Card>
                          ))
                        }
                      </div>
                    </div>
                  )}
                </div>
              </Card>
            </motion.div>
          )}

          {/* Step 4: Summary */}
          {currentStep === 4 && (
            <motion.div
              key="step4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-green-100 rounded-full mb-4">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Your Debt Freedom Plan</h2>
                  <p className="text-gray-600">
                    Review your personalized debt elimination strategy and start your journey to financial freedom.
                  </p>
                </div>

                <div className="space-y-6">
                  {/* Strategy Summary */}
                  <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <Target className="h-5 w-5 text-blue-600" />
                      Your Strategy: {payoffStrategy === 'avalanche' ? 'Debt Avalanche' : 'Debt Snowball'}
                    </h3>
                    <p className="text-gray-700">
                      {payoffStrategy === 'avalanche' 
                        ? 'You\'re focusing on high-interest debt first to minimize total interest paid.'
                        : 'You\'re focusing on smallest balances first to build momentum and motivation.'
                      }
                    </p>
                  </Card>

                  {/* Financial Summary */}
                  <Card className="p-6 bg-gradient-to-r from-red-50 to-orange-50 border-red-200">
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <DollarSign className="h-5 w-5 text-red-600" />
                      Financial Overview
                    </h3>
                    <div className="grid md:grid-cols-4 gap-4 text-center">
                      <div>
                        <p className="text-sm text-gray-600">Total Debt</p>
                        <p className="text-2xl font-bold text-red-600">{formatAmount(totalDebt)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Monthly Payments</p>
                        <p className="text-2xl font-bold text-orange-600">{formatAmount(totalMonthlyPayments)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Extra Payment</p>
                        <p className="text-2xl font-bold text-green-600">{formatAmount(extraPaymentAllocation)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Number of Debts</p>
                        <p className="text-2xl font-bold text-blue-600">{debts.length}</p>
                      </div>
                    </div>
                  </Card>

                  {/* Timeline */}
                  {debts.length > 0 && (
                    <Card className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
                      <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                        <Calendar className="h-5 w-5 text-green-600" />
                        Debt Freedom Timeline
                      </h3>
                      <div className="space-y-4">
                        {[...debts]
                          .sort((a, b) => payoffStrategy === 'avalanche' 
                            ? b.interestRate - a.interestRate 
                            : a.remainingBalance - b.remainingBalance
                          )
                          .map((debt, index) => (
                            <div key={debt.id} className="flex items-center justify-between p-4 bg-white/60 rounded-lg">
                              <div className="flex items-center gap-3">
                                <div className="w-6 h-6 rounded-full bg-green-500 text-white text-xs flex items-center justify-center font-bold">
                                  {index + 1}
                                </div>
                                <div>
                                  <p className="font-medium text-gray-800">{debt.name}</p>
                                  <p className="text-sm text-gray-600">{formatAmount(debt.remainingBalance)} balance</p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-green-600">{formatDate(debt.estimatedPayoffDate)}</p>
                                <p className="text-sm text-gray-600">Payoff date</p>
                              </div>
                            </div>
                          ))
                        }
                      </div>
                      
                      <Separator className="my-4" />
                      
                      <div className="text-center">
                        <p className="text-sm text-gray-600">Estimated Debt-Free Date</p>
                        <p className="text-2xl font-bold text-green-600">
                          {debts.length > 0 ? formatDate(
                            debts.reduce((latest, debt) => 
                              new Date(debt.estimatedPayoffDate) > new Date(latest) 
                                ? debt.estimatedPayoffDate 
                                : latest, 
                              debts[0].estimatedPayoffDate
                            )
                          ) : 'N/A'}
                        </p>
                      </div>
                    </Card>
                  )}

                  {/* Next Steps */}
                  <Alert className="border-blue-200 bg-blue-50">
                    <Info className="h-4 w-4 text-blue-600" />
                    <AlertDescription className="text-blue-800">
                      <strong>Next Steps:</strong> Set up automatic payments, track your progress monthly, and celebrate each debt you eliminate. You've got this! 💪
                    </AlertDescription>
                  </Alert>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8">
          <Button
            variant="outline"
            onClick={handleBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {currentStep === 1 ? 'Back to Dashboard' : 'Back'}
          </Button>

          <div className="flex items-center gap-4">
            {currentStep < 4 ? (
              <Button
                onClick={handleNext}
                disabled={currentStep === 2 && debts.length === 0}
                className="flex items-center gap-2 bg-red-600 hover:bg-red-700"
              >
                Continue
                <ArrowRight className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={handleComplete}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              >
                Complete Debt Plan
                <CheckCircle className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}