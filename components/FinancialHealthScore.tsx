import { AlertCircle, TrendingUp, TrendingDown } from "lucide-react";
import { Card } from "./ui/card";

interface FinancialHealthScoreProps {
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlySavings: number;
  monthlyInvestment: number;
}

export function FinancialHealthScore({ 
  monthlyIncome, 
  monthlyExpenses, 
  monthlySavings, 
  monthlyInvestment 
}: FinancialHealthScoreProps) {
  const totalSavings = monthlySavings + monthlyInvestment;
  const savingsRate = monthlyIncome > 0 ? Math.round((totalSavings / monthlyIncome) * 100) : 0;
  const incomeUtilization = monthlyIncome > 0 ? Math.round((monthlyExpenses / monthlyIncome) * 100) : 0;
  
  const isOverBudget = monthlyExpenses > monthlyIncome;
  const budgetStatus = isOverBudget ? "Over Budget" : "Within Budget";
  const budgetStatusColor = isOverBudget ? "text-red-600" : "text-green-600";

  const getScoreColor = (score: number) => {
    if (score >= 20) return "text-green-600";
    if (score >= 10) return "text-yellow-600";
    return "text-red-600";
  };

  const getUtilizationColor = (utilization: number) => {
    if (utilization <= 70) return "text-green-600";
    if (utilization <= 90) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <Card className="p-6 bg-white">
      <div className="text-center mb-6">
        <h3 className="font-bold text-gray-800 mb-4">Financial Health Score</h3>
        
        <div className="mb-6">
          <p className={`text-6xl font-bold mb-2 ${getScoreColor(savingsRate)}`}>
            {savingsRate}
          </p>
          <p className="text-gray-600">Savings Rate</p>
        </div>
      </div>

      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <span className="text-gray-600">Income Utilization</span>
          <span className={`font-medium ${getUtilizationColor(incomeUtilization)}`}>
            {incomeUtilization}%
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-gray-600">Savings Rate</span>
          <span className={`font-medium ${getScoreColor(savingsRate)}`}>
            {savingsRate}%
          </span>
        </div>

        <div className="flex items-center justify-between">
          <span className="text-gray-600">Budget Status</span>
          <div className="flex items-center gap-1">
            {isOverBudget ? (
              <TrendingUp className="h-4 w-4 text-red-600" />
            ) : (
              <TrendingDown className="h-4 w-4 text-green-600" />
            )}
            <span className={`font-medium ${budgetStatusColor}`}>
              {budgetStatus}
            </span>
          </div>
        </div>
      </div>

      {savingsRate < 10 && (
        <div className="mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
          <div className="flex items-start gap-2">
            <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
            <div>
              <p className="text-sm font-medium text-yellow-800">Improve Your Score</p>
              <p className="text-xs text-yellow-700">
                Aim for at least 10% savings rate to build financial resilience
              </p>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
}