import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { ChevronRight, ChevronLeft, DollarSign, Calendar, CheckCircle, PiggyBank } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import obieLogo from 'figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png';

interface IncomeData {
  amount: string;
  frequency: 'monthly' | 'weekly' | 'yearly' | '';
  savings?: string;
}

const frequencyOptions = [
  { value: 'monthly', label: 'Monthly', emoji: '📅' },
  { value: 'weekly', label: 'Weekly', emoji: '📆' },
  { value: 'yearly', label: 'Yearly', emoji: '🗓️' }
];

function FrequencyCard({ 
  option, 
  isSelected, 
  onSelect 
}: { 
  option: typeof frequencyOptions[0]; 
  isSelected: boolean; 
  onSelect: () => void;
}) {
  return (
    <motion.div
      whileHover={{ scale: 1.02, y: -2 }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="relative"
    >
      <Card 
        className={`p-6 cursor-pointer transition-all duration-300 border-2`}
        onClick={onSelect}
        style={{
          borderColor: isSelected ? '#A37AF9' : '#E5E5E5',
          backgroundColor: isSelected ? '#A37AF9' + '15' : 'white',
          minHeight: '100px'
        }}
      >
        <div className="flex flex-col items-center text-center gap-3">
          <div className="flex-shrink-0">
            <span className="text-3xl">{option.emoji}</span>
          </div>
          <div>
            <h3 className={`font-semibold text-base ${isSelected ? 'text-purple-700' : 'text-gray-800'}`}>
              {option.label}
            </h3>
          </div>
          {isSelected && (
            <div className="absolute top-4 right-4 w-5 h-5 rounded border-2 flex items-center justify-center" style={{
              backgroundColor: '#A37AF9'
            }}>
              <div className="w-2 h-2 bg-white rounded-sm" />
            </div>
          )}
        </div>
      </Card>
    </motion.div>
  );
}

export function BudgetModule({ onComplete }: { onComplete: (data: IncomeData) => void }) {
  const [currentStep, setCurrentStep] = useState(0);
  const [incomeData, setIncomeData] = useState<IncomeData>({
    amount: '',
    frequency: '',
    savings: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAmountChange = (value: string) => {
    // Remove any non-numeric characters except decimal point
    const numericValue = value.replace(/[^0-9.]/g, '');
    setIncomeData(prev => ({ ...prev, amount: numericValue }));
  };

  const handleFrequencySelect = (frequency: 'monthly' | 'weekly' | 'yearly') => {
    setIncomeData(prev => ({ ...prev, frequency }));
  };

  const handleSavingsChange = (value: string) => {
    // Remove any non-numeric characters except decimal point
    const numericValue = value.replace(/[^0-9.]/g, '');
    setIncomeData(prev => ({ ...prev, savings: numericValue }));
  };

  const handleNext = () => {
    if (currentStep === 0 && incomeData.amount && incomeData.frequency) {
      setCurrentStep(1);
    } else if (currentStep === 1 && incomeData.savings !== undefined) {
      handleSubmit();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    // Simulate processing
    await new Promise(resolve => setTimeout(resolve, 1500));
    setIsSubmitting(false);
    onComplete(incomeData);
  };

  const canProceed = currentStep === 0 ? (incomeData.amount && incomeData.frequency) : 
                     incomeData.savings !== undefined;

  const formatCurrency = (amount: string) => {
    if (!amount) return '';
    const num = parseFloat(amount);
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(num);
  };

  return (
    <div className="min-h-screen py-8" style={{ backgroundColor: '#F5F5F5' }}>
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex justify-between items-center mb-6">
            <Button
              variant="ghost"
              onClick={handlePrevious}
              disabled={currentStep === 0}
              className="flex items-center gap-2 text-gray-500"
            >
              <ChevronLeft size={20} />
              Back
            </Button>
            <span className="text-sm text-gray-500 font-medium">
              Stage 1: Optimise • Budget
            </span>
            <Button
              variant="outline"
              onClick={() => onComplete({ amount: '0', frequency: 'monthly' })}
              className="flex items-center gap-2 text-purple-600 border-purple-300 hover:bg-purple-50"
            >
              Skip Budget 
            </Button>
          </div>
          
          <div className="flex justify-center mb-6">
            <img 
              src={obieLogo} 
              alt="Obiemoney Logo" 
              className="h-10 w-auto"
            />
          </div>
          
          <div className="mb-4">
            <span className="px-4 py-2 rounded-full text-sm font-medium" style={{ 
              backgroundColor: '#A37AF9' + '20', 
              color: '#A37AF9' 
            }}>
              Step {currentStep + 1} of 2
            </span>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mb-8"
        >
          <div className="w-full bg-gray-200 rounded-full h-2">
            <motion.div
              initial={{ width: '50%' }}
              animate={{ 
                width: currentStep === 0 ? '50%' : '100%' 
              }}
              transition={{ duration: 0.5 }}
              className="h-2 rounded-full"
              style={{ backgroundColor: '#A37AF9' }}
            />
          </div>
        </motion.div>

        {/* Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.4 }}
            className="bg-white rounded-2xl shadow-sm p-12 mb-8"
          >
            {currentStep === 0 ? (
              // Combined Income Amount and Frequency Step - Single Row
              <div>
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-8 text-center"
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-6" style={{ backgroundColor: '#A37AF9' + '20' }}>
                    <DollarSign size={32} style={{ color: '#A37AF9' }} />
                  </div>
                  <h2 className="text-3xl font-bold mb-4" style={{ color: '#A37AF9' }}>
                    Tell us about your income
                  </h2>
                  <p className="text-gray-600 text-lg mb-8">
                    This helps us create a budget that works for your situation
                  </p>
                </motion.div>

                <div className="max-w-4xl mx-auto">
                  {/* Income Questions in Single Row */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                    {/* Income Amount Section */}
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 }}
                      className="space-y-4"
                    >
                      <h3 className="text-xl font-semibold text-gray-800 text-left">How much do you earn?</h3>
                      <div className="relative">
                        <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-2xl font-semibold text-gray-400">
                          $
                        </span>
                        <Input
                          type="text"
                          value={incomeData.amount}
                          onChange={(e) => handleAmountChange(e.target.value)}
                          placeholder="Enter your income"
                          className="pl-12 pr-4 py-6 border-2 rounded-xl"
                          style={{
                            borderColor: incomeData.amount ? '#A37AF9' : '#E5E5E5',
                            fontSize: '18px',
                            fontWeight: '600'
                          }}
                        />
                      </div>

                      {incomeData.amount && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="mt-4"
                        >
                          <p className="text-lg text-gray-600 text-left">
                            You entered: <span className="font-semibold" style={{ color: '#A37AF9' }}>
                              {formatCurrency(incomeData.amount)}
                            </span>
                          </p>
                        </motion.div>
                      )}
                    </motion.div>

                    {/* Pay Frequency Section */}
                    <motion.div
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 }}
                      className="space-y-4"
                    >
                      <h3 className="text-xl font-semibold text-gray-800 text-left">How often do you get paid?</h3>
                      <div>
                        <Select
                          value={incomeData.frequency}
                          onValueChange={(value) => handleFrequencySelect(value as 'monthly' | 'weekly' | 'yearly')}
                        >
                          <SelectTrigger 
                            className="w-full py-6 border-2 rounded-xl"
                            style={{
                              borderColor: incomeData.frequency ? '#A37AF9' : '#E5E5E5',
                              fontSize: '18px',
                              fontWeight: '600'
                            }}
                          >
                            <SelectValue placeholder="Select pay frequency" style={{ fontSize: '18px' }} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="weekly" className="py-3" style={{ fontSize: '18px' }}>
                              <div className="flex items-center gap-3">
                                {/* <span>📆</span> */}
                                <span>Weekly</span>
                              </div>
                            </SelectItem>
                            <SelectItem value="monthly" className="py-3" style={{ fontSize: '18px' }}>
                              <div className="flex items-center gap-3">
                                {/* <span>📅</span> */}
                                <span>Monthly</span>
                              </div>
                            </SelectItem>
                            <SelectItem value="yearly" className="py-3" style={{ fontSize: '18px' }}>
                              <div className="flex items-center gap-3">  
                                {/* <span>🗓️</span> */}
                                <span>Yearly</span>
                              </div>
                            </SelectItem>
                          </SelectContent>
                        </Select>

                        {incomeData.frequency && (
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            className="mt-4"
                          >
                            <p className="text-lg text-gray-600 text-left">
                              Pay frequency: <span className="font-semibold" style={{ color: '#A37AF9' }}>
                                {incomeData.frequency}
                              </span>
                            </p>
                          </motion.div>
                        )}
                      </div>
                    </motion.div>
                  </div>

                  {/* Next Button */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="text-center"
                  >
                    <Button
                      onClick={handleNext}
                      disabled={!canProceed}
                      className="px-12 py-3 text-lg font-semibold"
                      style={{
                        backgroundColor: canProceed ? '#A37AF9' : '#E5E5E5',
                        color: canProceed ? 'white' : '#999'
                      }}
                    >
                      Next
                      <ChevronRight size={20} className="ml-2" />
                    </Button>
                  </motion.div>
                </div>
              </div>
            ) : (
              // Savings Amount Step
              <div className="text-center">
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="mb-8"
                >
                  <div className="inline-flex items-center justify-center w-16 h-16 rounded-full mb-6" style={{ backgroundColor: '#A37AF9' + '20' }}>
                    <PiggyBank size={32} style={{ color: '#A37AF9' }} />
                  </div>
                  <h2 className="text-3xl font-bold mb-4" style={{ color: '#A37AF9' }}>
                    How much do you have in savings right now?
                  </h2>
                  <p className="text-gray-600 text-lg mb-8">
                    This helps us understand your current financial position
                  </p>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                  className="max-w-md mx-auto"
                >
                  <div className="relative">
                    <span className="absolute left-4 top-1/2 transform -translate-y-1/2 text-2xl font-semibold text-gray-400">
                      $
                    </span>
                    <Input
                      type="text"
                      value={incomeData.savings || ''}
                      onChange={(e) => handleSavingsChange(e.target.value)}
                      placeholder="Enter your savings amount"
                      className="pl-12 pr-4 py-6 text-center border-2 rounded-xl"
                      style={{
                        borderColor: incomeData.savings ? '#A37AF9' : '#E5E5E5',
                        fontSize: '18px',
                        fontWeight: '600'
                      }}
                    />
                  </div>

                  {incomeData.savings && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mt-4"
                    >
                      <p className="text-lg text-gray-600 text-left">
                        Current savings: <span className="font-semibold" style={{ color: '#A37AF9' }}>
                          {formatCurrency(incomeData.savings)}
                        </span>
                      </p>
                    </motion.div>
                  )}

                  <div className="text-center">
                    <Button
                      onClick={handleNext}
                      disabled={incomeData.savings === undefined || incomeData.savings === ''}
                      className="mt-8 px-12 py-3 text-lg font-semibold"
                      style={{
                        backgroundColor: incomeData.savings ? '#A37AF9' : '#E5E5E5',
                        color: incomeData.savings ? 'white' : '#999'
                      }}
                    >
                      Complete Setup
                      <ChevronRight size={20} className="ml-2" />
                    </Button>
                  </div>
                </motion.div>

                {isSubmitting && (
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="mt-8 p-6 rounded-xl" style={{ backgroundColor: '#A37AF9' + '10' }}
                  >
                    <div className="flex items-center justify-center gap-3">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2" style={{ borderColor: '#A37AF9' }}></div>
                      <p className="font-semibold" style={{ color: '#A37AF9' }}>
                        Setting up your budget...
                      </p>
                    </div>
                  </motion.div>
                )}
              </div>
            )}
          </motion.div>
        </AnimatePresence>


      </div>
    </div>
  );
}