import { useState } from "react";
import { motion } from "motion/react";
import { ChevronLeft, Edit, PiggyBank, TrendingUp, Home, Shield, Coffee, CreditCard, Sparkles, Target, Zap } from "lucide-react";
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Card } from "./ui/card";

interface IncomeData {
  amount: string;
  frequency: 'monthly' | 'weekly' | 'yearly' | '';
}

interface OptimizationPlanProps {
  incomeData: IncomeData;
  onComplete: (data: IncomeData) => void;
  onBack: () => void;
}

// Predefined expense categories with suggested percentages and icons
const expenseCategories = [
  { name: 'Living Expenses', percentage: 55, color: '#FF6B47', icon: Home, description: 'Rent, utilities, groceries', gradient: 'from-orange-400 to-red-500' },
  { name: 'Medical & Insurance', percentage: 5, color: '#FF6B47', icon: Shield, description: 'Health & protection', gradient: 'from-blue-400 to-blue-600' },
  { name: 'Fun & Entertainment', percentage: 10, color: '#FF6B47', icon: Coffee, description: 'Leisure & lifestyle', gradient: 'from-purple-400 to-pink-500' },
  { name: 'Debt Repayments', percentage: 0, color: '#FF6B47', icon: CreditCard, description: 'Loans & credit cards', gradient: 'from-red-400 to-red-600' }
];

const savingsCategories = [
  { name: 'Savings', percentage: 20, color: '#10B981', icon: PiggyBank, description: 'Emergency fund & goals', gradient: 'from-green-400 to-emerald-500' },
  { name: 'Investment', percentage: 10, color: '#10B981', icon: TrendingUp, description: 'Wealth building', gradient: 'from-emerald-400 to-teal-500' }
];

function EnhancedCircularProgress({ percentage, color, size = 120, strokeWidth = 8 }: { percentage: number; color: string; size?: number; strokeWidth?: number }) {
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className="relative flex items-center justify-center" style={{ width: size, height: size }}>
      <svg width={size} height={size} className="transform -rotate-90">
        {/* Background circle */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke="rgba(255,255,255,0.1)"
          strokeWidth={strokeWidth}
          fill="transparent"
        />
        {/* Progress circle */}
        <motion.circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          initial={{ strokeDashoffset: circumference }}
          animate={{ strokeDashoffset }}
          transition={{ duration: 2, ease: "easeOut", delay: 0.5 }}
          style={{
            filter: `drop-shadow(0 0 8px ${color}40)`
          }}
        />
        {/* Glow effect */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth * 0.5}
          fill="transparent"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          opacity="0.3"
          style={{
            filter: `blur(4px)`
          }}
        />
      </svg>
      
      <div className="absolute inset-0 flex flex-col items-center justify-center">
        <motion.span 
          className="text-2xl font-bold"
          style={{ color }}
          initial={{ opacity: 0, scale: 0.5 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 1 }}
        >
          {percentage}%
        </motion.span>
      </div>
    </div>
  );
}

function CreativeCategoryItem({ 
  name, 
  amount, 
  percentage, 
  color,
  icon: Icon,
  description,
  gradient,
  index
}: { 
  name: string; 
  amount: number; 
  percentage: number; 
  color: string;
  icon: any;
  description: string;
  gradient: string;
  index: number;
}) {
  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      whileHover={{ scale: 1.02, y: -2 }}
      className="group relative p-5 rounded-2xl bg-white/80 backdrop-blur-sm border border-white/20 hover:bg-white/90 transition-all duration-300 shadow-lg hover:shadow-xl"
      style={{
        background: `linear-gradient(135deg, ${color}08, ${color}03)`
      }}
    >
      {/* Animated background gradient on hover */}
      <div 
        className={`absolute inset-0 bg-gradient-to-r ${gradient} opacity-0 group-hover:opacity-5 rounded-2xl transition-opacity duration-300`}
      />
      
      <div className="relative flex items-center gap-4">
        <motion.div 
          className="relative p-4 rounded-2xl flex-shrink-0 overflow-hidden"
          style={{ backgroundColor: `${color}15` }}
          whileHover={{ rotate: 5 }}
          transition={{ duration: 0.3 }}
        >
          <div 
            className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-10`}
          />
          <Icon size={24} style={{ color }} className="relative z-10" />
          
          {/* Sparkle effect on hover */}
          <motion.div
            className="absolute top-1 right-1 opacity-0 group-hover:opacity-100"
            initial={{ scale: 0 }}
            whileHover={{ scale: 1 }}
            transition={{ duration: 0.3 }}
          >
            <Sparkles size={12} style={{ color }} />
          </motion.div>
        </motion.div>
        
        <div className="flex-1 min-w-0">
          <h4 className="font-semibold text-gray-900 text-left group-hover:text-gray-800 transition-colors">
            {name}
          </h4>
          <p className="text-sm text-gray-600 text-left group-hover:text-gray-700 transition-colors">
            {description}
          </p>
        </div>
        
        <div className="text-right flex-shrink-0">
          <motion.p 
            className="text-xl font-bold text-gray-900 group-hover:text-gray-800 transition-colors"
            whileHover={{ scale: 1.05 }}
          >
            ${amount.toLocaleString()}
          </motion.p>
          <motion.div 
            className="inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium mt-1 transition-all duration-300"
            style={{ 
              backgroundColor: `${color}20`, 
              color: color 
            }}
            whileHover={{
              backgroundColor: `${color}30`,
              scale: 1.05
            }}
          >
            <span>{percentage}%</span>
          </motion.div>
        </div>
      </div>
    </motion.div>
  );
}

export function OptimizationPlan({ incomeData, onComplete, onBack }: OptimizationPlanProps) {
  const [isEditingIncome, setIsEditingIncome] = useState(false);
  const [editedIncome, setEditedIncome] = useState(incomeData.amount);

  // Convert income to monthly amount
  const getMonthlyIncome = () => {
    const amount = parseFloat(incomeData.amount) || 0;
    if (incomeData.frequency === 'weekly') return amount * 4.33;
    if (incomeData.frequency === 'yearly') return amount / 12;
    return amount;
  };

  const monthlyIncome = getMonthlyIncome();

  // Calculate amounts based on percentages
  const expenseAmounts = expenseCategories.map(category => ({
    ...category,
    amount: Math.round(monthlyIncome * (category.percentage / 100))
  }));

  const savingsAmounts = savingsCategories.map(category => ({
    ...category,
    amount: Math.round(monthlyIncome * (category.percentage / 100))
  }));

  const totalExpensePercentage = expenseCategories.reduce((sum, cat) => sum + cat.percentage, 0);
  const totalSavingsPercentage = savingsCategories.reduce((sum, cat) => sum + cat.percentage, 0);
  const totalExpenseAmount = expenseAmounts.reduce((sum, cat) => sum + cat.amount, 0);
  const totalSavingsAmount = savingsAmounts.reduce((sum, cat) => sum + cat.amount, 0);

  const handleIncomeEdit = () => {
    if (isEditingIncome) {
      const updatedIncomeData = { ...incomeData, amount: editedIncome };
      onComplete(updatedIncomeData);
    }
    setIsEditingIncome(!isEditingIncome);
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Animated background */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-50 via-blue-50 to-emerald-50">
        <div className="absolute inset-0 bg-gradient-to-tr from-purple-100/20 via-transparent to-emerald-100/20" />
        
        {/* Floating geometric shapes */}
        <motion.div
          className="absolute top-20 left-20 w-32 h-32 bg-gradient-to-br from-purple-200/30 to-blue-200/30 rounded-full blur-xl"
          animate={{ 
            x: [0, 30, 0],
            y: [0, -20, 0],
            scale: [1, 1.1, 1]
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute top-40 right-32 w-24 h-24 bg-gradient-to-br from-emerald-200/30 to-teal-200/30 rounded-full blur-xl"
          animate={{ 
            x: [0, -25, 0],
            y: [0, 15, 0],
            scale: [1, 0.9, 1]
          }}
          transition={{ duration: 6, repeat: Infinity, ease: "easeInOut", delay: 1 }}
        />
        <motion.div
          className="absolute bottom-32 left-1/3 w-20 h-20 bg-gradient-to-br from-orange-200/30 to-red-200/30 rounded-full blur-xl"
          animate={{ 
            x: [0, 20, 0],
            y: [0, -10, 0],
            scale: [1, 1.2, 1]
          }}
          transition={{ duration: 7, repeat: Infinity, ease: "easeInOut", delay: 2 }}
        />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-6 py-12">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="relative"
          >

            
            <h1 className="text-5xl font-bold mb-6 bg-gradient-to-r from-purple-600 via-blue-600 to-emerald-600 bg-clip-text text-transparent">
              Optimise Your Monthly Income
            </h1>
            <p className="text-gray-600 text-xl max-w-3xl mx-auto leading-relaxed">
              Transform your financial future with our intelligent allocation strategy. 
              <br />
              <span className="text-purple-600 font-medium">Every dollar has a purpose, every goal within reach.</span>
            </p>
          </motion.div>
        </motion.div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          {/* Living Expenses Card */}
          <motion.div
            initial={{ opacity: 0, x: -40 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
            className="relative"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-orange-100/50 to-red-100/50 rounded-3xl blur-sm" />
            <Card className="relative p-8 bg-white/90 backdrop-blur-sm border-0 shadow-2xl rounded-3xl overflow-hidden">
              {/* Subtle pattern overlay */}
              <div className="absolute inset-0 opacity-5" style={{
                backgroundImage: `radial-gradient(circle at 1px 1px, #FF6B47 1px, transparent 0)`,
                backgroundSize: '20px 20px'
              }} />
              
              <div className="relative flex items-center justify-between mb-2">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 text-left">Living Expenses</h2>
                  <p className="text-gray-600 text-left">Essential monthly costs</p>
                </div>
                <div className="text-right">
                  <EnhancedCircularProgress 
                    percentage={totalExpensePercentage} 
                    color="#FF6B47" 
                    size={100}
                  />
                </div>
              </div>
              
              <motion.div 
                className="mb-2"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
              >
                <div className="flex items-baseline gap-3 mb-3">
                  <span className="text-4xl font-bold text-gray-900">
                    ${totalExpenseAmount.toLocaleString()}
                  </span>
                  <span className="text-lg text-gray-600">per month</span>
                </div>
                <div className="h-3 bg-gray-100 rounded-full overflow-hidden shadow-inner">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${totalExpensePercentage}%` }}
                    transition={{ duration: 2, delay: 0.8, ease: "easeOut" }}
                    className="h-full bg-gradient-to-r from-orange-400 via-orange-500 to-red-500 rounded-full shadow-sm"
                    style={{
                      filter: 'drop-shadow(0 2px 4px rgba(255, 107, 71, 0.3))'
                    }}
                  />
                </div>
              </motion.div>
              
              <div className="space-y-4">
                {expenseAmounts.map((category, index) => (
                  <CreativeCategoryItem key={index} {...category} index={index} />
                ))}
              </div>
            </Card>
          </motion.div>

          {/* Savings & Investment Card */}
          <motion.div
            initial={{ opacity: 0, x: 40 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5, duration: 0.8 }}
            className="relative"
          >

            <Card className="relative p-8 bg-white/90 backdrop-blur-sm border-0 shadow-2xl rounded-3xl overflow-hidden">
              {/* Subtle pattern overlay */}
              <div className="absolute inset-0 opacity-5" style={{
                backgroundImage: `radial-gradient(circle at 1px 1px, #10B981 1px, transparent 0)`,
                backgroundSize: '20px 20px'
              }} />
              
              <div className="relative flex items-center justify-between mb-2">
                <div>
                  <h2 className="text-2xl font-bold text-gray-900 text-left">Savings & Investment</h2>
                  <p className="text-gray-600 text-left">Building your wealth</p>
                </div>
                <div className="text-right">
                  <EnhancedCircularProgress 
                    percentage={totalSavingsPercentage} 
                    color="#10B981" 
                    size={100}
                  />
                </div>
              </div>
              
              <motion.div 
                className="mb-2"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
              >
                <div className="flex items-baseline gap-3 mb-3">
                  <span className="text-4xl font-bold text-gray-900">
                    ${totalSavingsAmount.toLocaleString()}
                  </span>
                  <span className="text-lg text-gray-600">per month</span>
                </div>
                <div className="h-3 bg-gray-100 rounded-full overflow-hidden shadow-inner">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: `${totalSavingsPercentage}%` }}
                    transition={{ duration: 2, delay: 0.9, ease: "easeOut" }}
                    className="h-full bg-gradient-to-r from-emerald-400 via-emerald-500 to-teal-500 rounded-full shadow-sm"
                    style={{
                      filter: 'drop-shadow(0 2px 4px rgba(16, 185, 129, 0.3))'
                    }}
                  />
                </div>
              </motion.div>
              
              <div className="space-y-4">
                {savingsAmounts.map((category, index) => (
                  <CreativeCategoryItem key={index} {...category} index={index + 4} />
                ))}
              </div>
            </Card>
          </motion.div>
        </div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2 }}
          className="flex justify-between items-center"
        >
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              variant="outline"
              onClick={onBack}
              className="flex items-center gap-3 px-8 py-4 border-2 border-gray-300 text-gray-700 hover:bg-gray-50 rounded-2xl text-lg font-medium shadow-lg hover:shadow-xl transition-all duration-300"
            >
              <ChevronLeft size={20} />
              Back
            </Button>
          </motion.div>
          
          <motion.div whileHover={{ scale: 1.02 }} whileTap={{ scale: 0.98 }}>
            <Button
              onClick={() => onComplete(incomeData)}
              className="relative px-12 py-4 text-white rounded-2xl text-lg font-medium shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden"
              style={{ 
                background: 'linear-gradient(135deg, #A37AF9 0%, #7C3AED 50%, #6366F1 100%)'
              }}
            >
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0"
                initial={{ x: '-100%' }}
                whileHover={{ x: '100%' }}
                transition={{ duration: 0.6 }}
              />
              <span className="relative z-10 flex items-center gap-2">
                Continue to Expenses
                <motion.div
                  animate={{ x: [0, 4, 0] }}
                  transition={{ duration: 1.5, repeat: Infinity }}
                >
                  →
                </motion.div>
              </span>
            </Button>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}