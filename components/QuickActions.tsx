import { Calendar, Target, Bell, FileText, Settings, TrendingUp } from "lucide-react";
import { Card } from "./ui/card";
import { Button } from "./ui/button";

interface QuickActionsProps {
  onViewMonthlyReport?: () => void;
  onSetFinancialGoals?: () => void;
  onManageReminders?: () => void;
}

export function QuickActions({ 
  onViewMonthlyReport,
  onSetFinancialGoals,
  onManageReminders
}: QuickActionsProps) {
  const quickActionItems = [
    {
      id: "monthly-report",
      icon: Calendar,
      label: "View Monthly Report",
      onClick: onViewMonthlyReport || (() => console.log("Monthly report clicked"))
    },
    {
      id: "financial-goals",
      icon: Target,
      label: "Set Financial Goals",
      onClick: onSetFinancialGoals || (() => console.log("Financial goals clicked"))
    },
    {
      id: "reminders",
      icon: Bell,
      label: "Manage Reminders",
      onClick: onManageReminders || (() => console.log("Manage reminders clicked"))
    }
  ];

  return (
    <Card className="p-6 bg-white">
      <h3 className="font-bold text-gray-800 mb-4">Quick Actions</h3>
      
      <div className="space-y-3">
        {quickActionItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <Button
              key={item.id}
              variant="outline"
              className="w-full justify-start h-auto py-3 px-4 hover:bg-gray-50"
              onClick={item.onClick}
            >
              <div className="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                <IconComponent className="h-4 w-4 text-gray-600" />
              </div>
              <span className="text-gray-800">{item.label}</span>
            </Button>
          );
        })}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-100">
        <p className="text-xs text-gray-500 text-center">
          More actions available in Settings
        </p>
      </div>
    </Card>
  );
}