import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "motion/react";
import { 
  <PERSON><PERSON>hart, 
  ArrowLeft, 
  ArrowRight, 
  Target, 
  AlertCircle, 
  CheckCircle, 
  Calendar, 
  DollarSign,
  TrendingUp,
  Plus,
  Info,
  Clock,
  Trash2,
  Bar<PERSON>hart<PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Bell
} from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Progress } from "./ui/progress";
import { Alert, AlertDescription } from "./ui/alert";
import { Badge } from "./ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { InvestmentGoal, InvestmentGoalsData, NewInvestmentGoal } from "../types/investment";
import { investmentTypes } from "../constants/investments";
import { 
  calculateCompoundGrowth, 
  calculateTimeToGoal, 
  getMonthlyFromFrequency, 
  formatAmount, 
  formatDate 
} from "../utils/investment";
import obieLogo from 'figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png';

interface FinancialData {
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlySavings: number;
  monthlyInvestment: number;
  availableFromIncome: number;
  availableCashSavings: number;
}

interface InvestmentGoalsProps {
  financialData: FinancialData;
  onComplete: (data: InvestmentGoalsData) => void;
  onBack: () => void;
}

export function InvestmentGoals({ financialData, onComplete, onBack }: InvestmentGoalsProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [goals, setGoals] = useState<InvestmentGoal[]>([]);
  const [newGoal, setNewGoal] = useState<NewInvestmentGoal>({
    name: '',
    investmentType: 'index-funds',
    targetAmount: '',
    expectedReturnRate: '7',
    contributionFrequency: 'monthly',
    initialInvestment: 0,
    monthlyContribution: 0
  });
  const [showAddGoal, setShowAddGoal] = useState(false);
  const [hasVisitedStep2, setHasVisitedStep2] = useState(false);

  // Show form by default when first visiting step 2 and no goals exist
  useEffect(() => {
    if (currentStep === 2 && !hasVisitedStep2 && goals.length === 0) {
      setShowAddGoal(true);
      setHasVisitedStep2(true);
    }
  }, [currentStep, hasVisitedStep2, goals.length]);

  // Calculate totals
  const totalTargetAmount = goals.reduce((sum, goal) => sum + goal.targetAmount, 0);
  const totalInvestedAmount = goals.reduce((sum, goal) => sum + goal.currentAmount, 0);
  const totalMonthlyContribution = goals.reduce((sum, goal) => sum + goal.monthlyContribution, 0);
  
  // Calculate projected value (5 years out)
  const projectedValue = goals.reduce((sum, goal) => {
    return sum + calculateCompoundGrowth(
      goal.currentAmount,
      goal.monthlyContribution,
      goal.expectedReturnRate,
      5
    );
  }, 0);

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      onBack();
    }
  };

  const handleAddGoal = () => {
    // More lenient validation - only require name and target amount
    if (!newGoal.name.trim() || !newGoal.targetAmount || parseFloat(newGoal.targetAmount) <= 0) {
      alert('Please enter a goal name and a valid target amount.');
      return;
    }

    const targetAmount = parseFloat(newGoal.targetAmount);
    const expectedReturnRate = parseFloat(newGoal.expectedReturnRate) || 7; // Default to 7% if not provided
    
    // Set initial values to 0 for now - they'll be set in Step 3
    const monthlyContribution = 0;
    const initialInvestment = 0;

    // Calculate time to goal (will be Infinity with 0 contribution)
    const timeToGoal = calculateTimeToGoal(
      targetAmount,
      initialInvestment,
      monthlyContribution,
      expectedReturnRate
    );

    const completionDate = new Date();
    if (timeToGoal !== Infinity) {
      completionDate.setMonth(completionDate.getMonth() + timeToGoal);
    } else {
      // Set to 10 years from now as default
      completionDate.setFullYear(completionDate.getFullYear() + 10);
    }

    const goal: InvestmentGoal = {
      id: Date.now().toString(),
      name: newGoal.name.trim(),
      investmentType: newGoal.investmentType,
      targetAmount,
      currentAmount: initialInvestment,
      monthlyContribution,
      expectedReturnRate,
      contributionFrequency: newGoal.contributionFrequency,
      estimatedCompletionDate: completionDate.toISOString().split('T')[0],
      isCompleted: false,
      lastContributionDate: new Date().toISOString()
    };

    console.log('Adding new goal:', goal); // Debug log
    setGoals([...goals, goal]);
    
    // Reset form
    setNewGoal({
      name: '',
      investmentType: 'index-funds',
      targetAmount: '',
      expectedReturnRate: '7',
      contributionFrequency: 'monthly',
      initialInvestment: 0,
      monthlyContribution: 0
    });
    setShowAddGoal(false);
  };

  const handleRemoveGoal = (goalId: string) => {
    setGoals(goals.filter(goal => goal.id !== goalId));
  };

  const handleComplete = () => {
    const investmentGoalsData: InvestmentGoalsData = {
      goals,
      totalTargetAmount,
      totalInvestedAmount,
      totalMonthlyContribution,
      projectedValue
    };

    onComplete(investmentGoalsData);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center gap-4">
            <img src={obieLogo} alt="Obiemoney Logo" className="h-10 w-auto" />
            <div>
              <h1 className="text-3xl font-bold text-green-600">Investment Goals</h1>
              <p className="text-gray-600">Step 5: Small amounts over time make a big difference</p>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-xs bg-green-100 text-green-600 px-2 py-1 rounded-full font-medium">
                  STAGE 2: MAXIMISE
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 bg-white/60 rounded-full px-4 py-2">
              <span className="text-sm font-medium text-gray-600">Step {currentStep} of 4</span>
            </div>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <div className="mb-8">
          <Progress value={(currentStep / 4) * 100} className="h-2 bg-white/60" />
          <div className="flex justify-between mt-2 text-sm text-gray-600">
            <span className={currentStep >= 1 ? 'text-green-600 font-medium' : ''}>Learn</span>
            <span className={currentStep >= 2 ? 'text-green-600 font-medium' : ''}>Set Goals</span>
            <span className={currentStep >= 3 ? 'text-green-600 font-medium' : ''}>Fund Goals</span>
            <span className={currentStep >= 4 ? 'text-green-600 font-medium' : ''}>Summary</span>
          </div>
        </div>

        <AnimatePresence mode="wait">
          {/* Step 1: Education */}
          {currentStep === 1 && (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-8"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-green-100 rounded-full mb-4">
                    <LineChart className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">
                    Welcome to Stage 2: Maximise Your Wealth
                  </h2>
                  <p className="text-gray-600 max-w-2xl mx-auto">
                    Congratulations on completing Stage 1! You've optimized your finances. Now it's time to maximize your wealth through strategic investing. 
                    The power of compound growth can transform small, consistent investments into substantial wealth over time.
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  <Card className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-green-100 rounded-full mb-4">
                        <TrendingUp className="h-6 w-6 text-green-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Compound Growth</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Your money earns returns, then those returns earn returns. Time is your greatest ally in building wealth.
                      </p>
                      <div className="text-xs text-green-600 bg-green-50 rounded-lg p-2">
                        $100/month → $87,000 in 20 years @ 7%
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-blue-100 rounded-full mb-4">
                        <PieChart className="h-6 w-6 text-blue-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Diversification</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Spread your investments across different asset types to reduce risk while maintaining growth potential.
                      </p>
                      <div className="text-xs text-blue-600 bg-blue-50 rounded-lg p-2">
                        Stocks, Bonds, ETFs, REITs
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6 bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-purple-100 rounded-full mb-4">
                        <Clock className="h-6 w-6 text-purple-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Time in Market</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Consistency beats timing. Regular investing through market ups and downs builds wealth steadily.
                      </p>
                      <div className="text-xs text-purple-600 bg-purple-50 rounded-lg p-2">
                        Dollar-cost averaging
                      </div>
                    </div>
                  </Card>
                </div>

                <div className="space-y-6 mb-8">
                  <h3 className="font-bold text-gray-800 text-center">Investment Types & Expected Returns</h3>
                  
                  <div className="grid md:grid-cols-2 gap-4">
                    {investmentTypes.map(type => (
                      <Card key={type.id} className="p-4 bg-gray-50 border-gray-200">
                        <div className="flex items-start gap-3">
                          <div 
                            className="p-2 rounded-lg"
                            style={{ backgroundColor: `${type.color}20` }}
                          >
                            <BarChart3 className="h-4 w-4" style={{ color: type.color }} />
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center justify-between mb-1">
                              <h4 className="font-bold text-gray-800">{type.name}</h4>
                              <Badge 
                                variant={type.riskLevel === 'Low' ? 'secondary' : type.riskLevel === 'High' || type.riskLevel === 'Very High' ? 'destructive' : 'default'}
                                className="text-xs"
                              >
                                {type.riskLevel}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{type.description}</p>
                            <div className="flex justify-between text-xs">
                              <span className="text-gray-500">Expected Return:</span>
                              <span className="font-medium" style={{ color: type.color }}>{type.expectedReturn}</span>
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                </div>

                <Alert className="border-green-200 bg-green-50">
                  <Info className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-800">
                    <strong>Remember:</strong> Investing involves risk, and past performance doesn't guarantee future results. Start small, stay consistent, and focus on long-term growth!
                  </AlertDescription>
                </Alert>
              </Card>
            </motion.div>
          )}

          {/* Step 2: Set Investment Goals */}
          {currentStep === 2 && (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="flex justify-between items-center mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">Create Investment Goals</h2>
                    <p className="text-gray-600">
                      Set specific investment targets with expected returns and contribution schedules.
                    </p>
                  </div>
                  {goals.length > 0 && !showAddGoal && (
                    <Button 
                      onClick={() => setShowAddGoal(true)}
                      className="bg-green-600 hover:bg-green-700"
                    >
                      <Plus size={16} className="mr-2" />
                      Add Goal
                    </Button>
                  )}
                </div>

                {/* Existing Goals */}
                <div className="space-y-4 mb-6">
                  {goals.length > 0 && (
                    goals.map((goal) => {
                      const investmentType = investmentTypes.find(type => type.id === goal.investmentType);
                      const projectedAmount = calculateCompoundGrowth(
                        goal.currentAmount,
                        goal.monthlyContribution,
                        goal.expectedReturnRate,
                        5
                      );
                      
                      return (
                        <Card key={goal.id} className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
                          <div className="flex items-start gap-4">
                            <div 
                              className="w-16 h-16 rounded-lg flex items-center justify-center flex-shrink-0"
                              style={{ backgroundColor: `${investmentType?.color}20` }}
                            >
                              <LineChart className="h-8 w-8" style={{ color: investmentType?.color }} />
                            </div>
                            
                            <div className="flex-1">
                              <div className="flex items-start justify-between mb-2">
                                <div>
                                  <h3 className="font-bold text-gray-800 mb-1">{goal.name}</h3>
                                  <div className="flex items-center gap-4 text-sm text-gray-600">
                                    <span>Target: {formatAmount(goal.targetAmount)}</span>
                                    <span>Return: {goal.expectedReturnRate}%</span>
                                    <span>Type: {investmentType?.name}</span>
                                  </div>
                                </div>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => handleRemoveGoal(goal.id)}
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  <Trash2 size={16} />
                                </Button>
                              </div>
                              
                              <div className="space-y-2">
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-600">Ready for funding in Step 3</span>
                                  <span className="font-medium text-green-600">
                                    {formatAmount(goal.targetAmount)} goal
                                  </span>
                                </div>
                                <Progress value={0} className="h-2" />
                                <div className="flex justify-between text-xs text-gray-500">
                                  <span>Not funded yet</span>
                                  <span className="text-green-600">
                                    Potential: {formatAmount(calculateCompoundGrowth(1000, 100, goal.expectedReturnRate, 5))} in 5yr
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </Card>
                      );
                    })
                  )}
                  
                  {/* Empty State - only show if no goals and form is not visible */}
                  {goals.length === 0 && !showAddGoal && (
                    <Card className="p-8 text-center bg-gray-50">
                      <LineChart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-4">No investment goals created yet</p>
                      <Button 
                        onClick={() => setShowAddGoal(true)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Create Your First Investment Goal
                      </Button>
                    </Card>
                  )}
                </div>

                {/* Add Goal Form - Show by default on first visit or when clicked */}
                {showAddGoal && (
                  <Card className="p-6 bg-gradient-to-r from-emerald-50 to-green-50 border-emerald-200 mb-6">
                    <h3 className="font-bold text-gray-800 mb-4">Add New Investment Goal</h3>
                    <div className="space-y-4">
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Goal Name *
                          </label>
                          <Input
                            placeholder="e.g., Retirement Fund"
                            value={newGoal.name}
                            onChange={(e) => setNewGoal({...newGoal, name: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Target Amount *
                          </label>
                          <Input
                            type="number"
                            placeholder="50000"
                            value={newGoal.targetAmount}
                            onChange={(e) => setNewGoal({...newGoal, targetAmount: e.target.value})}
                          />
                        </div>
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Investment Type
                          </label>
                          <Select
                            value={newGoal.investmentType}
                            onValueChange={(value: any) => {
                              setNewGoal({...newGoal, investmentType: value});
                              // Auto-set expected return based on investment type
                              const type = investmentTypes.find(t => t.id === value);
                              if (type) {
                                const avgReturn = type.expectedReturn.split('-')[0].replace('%', '');
                                setNewGoal(prev => ({...prev, expectedReturnRate: avgReturn}));
                              }
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {investmentTypes.map(type => (
                                <SelectItem key={type.id} value={type.id}>{type.name}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Expected Annual Return (%)
                          </label>
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="7"
                            value={newGoal.expectedReturnRate}
                            onChange={(e) => setNewGoal({...newGoal, expectedReturnRate: e.target.value})}
                          />
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Contribution Frequency
                        </label>
                        <Select
                          value={newGoal.contributionFrequency}
                          onValueChange={(value: any) => setNewGoal({...newGoal, contributionFrequency: value})}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="weekly">Weekly</SelectItem>
                            <SelectItem value="monthly">Monthly</SelectItem>
                            <SelectItem value="quarterly">Quarterly</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <Alert className="border-blue-200 bg-blue-50">
                        <Info className="h-4 w-4 text-blue-600" />
                        <AlertDescription className="text-blue-800">
                          You'll set the initial investment and contribution amounts in the next step.
                        </AlertDescription>
                      </Alert>
                    </div>
                    
                    <div className="flex gap-3 mt-6">
                      <Button 
                        onClick={handleAddGoal}
                        disabled={!newGoal.name.trim() || !newGoal.targetAmount}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Add Investment Goal
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => setShowAddGoal(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </Card>
                )}

                {/* Summary */}
                {goals.length > 0 && (
                  <Card className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
                    <div className="grid md:grid-cols-3 gap-6 text-center">
                      <div>
                        <p className="text-sm text-gray-600">Goals Created</p>
                        <p className="text-2xl font-bold text-green-600">{goals.length}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Total Target</p>
                        <p className="text-2xl font-bold text-emerald-600">{formatAmount(totalTargetAmount)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Next Step</p>
                        <p className="text-lg font-medium text-blue-600">Fund Your Goals</p>
                      </div>
                    </div>
                  </Card>
                )}
              </Card>
            </motion.div>
          )}

          {/* Step 3: Fund Goals */}
          {currentStep === 3 && (
            <motion.div
              key="step3"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-green-100 rounded-full mb-4">
                    <DollarSign className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Fund Your Investment Goals</h2>
                  <p className="text-gray-600">
                    Allocate initial investments and set up regular contributions to reach your targets.
                  </p>
                </div>

                <div className="space-y-6">
                  {goals.map((goal, index) => {
                    const investmentType = investmentTypes.find(type => type.id === goal.investmentType);
                    const timeToGoal = calculateTimeToGoal(
                      goal.targetAmount,
                      goal.currentAmount,
                      goal.monthlyContribution,
                      goal.expectedReturnRate
                    );
                    
                    return (
                      <Card key={goal.id} className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
                        <div className="flex items-start gap-4 mb-6">
                          <div 
                            className="w-16 h-16 rounded-lg flex items-center justify-center flex-shrink-0"
                            style={{ backgroundColor: `${investmentType?.color}20` }}
                          >
                            <LineChart className="h-8 w-8" style={{ color: investmentType?.color }} />
                          </div>
                          
                          <div className="flex-1">
                            <h3 className="font-bold text-gray-800 mb-2">{goal.name}</h3>
                            <p className="text-sm text-gray-600">
                              Target: {formatAmount(goal.targetAmount)} • {goal.expectedReturnRate}% return • {investmentType?.name}
                            </p>
                          </div>
                        </div>

                        <div className="grid md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Initial Investment
                            </label>
                            <Input
                              type="number"
                              placeholder="1000"
                              value={goal.currentAmount || ''}
                              onChange={(e) => {
                                const updatedGoals = goals.map(g => 
                                  g.id === goal.id 
                                    ? {...g, currentAmount: parseFloat(e.target.value) || 0}
                                    : g
                                );
                                setGoals(updatedGoals);
                              }}
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              {goal.contributionFrequency === 'weekly' ? 'Weekly' : 
                               goal.contributionFrequency === 'monthly' ? 'Monthly' : 'Quarterly'} Contribution
                            </label>
                            <Input
                              type="number"
                              placeholder="100"
                              value={goal.monthlyContribution || ''}
                              onChange={(e) => {
                                const updatedGoals = goals.map(g => 
                                  g.id === goal.id 
                                    ? {...g, monthlyContribution: parseFloat(e.target.value) || 0}
                                    : g
                                );
                                setGoals(updatedGoals);
                              }}
                            />
                          </div>
                        </div>

                        {(goal.currentAmount > 0 || goal.monthlyContribution > 0) && (
                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span className="text-gray-600">Progress</span>
                              <span className="font-medium text-green-600">
                                {formatAmount(goal.currentAmount)} / {formatAmount(goal.targetAmount)}
                              </span>
                            </div>
                            <Progress 
                              value={Math.min((goal.currentAmount / goal.targetAmount) * 100, 100)} 
                              className="h-2" 
                            />
                            <div className="flex justify-between text-xs text-gray-500">
                              <span>
                                {goal.monthlyContribution > 0 
                                  ? `${formatAmount(goal.monthlyContribution)}/${goal.contributionFrequency}` 
                                  : 'No contributions set'}
                              </span>
                              <span className="text-green-600">
                                {timeToGoal !== Infinity 
                                  ? `${Math.round(timeToGoal)} months to goal` 
                                  : 'Set contributions to calculate timeline'}
                              </span>
                            </div>
                          </div>
                        )}
                      </Card>
                    );
                  })}

                  {goals.length === 0 && (
                    <Card className="p-8 text-center bg-gray-50">
                      <LineChart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">No investment goals to fund. Go back to create your first goal!</p>
                    </Card>
                  )}
                </div>
              </Card>
            </motion.div>
          )}

          {/* Step 4: Summary */}
          {currentStep === 4 && (
            <motion.div
              key="step4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-green-100 rounded-full mb-4">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Your Investment Strategy</h2>
                  <p className="text-gray-600">
                    Review your investment goals and projected growth. Stay consistent and watch your wealth compound over time!
                  </p>
                </div>

                <div className="space-y-6">
                  {/* Investment Overview */}
                  <Card className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
                    <h3 className="font-bold text-gray-800 mb-4">Investment Overview</h3>
                    <div className="grid md:grid-cols-4 gap-4 text-center">
                      <div>
                        <p className="text-sm text-gray-600">Goals Created</p>
                        <p className="text-2xl font-bold text-green-600">{goals.length}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Total Target</p>
                        <p className="text-2xl font-bold text-emerald-600">{formatAmount(totalTargetAmount)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Current Investment</p>
                        <p className="text-2xl font-bold text-teal-600">{formatAmount(totalInvestedAmount)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Monthly Contributions</p>
                        <p className="text-2xl font-bold text-cyan-600">{formatAmount(totalMonthlyContribution)}</p>
                      </div>
                    </div>
                  </Card>

                  {/* Individual Goals */}
                  {goals.length > 0 ? (
                    <div className="space-y-4">
                      <h3 className="font-bold text-gray-800">Your Investment Goals</h3>
                      {goals.map((goal) => {
                        const investmentType = investmentTypes.find(type => type.id === goal.investmentType);
                        const projectedAmount = calculateCompoundGrowth(
                          goal.currentAmount,
                          goal.monthlyContribution,
                          goal.expectedReturnRate,
                          5
                        );
                        const timeToGoal = calculateTimeToGoal(
                          goal.targetAmount,
                          goal.currentAmount,
                          goal.monthlyContribution,
                          goal.expectedReturnRate
                        );
                        
                        return (
                          <Card key={goal.id} className="p-6 bg-gradient-to-r from-emerald-50 to-green-50 border-emerald-200">
                            <div className="flex items-start gap-4">
                              <div 
                                className="w-16 h-16 rounded-lg flex items-center justify-center flex-shrink-0"
                                style={{ backgroundColor: `${investmentType?.color}20` }}
                              >
                                <LineChart className="h-8 w-8" style={{ color: investmentType?.color }} />
                              </div>
                              
                              <div className="flex-1">
                                <div className="flex justify-between items-start mb-3">
                                  <div>
                                    <h4 className="font-bold text-gray-800">{goal.name}</h4>
                                    <p className="text-sm text-gray-600">
                                      {investmentType?.name} • {goal.expectedReturnRate}% expected return
                                    </p>
                                  </div>
                                  <div className="text-right">
                                    <p className="text-sm text-gray-600">Target Amount</p>
                                    <p className="font-bold text-green-600">{formatAmount(goal.targetAmount)}</p>
                                  </div>
                                </div>
                                
                                <div className="grid md:grid-cols-3 gap-4 text-sm">
                                  <div>
                                    <p className="text-gray-600">Current Investment</p>
                                    <p className="font-medium">{formatAmount(goal.currentAmount)}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-600">{goal.contributionFrequency} Contribution</p>
                                    <p className="font-medium">{formatAmount(goal.monthlyContribution)}</p>
                                  </div>
                                  <div>
                                    <p className="text-gray-600">Time to Goal</p>
                                    <p className="font-medium text-green-600">
                                      {timeToGoal !== Infinity ? `${Math.round(timeToGoal)} months` : 'No contributions'}
                                    </p>
                                  </div>
                                </div>
                                
                                <div className="mt-3 p-3 bg-green-100 rounded-lg">
                                  <p className="text-sm text-green-800">
                                    <strong>5-Year Projection:</strong> {formatAmount(projectedAmount)} 
                                    {projectedAmount > goal.currentAmount && (
                                      <span className="ml-2">
                                        (+{formatAmount(projectedAmount - goal.currentAmount)} growth)
                                      </span>
                                    )}
                                  </p>
                                </div>
                              </div>
                            </div>
                          </Card>
                        );
                      })}
                    </div>
                  ) : (
                    <Card className="p-8 text-center bg-gray-50">
                      <LineChart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">No investment goals created yet. Consider setting some goals to grow your wealth over time.</p>
                    </Card>
                  )}

                  {/* 5-Year Projection */}
                  {projectedValue > 0 && (
                    <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                      <h3 className="font-bold text-gray-800 mb-4">5-Year Wealth Projection</h3>
                      <div className="text-center">
                        <p className="text-sm text-gray-600 mb-2">Projected Portfolio Value</p>
                        <p className="text-4xl font-bold text-blue-600 mb-4">{formatAmount(projectedValue)}</p>
                        <p className="text-sm text-blue-800">
                          Based on your current contributions and expected returns. Remember, actual results may vary.
                        </p>
                      </div>
                    </Card>
                  )}

                  {/* Next Steps */}
                  <Alert className="border-green-200 bg-green-50">
                    <Info className="h-4 w-4 text-green-600" />
                    <AlertDescription className="text-green-800">
                      <strong>Next Steps:</strong> Set up automatic transfers to your investment accounts, review your portfolio quarterly, and stay consistent with your contributions. Time is your greatest asset! 📈
                    </AlertDescription>
                  </Alert>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8">
          <Button
            variant="outline"
            onClick={handleBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {currentStep === 1 ? 'Back to Dashboard' : 'Back'}
          </Button>

          <div className="flex items-center gap-4">
            {currentStep < 4 ? (
              <Button
                onClick={handleNext}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              >
                Continue
                <ArrowRight className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={handleComplete}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              >
                Complete Investment Goals
                <CheckCircle className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}