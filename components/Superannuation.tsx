import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { 
  Building, 
  ArrowLeft, 
  ArrowRight, 
  Target, 
  AlertCircle, 
  CheckCircle, 
  Calendar, 
  DollarSign,
  TrendingUp,
  Plus,
  Info,
  Clock,
  Trash2,
  BarChart3,
  Calculator,
  Users,
  Wallet,
  <PERSON>rk<PERSON>,
  Bell
} from "lucide-react";
import { But<PERSON> } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Progress } from "./ui/progress";
import { Alert, AlertDescription } from "./ui/alert";
import { Badge } from "./ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { SuperFund, SuperannuationData, NewSuperFund } from "../types/superannuation";
import { popularSuperFunds, retirementLifestyles, superHealthCheckFacts, superKeyFacts } from "../constants/superannuation";
import { 
  calculateSuperProjection,
  calculateRetirementIncome,
  getMonthlyFromFrequency,
  formatAmount,
  getRetirementReadiness,
  getRetirementAgeOptions
} from "../utils/superannuation";
import obieLogo from 'figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png';

interface FinancialData {
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlySavings: number;
  monthlyInvestment: number;
  availableFromIncome: number;
  availableCashSavings: number;
}

interface SuperannuationProps {
  financialData: FinancialData;
  onComplete: (data: SuperannuationData) => void;
  onBack: () => void;
}

export function Superannuation({ financialData, onComplete, onBack }: SuperannuationProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [currentAge, setCurrentAge] = useState('');
  const [retirementAge, setRetirementAge] = useState('67');
  const [targetRetirementIncome, setTargetRetirementIncome] = useState('');
  const [superFunds, setSuperFunds] = useState<SuperFund[]>([]);
  const [newFund, setNewFund] = useState<NewSuperFund>({
    fundName: '',
    currentBalance: '',
    extraContributions: '',
    contributionFrequency: 'monthly',
    expectedReturnRate: '7'
  });
  const [showAddFund, setShowAddFund] = useState(false);

  // Calculate totals
  const totalCurrentBalance = superFunds.reduce((sum, fund) => sum + fund.currentBalance, 0);
  const totalExtraContributions = superFunds.reduce((sum, fund) => {
    return sum + getMonthlyFromFrequency(fund.extraContributions, fund.contributionFrequency);
  }, 0);

  // Calculate projections
  const yearsToRetirement = Math.max(0, parseInt(retirementAge) - parseInt(currentAge || '30'));
  const annualSalary = financialData.monthlyIncome * 12;
  
  const projectedRetirementBalance = superFunds.reduce((sum, fund) => {
    return sum + calculateSuperProjection(
      fund.currentBalance,
      fund.extraContributions,
      fund.contributionFrequency,
      fund.expectedReturnRate,
      yearsToRetirement,
      parseInt(currentAge || '30'),
      annualSalary
    );
  }, 0);
  
  const projectedRetirementIncome = calculateRetirementIncome(projectedRetirementBalance, parseInt(retirementAge));
  const targetIncome = parseFloat(targetRetirementIncome) || 0;
  const isOnTrack = projectedRetirementIncome >= targetIncome * 0.8; // 80% or more is considered on track

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      onBack();
    }
  };

  const handleAddFund = () => {
    if (!newFund.fundName.trim() || !newFund.currentBalance) return;

    const fund: SuperFund = {
      id: Date.now().toString(),
      fundName: newFund.fundName.trim(),
      currentBalance: parseFloat(newFund.currentBalance) || 0,
      extraContributions: parseFloat(newFund.extraContributions) || 0,
      contributionFrequency: newFund.contributionFrequency,
      expectedReturnRate: parseFloat(newFund.expectedReturnRate) || 7,
      lastContributionDate: new Date().toISOString()
    };

    setSuperFunds([...superFunds, fund]);
    setNewFund({
      fundName: '',
      currentBalance: '',
      extraContributions: '',
      contributionFrequency: 'monthly',
      expectedReturnRate: '7'
    });
    setShowAddFund(false);
  };

  const handleRemoveFund = (fundId: string) => {
    setSuperFunds(superFunds.filter(fund => fund.id !== fundId));
  };

  const handleComplete = () => {
    const superannuationData: SuperannuationData = {
      retirementAge: parseInt(retirementAge),
      targetRetirementIncome: targetIncome,
      currentAge: parseInt(currentAge || '30'),
      superFunds,
      totalCurrentBalance,
      totalExtraContributions,
      projectedRetirementBalance,
      projectedRetirementIncome,
      isOnTrack
    };

    onComplete(superannuationData);
  };

  const retirementReadiness = getRetirementReadiness(projectedRetirementIncome, targetIncome);

  const getIconComponent = (iconName: string) => {
    const icons = {
      Calculator,
      Target,
      TrendingUp,
      DollarSign,
      Wallet,
      Clock,
      Users
    };
    return icons[iconName as keyof typeof icons] || Calculator;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-cyan-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center gap-4">
            <img src={obieLogo} alt="Obiemoney Logo" className="h-10 w-auto" />
            <div>
              <h1 className="text-3xl font-bold text-indigo-600">Superannuation</h1>
              <p className="text-gray-600">Step 6: Super? Enough to retire on?</p>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-xs bg-indigo-100 text-indigo-600 px-2 py-1 rounded-full font-medium">
                  STAGE 2: MAXIMISE
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 bg-white/60 rounded-full px-4 py-2">
              <span className="text-sm font-medium text-gray-600">Step {currentStep} of 4</span>
            </div>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <div className="mb-8">
          <Progress value={(currentStep / 4) * 100} className="h-2 bg-white/60" />
          <div className="flex justify-between mt-2 text-sm text-gray-600">
            <span className={currentStep >= 1 ? 'text-indigo-600 font-medium' : ''}>Health Check</span>
            <span className={currentStep >= 2 ? 'text-indigo-600 font-medium' : ''}>Set Goals</span>
            <span className={currentStep >= 3 ? 'text-indigo-600 font-medium' : ''}>Manage Funds</span>
            <span className={currentStep >= 4 ? 'text-indigo-600 font-medium' : ''}>Summary</span>
          </div>
        </div>

        <AnimatePresence mode="wait">
          {/* Step 1: Super Health Check */}
          {currentStep === 1 && (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-8"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-indigo-100 rounded-full mb-4">
                    <Building className="h-8 w-8 text-indigo-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">
                    Superannuation Health Check
                  </h2>
                  <p className="text-gray-600 max-w-2xl mx-auto">
                    Let's assess your retirement readiness! We'll check if your super is on track to provide the retirement lifestyle you want.
                    Your golden years should be truly golden.
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  {superHealthCheckFacts.map((fact, index) => {
                    const IconComponent = getIconComponent(fact.icon);
                    return (
                      <Card key={index} className={`p-6 bg-gradient-to-br from-${fact.color}-50 to-blue-50 border-${fact.color}-200`}>
                        <div className="text-center">
                          <div className={`inline-flex p-3 bg-${fact.color}-100 rounded-full mb-4`}>
                            <IconComponent className={`h-6 w-6 text-${fact.color}-600`} />
                          </div>
                          <h3 className="font-bold text-gray-800 mb-2">{fact.title}</h3>
                          <p className="text-sm text-gray-600 mb-4">
                            {fact.description}
                          </p>
                          <div className={`text-xs text-${fact.color}-600 bg-${fact.color}-50 rounded-lg p-2`}>
                            {fact.stat}
                          </div>
                        </div>
                      </Card>
                    );
                  })}
                </div>

                <div className="space-y-6 mb-8">
                  <h3 className="font-bold text-gray-800 text-center">Key Super Facts</h3>
                  
                  <div className="grid md:grid-cols-2 gap-6">
                    {superKeyFacts.map((fact, index) => {
                      const IconComponent = getIconComponent(fact.icon);
                      return (
                        <Card key={index} className={`p-6 bg-gradient-to-r ${fact.bgColor} ${fact.borderColor}`}>
                          <div className="flex items-start gap-4">
                            <div className={`p-3 bg-${fact.color}-100 rounded-full flex-shrink-0`}>
                              <IconComponent className={`h-6 w-6 text-${fact.color}-600`} />
                            </div>
                            <div>
                              <h4 className="font-bold text-gray-800 mb-2">{fact.title}</h4>
                              <p className="text-sm text-gray-600 mb-2">
                                {fact.description}
                              </p>
                              <p className={`text-xs text-${fact.color}-600 font-medium`}>
                                {fact.statLabel}
                                {fact.icon === 'DollarSign' && ` salary: ${formatAmount(annualSalary * 0.095)}/year`}
                              </p>
                            </div>
                          </div>
                        </Card>
                      );
                    })}
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6 mb-8">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Your Current Age *
                    </label>
                    <Input
                      type="number"
                      placeholder="35"
                      value={currentAge}
                      onChange={(e) => setCurrentAge(e.target.value)}
                      min="18"
                      max="70"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Planned Retirement Age
                    </label>
                    <Select value={retirementAge} onValueChange={setRetirementAge}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {getRetirementAgeOptions().map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {currentAge && (
                  <Alert className="border-indigo-200 bg-indigo-50">
                    <Info className="h-4 w-4 text-indigo-600" />
                    <AlertDescription className="text-indigo-800">
                      <strong>Great!</strong> You have {yearsToRetirement} years until retirement. 
                      That's {yearsToRetirement > 20 ? 'plenty of time' : yearsToRetirement > 10 ? 'good time' : 'limited time'} to optimize your super strategy.
                    </AlertDescription>
                  </Alert>
                )}
              </Card>
            </motion.div>
          )}

          {/* Step 2: Set Retirement Goals */}
          {currentStep === 2 && (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-blue-100 rounded-full mb-4">
                    <Target className="h-8 w-8 text-blue-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Set Your Retirement Income Goal</h2>
                  <p className="text-gray-600">
                    How much annual income do you want in retirement? This helps us calculate if your super is on track.
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  {retirementLifestyles.map((lifestyle) => (
                    <Card 
                      key={lifestyle.id}
                      className={`p-6 cursor-pointer transition-all ${
                        targetRetirementIncome === lifestyle.amount.toString()
                          ? `border-${lifestyle.color}-500 bg-${lifestyle.color}-50` 
                          : `border-gray-200 hover:border-${lifestyle.color}-300`
                      }`}
                      onClick={() => setTargetRetirementIncome(lifestyle.amount.toString())}
                    >
                      <div className="text-center">
                        <div className={`text-2xl font-bold text-${lifestyle.color}-600 mb-2`}>
                          {formatAmount(lifestyle.amount)}
                        </div>
                        <h4 className="font-bold text-gray-800 mb-2">{lifestyle.title}</h4>
                        <p className="text-sm text-gray-600">
                          {lifestyle.description}
                        </p>
                        <div className={`mt-3 text-xs text-${lifestyle.color}-600`}>
                          {lifestyle.weeklyAmount}
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>

                <div className="mb-8">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Custom Retirement Income Goal (Annual)
                  </label>
                  <Input
                    type="number"
                    placeholder="Enter custom amount..."
                    value={targetRetirementIncome}
                    onChange={(e) => setTargetRetirementIncome(e.target.value)}
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    This is the total annual income you want in retirement, including super withdrawals and any other income.
                  </p>
                </div>

                {targetRetirementIncome && (
                  <Alert className="border-blue-200 bg-blue-50">
                    <Target className="h-4 w-4 text-blue-600" />
                    <AlertDescription className="text-blue-800">
                      <strong>Target Set!</strong> You're aiming for {formatAmount(parseFloat(targetRetirementIncome))} per year in retirement. 
                      That's about {formatAmount(parseFloat(targetRetirementIncome) / 52)} per week.
                    </AlertDescription>
                  </Alert>
                )}
              </Card>
            </motion.div>
          )}

          {/* Step 3: Manage Super Funds */}
          {currentStep === 3 && (
            <motion.div
              key="step3"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="flex justify-between items-center mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">Manage Your Super Funds</h2>
                    <p className="text-gray-600">
                      Add your super funds and set up extra contributions to boost your retirement savings.
                    </p>
                  </div>
                  <Button 
                    onClick={() => setShowAddFund(true)}
                    className="bg-indigo-600 hover:bg-indigo-700"
                  >
                    <Plus size={16} className="mr-2" />
                    Add Fund
                  </Button>
                </div>

                {/* Existing Super Funds */}
                <div className="space-y-4 mb-6">
                  {superFunds.length > 0 ? (
                    superFunds.map((fund) => {
                      const projectedBalance = calculateSuperProjection(
                        fund.currentBalance,
                        fund.extraContributions,
                        fund.contributionFrequency,
                        fund.expectedReturnRate,
                        yearsToRetirement,
                        parseInt(currentAge || '30'),
                        annualSalary
                      );
                      
                      return (
                        <Card key={fund.id} className="p-6 bg-gradient-to-r from-indigo-50 to-cyan-50 border-indigo-200">
                          <div className="flex items-start gap-4">
                            <div className="w-16 h-16 rounded-lg bg-indigo-100 flex items-center justify-center flex-shrink-0">
                              <Building className="h-8 w-8 text-indigo-600" />
                            </div>
                            
                            <div className="flex-1">
                              <div className="flex items-start justify-between mb-2">
                                <div>
                                  <h3 className="font-bold text-gray-800 mb-1">{fund.fundName}</h3>
                                  <div className="flex items-center gap-4 text-sm text-gray-600">
                                    <span>Balance: {formatAmount(fund.currentBalance)}</span>
                                    <span>Extra: {formatAmount(fund.extraContributions)}</span>
                                    <span>Return: {fund.expectedReturnRate}%</span>
                                  </div>
                                </div>
                                <Button 
                                  variant="outline" 
                                  size="sm"
                                  onClick={() => handleRemoveFund(fund.id)}
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  <Trash2 size={16} />
                                </Button>
                              </div>
                              
                              <div className="space-y-2">
                                <div className="flex justify-between text-sm">
                                  <span className="text-gray-600">Projected at Retirement</span>
                                  <span className="font-medium text-indigo-600">
                                    {formatAmount(projectedBalance)}
                                  </span>
                                </div>
                                <div className="flex justify-between text-xs text-gray-500">
                                  <span>Contribution frequency: {fund.contributionFrequency}</span>
                                  <span className="text-indigo-600">
                                    Growth over {yearsToRetirement} years
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>
                        </Card>
                      );
                    })
                  ) : (
                    <Card className="p-8 text-center bg-gray-50">
                      <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-4">No super funds added yet</p>
                      <Button 
                        onClick={() => setShowAddFund(true)}
                        className="bg-indigo-600 hover:bg-indigo-700"
                      >
                        Add Your First Super Fund
                      </Button>
                    </Card>
                  )}
                </div>

                {/* Add Fund Form */}
                {showAddFund && (
                  <Card className="p-6 bg-gradient-to-r from-cyan-50 to-indigo-50 border-cyan-200 mb-6">
                    <h3 className="font-bold text-gray-800 mb-4">Add Super Fund</h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Fund Name *
                        </label>
                        <Select
                          value={newFund.fundName}
                          onValueChange={(value) => setNewFund({...newFund, fundName: value})}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select your super fund" />
                          </SelectTrigger>
                          <SelectContent>
                            {popularSuperFunds.map(fund => (
                              <SelectItem key={fund} value={fund}>{fund}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Current Balance *
                          </label>
                          <Input
                            type="number"
                            placeholder="50000"
                            value={newFund.currentBalance}
                            onChange={(e) => setNewFund({...newFund, currentBalance: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Extra Contributions
                          </label>
                          <Input
                            type="number"
                            placeholder="200"
                            value={newFund.extraContributions}
                            onChange={(e) => setNewFund({...newFund, extraContributions: e.target.value})}
                          />
                        </div>
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Contribution Frequency
                          </label>
                          <Select
                            value={newFund.contributionFrequency}
                            onValueChange={(value: any) => setNewFund({...newFund, contributionFrequency: value})}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="weekly">Weekly</SelectItem>
                              <SelectItem value="fortnightly">Fortnightly</SelectItem>
                              <SelectItem value="monthly">Monthly</SelectItem>
                              <SelectItem value="quarterly">Quarterly</SelectItem>
                              <SelectItem value="yearly">Yearly</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Expected Return Rate (%)
                          </label>
                          <Input
                            type="number"
                            step="0.1"
                            placeholder="7"
                            value={newFund.expectedReturnRate}
                            onChange={(e) => setNewFund({...newFund, expectedReturnRate: e.target.value})}
                          />
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex gap-3 mt-6">
                      <Button 
                        onClick={handleAddFund}
                        disabled={!newFund.fundName.trim() || !newFund.currentBalance}
                        className="bg-indigo-600 hover:bg-indigo-700"
                      >
                        Add Super Fund
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => setShowAddFund(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </Card>
                )}

                {/* Summary */}
                {superFunds.length > 0 && (
                  <Card className="p-6 bg-gradient-to-r from-indigo-50 to-cyan-50 border-indigo-200">
                    <div className="grid md:grid-cols-4 gap-6 text-center">
                      <div>
                        <p className="text-sm text-gray-600">Total Balance</p>
                        <p className="text-2xl font-bold text-indigo-600">{formatAmount(totalCurrentBalance)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Monthly Extras</p>
                        <p className="text-2xl font-bold text-cyan-600">{formatAmount(totalExtraContributions)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Projected Balance</p>
                        <p className="text-2xl font-bold text-blue-600">{formatAmount(projectedRetirementBalance)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Retirement Income</p>
                        <p className="text-2xl font-bold text-purple-600">{formatAmount(projectedRetirementIncome)}</p>
                      </div>
                    </div>
                  </Card>
                )}
              </Card>
            </motion.div>
          )}

          {/* Step 4: Summary */}
          {currentStep === 4 && (
            <motion.div
              key="step4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-indigo-100 rounded-full mb-4">
                    <CheckCircle className="h-8 w-8 text-indigo-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Your Superannuation Summary</h2>
                  <p className="text-gray-600">
                    Here's your complete retirement plan. You're now maximizing your wealth for the golden years ahead!
                  </p>
                </div>

                <div className="space-y-6">
                  {/* Retirement Readiness */}
                  <Card className={`p-6 bg-gradient-to-r ${
                    retirementReadiness.status === 'excellent' || retirementReadiness.status === 'good'
                      ? 'from-green-50 to-emerald-50 border-green-200'
                      : retirementReadiness.status === 'fair'
                      ? 'from-yellow-50 to-orange-50 border-yellow-200'
                      : 'from-red-50 to-pink-50 border-red-200'
                  }`}>
                    <div className="text-center">
                      <h3 className="font-bold text-gray-800 mb-2">Retirement Readiness</h3>
                      <p className={`text-2xl font-bold ${retirementReadiness.color} mb-2`}>
                        {retirementReadiness.message}
                      </p>
                      <div className="grid md:grid-cols-2 gap-4 mt-4">
                        <div>
                          <p className="text-sm text-gray-600">Your Target</p>
                          <p className="font-bold text-gray-800">{formatAmount(targetIncome)}/year</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Projected Income</p>
                          <p className={`font-bold ${retirementReadiness.color}`}>
                            {formatAmount(projectedRetirementIncome)}/year
                          </p>
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* Super Summary */}
                  <Card className="p-6 bg-gradient-to-r from-indigo-50 to-cyan-50 border-indigo-200">
                    <h3 className="font-bold text-gray-800 mb-4 text-center">Super Portfolio Summary</h3>
                    <div className="grid md:grid-cols-4 gap-4 text-center mb-6">
                      <div>
                        <p className="text-sm text-gray-600">Funds</p>
                        <p className="text-xl font-bold text-indigo-600">{superFunds.length}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Current Balance</p>
                        <p className="text-xl font-bold text-cyan-600">{formatAmount(totalCurrentBalance)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Extra Contributions</p>
                        <p className="text-xl font-bold text-blue-600">{formatAmount(totalExtraContributions)}/month</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Projected Balance</p>
                        <p className="text-xl font-bold text-purple-600">{formatAmount(projectedRetirementBalance)}</p>
                      </div>
                    </div>

                    <div className="space-y-3">
                      {superFunds.map((fund) => (
                        <div key={fund.id} className="flex items-center justify-between p-3 bg-white rounded-lg">
                          <div className="flex items-center gap-3">
                            <Building className="h-5 w-5 text-indigo-600" />
                            <div>
                              <p className="font-medium text-gray-800">{fund.fundName}</p>
                              <p className="text-sm text-gray-600">
                                {formatAmount(fund.extraContributions)} extra {fund.contributionFrequency}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-indigo-600">{formatAmount(fund.currentBalance)}</p>
                            <p className="text-xs text-gray-500">{fund.expectedReturnRate}% return</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </Card>

                  {/* Next Steps */}
                  <Card className="p-6 bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <Sparkles className="h-5 w-5 text-purple-600" />
                      What Happens Next
                    </h3>
                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Calendar className="h-6 w-6 text-indigo-600" />
                        </div>
                        <h4 className="font-bold text-gray-800 mb-2">Regular Contributions</h4>
                        <p className="text-sm text-gray-600">Make consistent extra contributions and let compound growth work over decades.</p>
                      </div>
                      
                      <div className="text-center">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Bell className="h-6 w-6 text-blue-600" />
                        </div>
                        <h4 className="font-bold text-gray-800 mb-2">Annual Reviews</h4>
                        <p className="text-sm text-gray-600">Review your super annually and adjust contributions as your salary grows.</p>
                      </div>
                      
                      <div className="text-center">
                        <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Target className="h-6 w-6 text-purple-600" />
                        </div>
                        <h4 className="font-bold text-gray-800 mb-2">Goal Tracking</h4>
                        <p className="text-sm text-gray-600">Monitor your progress and celebrate milestones on your journey to retirement.</p>
                      </div>
                    </div>
                  </Card>

                  {/* Success Message */}
                  <Alert className="border-indigo-200 bg-indigo-50">
                    <CheckCircle className="h-4 w-4 text-indigo-600" />
                    <AlertDescription className="text-indigo-800">
                      <strong>Congratulations!</strong> You've completed Stage 2: Maximise! Your comprehensive wealth-building strategy is now complete, covering investments and superannuation for long-term financial success.
                    </AlertDescription>
                  </Alert>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8">
          <Button
            variant="outline"
            onClick={handleBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft size={16} />
            {currentStep === 1 ? 'Back to Dashboard' : 'Back'}
          </Button>
          
          <div className="flex gap-3">
            {currentStep < 4 ? (
              <Button
                onClick={handleNext}
                disabled={
                  (currentStep === 1 && !currentAge) ||
                  (currentStep === 2 && !targetRetirementIncome) ||
                  (currentStep === 3 && superFunds.length === 0)
                }
                className="flex items-center gap-2 bg-indigo-600 hover:bg-indigo-700"
              >
                Continue
                <ArrowRight size={16} />
              </Button>
            ) : (
              <Button
                onClick={handleComplete}
                className="flex items-center gap-2 bg-indigo-600 hover:bg-indigo-700"
              >
                Complete Super Setup
                <CheckCircle size={16} />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}