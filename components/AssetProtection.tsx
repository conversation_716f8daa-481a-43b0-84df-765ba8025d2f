import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { 
  Shield, 
  ArrowLeft, 
  ArrowRight, 
  Target, 
  AlertCircle, 
  CheckCircle, 
  Plus,
  Info,
  Trash2,
  Home,
  Car,
  TrendingUp,
  Briefcase,
  Package,
  Lightbulb,
  DollarSign,
  Star,
  Gem,
  Smartphone,
  AlertTriangle,
  Sparkles,
  FileText,
  Users
} from "lucide-react";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Progress } from "./ui/progress";
import { Alert, AlertDescription } from "./ui/alert";
import { Badge } from "./ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Switch } from "./ui/switch";
import { Textarea } from "./ui/textarea";
import { Asset, AssetProtectionData, NewAsset } from "../types/assetProtection";
import { assetCategories, protectionGuidelines, assetProtectionEducation, riskLevels, commonProtectionMethods } from "../constants/assetProtection";
import { 
  calculateAssetProtectionData,
  formatAmount,
  getAssetRiskColor,
  getProtectionStatusColor,
  calculateProtectionEffectiveness,
  sortAssetsByPriority
} from "../utils/assetProtection";
import obieLogo from 'figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png';

interface FinancialData {
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlySavings: number;
  monthlyInvestment: number;
  availableFromIncome: number;
  availableCashSavings: number;
}

interface AssetProtectionProps {
  financialData: FinancialData;
  onComplete: (data: AssetProtectionData) => void;
  onBack: () => void;
}

export function AssetProtection({ financialData, onComplete, onBack }: AssetProtectionProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [assets, setAssets] = useState<Asset[]>([]);
  const [newAsset, setNewAsset] = useState<NewAsset>({
    name: '',
    category: 'real_estate',
    estimatedValue: '',
    description: '',
    riskLevel: 'medium',
    notes: ''
  });
  const [showAddAsset, setShowAddAsset] = useState(false);
  const [selectedAsset, setSelectedAsset] = useState<Asset | null>(null);
  const [protectionMethod, setProtectionMethod] = useState('');
  const [protectionDetails, setProtectionDetails] = useState('');

  // Calculate asset protection data
  const assetProtectionData = calculateAssetProtectionData(assets);
  const protectionEffectiveness = calculateProtectionEffectiveness(assets);
  const sortedAssets = sortAssetsByPriority(assets);

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      onBack();
    }
  };

  const handleAddAsset = () => {
    if (!newAsset.name.trim()) {
      alert('Please enter an asset name');
      return;
    }

    const asset: Asset = {
      id: Date.now().toString(),
      name: newAsset.name.trim(),
      category: newAsset.category,
      estimatedValue: parseFloat(newAsset.estimatedValue) || undefined,
      description: newAsset.description.trim() || undefined,
      isProtected: false,
      riskLevel: newAsset.riskLevel,
      notes: newAsset.notes.trim() || undefined,
      createdDate: new Date().toISOString(),
      updatedDate: new Date().toISOString()
    };

    setAssets([...assets, asset]);
    setNewAsset({
      name: '',
      category: 'real_estate',
      estimatedValue: '',
      description: '',
      riskLevel: 'medium',
      notes: ''
    });
    setShowAddAsset(false);
  };

  const handleRemoveAsset = (assetId: string) => {
    setAssets(assets.filter(asset => asset.id !== assetId));
  };

  const handleUpdateProtectionStatus = (assetId: string, isProtected: boolean) => {
    setAssets(assets.map(asset => 
      asset.id === assetId 
        ? { 
            ...asset, 
            isProtected, 
            updatedDate: new Date().toISOString(),
            protectionMethod: isProtected ? asset.protectionMethod : undefined,
            protectionDetails: isProtected ? asset.protectionDetails : undefined
          }
        : asset
    ));
  };

  const handleUpdateAssetProtection = (assetId: string, method: string, details: string) => {
    setAssets(assets.map(asset => 
      asset.id === assetId 
        ? { 
            ...asset, 
            protectionMethod: method,
            protectionDetails: details,
            isProtected: true,
            updatedDate: new Date().toISOString(),
            lastReviewDate: new Date().toISOString()
          }
        : asset
    ));
    setSelectedAsset(null);
    setProtectionMethod('');
    setProtectionDetails('');
  };

  const handleComplete = () => {
    onComplete(assetProtectionData);
  };

  const getIconComponent = (iconName: string) => {
    const icons = {
      Home,
      Car,
      TrendingUp,
      Briefcase,
      Package,
      Lightbulb,
      DollarSign,
      Star,
      Gem,
      Smartphone,
      Shield
    };
    return icons[iconName as keyof typeof icons] || Shield;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-amber-50 via-orange-50 to-red-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center gap-4">
            <img src={obieLogo} alt="Obiemoney Logo" className="h-10 w-auto" />
            <div>
              <h1 className="text-3xl font-bold text-amber-600">Asset Protection</h1>
              <p className="text-gray-600">Step 8: Are my assets protected?</p>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-xs bg-amber-100 text-amber-600 px-2 py-1 rounded-full font-medium">
                  STAGE 3: PROTECT
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 bg-white/60 rounded-full px-4 py-2">
              <span className="text-sm font-medium text-gray-600">Step {currentStep} of 4</span>
            </div>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <div className="mb-8">
          <Progress value={(currentStep / 4) * 100} className="h-2 bg-white/60" />
          <div className="flex justify-between mt-2 text-sm text-gray-600">
            <span className={currentStep >= 1 ? 'text-amber-600 font-medium' : ''}>Education</span>
            <span className={currentStep >= 2 ? 'text-amber-600 font-medium' : ''}>Asset Inventory</span>
            <span className={currentStep >= 3 ? 'text-amber-600 font-medium' : ''}>Protection Review</span>
            <span className={currentStep >= 4 ? 'text-amber-600 font-medium' : ''}>Summary</span>
          </div>
        </div>

        <AnimatePresence mode="wait">
          {/* Step 1: Asset Protection Education */}
          {currentStep === 1 && (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-8"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-amber-100 rounded-full mb-4">
                    <Shield className="h-8 w-8 text-amber-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">
                    {assetProtectionEducation.title}
                  </h2>
                  <p className="text-gray-600 max-w-2xl mx-auto">
                    {assetProtectionEducation.description}
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  <Card className="p-6 bg-gradient-to-br from-amber-50 to-orange-50 border-amber-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-amber-100 rounded-full mb-4">
                        <Shield className="h-6 w-6 text-amber-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Legal Protection</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Structure your assets legally to protect from creditors and legal claims while maintaining control.
                      </p>
                      <div className="text-xs text-amber-600 bg-amber-50 rounded-lg p-2">
                        Trusts, companies, joint ownership
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6 bg-gradient-to-br from-orange-50 to-red-50 border-orange-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-orange-100 rounded-full mb-4">
                        <AlertTriangle className="h-6 w-6 text-orange-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Risk Management</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Identify and mitigate risks to your assets through insurance, diversification, and planning.
                      </p>
                      <div className="text-xs text-orange-600 bg-orange-50 rounded-lg p-2">
                        Insurance, diversification, planning
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6 bg-gradient-to-br from-red-50 to-pink-50 border-red-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-red-100 rounded-full mb-4">
                        <Target className="h-6 w-6 text-red-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Strategic Planning</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Plan early and regularly review your protection strategies as your wealth grows.
                      </p>
                      <div className="text-xs text-red-600 bg-red-50 rounded-lg p-2">
                        Early planning, regular reviews
                      </div>
                    </div>
                  </Card>
                </div>

                <div className="space-y-6 mb-8">
                  <h3 className="font-bold text-gray-800 text-center">Key Protection Principles</h3>
                  
                  <div className="grid md:grid-cols-1 gap-4">
                    {assetProtectionEducation.keyPrinciples.map((principle, index) => (
                      <div key={index} className="flex items-start gap-3 p-4 bg-amber-50 rounded-lg">
                        <div className="w-6 h-6 bg-amber-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-amber-600" />
                        </div>
                        <p className="text-sm text-gray-700">{principle}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6 mb-8">
                  <div>
                    <h4 className="font-bold text-gray-800 mb-3">Common Protection Strategies</h4>
                    <div className="space-y-2">
                      {assetProtectionEducation.commonStrategies.map((strategy, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm text-gray-700">
                          <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
                          {strategy}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-bold text-gray-800 mb-3">Warning Flags</h4>
                    <div className="space-y-2">
                      {assetProtectionEducation.warningFlags.map((warning, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm text-gray-700">
                          <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
                          {warning}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <Alert className="border-amber-200 bg-amber-50">
                  <Info className="h-4 w-4 text-amber-600" />
                  <AlertDescription className="text-amber-800">
                    <strong>Important:</strong> Asset protection strategies should be implemented with professional legal and tax advice. 
                    All structures must have legitimate business purposes and comply with relevant laws.
                  </AlertDescription>
                </Alert>
              </Card>
            </motion.div>
          )}

          {/* Step 2: Asset Inventory */}
          {currentStep === 2 && (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="flex justify-between items-center mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">Asset Inventory</h2>
                    <p className="text-gray-600">
                      List all your valuable assets to assess protection needs and strategies.
                    </p>
                  </div>
                  <Button 
                    onClick={() => setShowAddAsset(true)}
                    className="bg-amber-600 hover:bg-amber-700"
                  >
                    <Plus size={16} className="mr-2" />
                    Add Asset
                  </Button>
                </div>

                {/* Asset Categories Reference */}
                <div className="mb-6">
                  <h3 className="font-bold text-gray-800 mb-3">Asset Categories</h3>
                  <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {assetCategories.slice(0, 6).map(category => {
                      const IconComponent = getIconComponent(category.icon);
                      return (
                        <div key={category.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
                          <IconComponent className="h-4 w-4" style={{ color: category.color }} />
                          <span className="text-sm font-medium text-gray-700">{category.name}</span>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Existing Assets */}
                <div className="space-y-4 mb-6">
                  {assets.length > 0 ? (
                    assets.map((asset) => {
                      const category = assetCategories.find(cat => cat.id === asset.category);
                      const IconComponent = getIconComponent(category?.icon || 'Shield');
                      
                      return (
                        <Card key={asset.id} className="p-6 bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200">
                          <div className="flex items-start gap-4">
                            <div 
                              className="w-16 h-16 rounded-lg flex items-center justify-center flex-shrink-0"
                              style={{ backgroundColor: `${category?.color}20` }}
                            >
                              <IconComponent className="h-8 w-8" style={{ color: category?.color }} />
                            </div>
                            
                            <div className="flex-1">
                              <div className="flex items-start justify-between mb-2">
                                <div>
                                  <h3 className="font-bold text-gray-800 mb-1">{asset.name}</h3>
                                  <div className="flex items-center gap-4 text-sm text-gray-600">
                                    <span>Category: {category?.name}</span>
                                    {asset.estimatedValue && (
                                      <span>Value: {formatAmount(asset.estimatedValue)}</span>
                                    )}
                                    <Badge 
                                      variant="outline" 
                                      style={{ 
                                        borderColor: getAssetRiskColor(asset.riskLevel),
                                        color: getAssetRiskColor(asset.riskLevel)
                                      }}
                                    >
                                      {asset.riskLevel.toUpperCase()} RISK
                                    </Badge>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Badge 
                                    variant={asset.isProtected ? "default" : "destructive"}
                                    className="text-xs"
                                  >
                                    {asset.isProtected ? 'Protected' : 'Not Protected'}
                                  </Badge>
                                  <Button 
                                    variant="outline" 
                                    size="sm"
                                    onClick={() => handleRemoveAsset(asset.id)}
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 size={16} />
                                  </Button>
                                </div>
                              </div>
                              
                              {asset.description && (
                                <p className="text-sm text-gray-600 mb-2">{asset.description}</p>
                              )}
                              
                              {asset.notes && (
                                <p className="text-xs text-gray-500 italic">Note: {asset.notes}</p>
                              )}
                            </div>
                          </div>
                        </Card>
                      );
                    })
                  ) : (
                    <Card className="p-8 text-center bg-gray-50">
                      <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-4">No assets added yet</p>
                      <Button 
                        onClick={() => setShowAddAsset(true)}
                        className="bg-amber-600 hover:bg-amber-700"
                      >
                        Add Your First Asset
                      </Button>
                    </Card>
                  )}
                </div>

                {/* Add Asset Form */}
                {showAddAsset && (
                  <Card className="p-6 bg-gradient-to-r from-orange-50 to-amber-50 border-orange-200 mb-6">
                    <h3 className="font-bold text-gray-800 mb-4">Add Asset</h3>
                    <div className="space-y-4">
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Asset Name *
                          </label>
                          <Input
                            placeholder="Family Home, Investment Portfolio, etc."
                            value={newAsset.name}
                            onChange={(e) => setNewAsset({...newAsset, name: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Category *
                          </label>
                          <Select
                            value={newAsset.category}
                            onValueChange={(value: any) => setNewAsset({...newAsset, category: value})}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {assetCategories.map(category => (
                                <SelectItem key={category.id} value={category.id}>{category.name}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Estimated Value
                          </label>
                          <Input
                            type="number"
                            placeholder="500000"
                            value={newAsset.estimatedValue}
                            onChange={(e) => setNewAsset({...newAsset, estimatedValue: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Risk Level
                          </label>
                          <Select
                            value={newAsset.riskLevel}
                            onValueChange={(value: any) => setNewAsset({...newAsset, riskLevel: value})}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {riskLevels.map(level => (
                                <SelectItem key={level.id} value={level.id}>{level.label}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Description
                        </label>
                        <Input
                          placeholder="Brief description of the asset..."
                          value={newAsset.description}
                          onChange={(e) => setNewAsset({...newAsset, description: e.target.value})}
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Notes
                        </label>
                        <Textarea
                          placeholder="Additional notes about this asset..."
                          value={newAsset.notes}
                          onChange={(e) => setNewAsset({...newAsset, notes: e.target.value})}
                          rows={3}
                        />
                      </div>
                    </div>
                    
                    <div className="flex gap-3 mt-6">
                      <Button 
                        onClick={handleAddAsset}
                        disabled={!newAsset.name.trim()}
                        className="bg-amber-600 hover:bg-amber-700"
                      >
                        Add Asset
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => setShowAddAsset(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </Card>
                )}

                {/* Summary */}
                {assets.length > 0 && (
                  <Card className="p-6 bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200">
                    <div className="grid md:grid-cols-4 gap-6 text-center">
                      <div>
                        <p className="text-sm text-gray-600">Total Assets</p>
                        <p className="text-2xl font-bold text-amber-600">{assets.length}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Total Value</p>
                        <p className="text-2xl font-bold text-orange-600">
                          {formatAmount(assetProtectionData.totalAssetValue)}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">High Risk</p>
                        <p className="text-2xl font-bold text-red-600">
                          {assets.filter(a => a.riskLevel === 'high').length}
                        </p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Protected</p>
                        <p className="text-2xl font-bold text-green-600">
                          {assetProtectionData.protectedAssets}
                        </p>
                      </div>
                    </div>
                  </Card>
                )}
              </Card>
            </motion.div>
          )}

          {/* Step 3: Protection Review */}
          {currentStep === 3 && (
            <motion.div
              key="step3"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-amber-100 rounded-full mb-4">
                    <Target className="h-8 w-8 text-amber-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Asset Protection Review</h2>
                  <p className="text-gray-600">
                    Review each asset's protection status and implement appropriate protection strategies.
                  </p>
                </div>

                {/* Protection Score */}
                <Card className={`p-6 mb-6 bg-gradient-to-r ${
                  protectionEffectiveness.status === 'excellent' || protectionEffectiveness.status === 'good'
                    ? 'from-green-50 to-emerald-50 border-green-200'
                    : protectionEffectiveness.status === 'fair'
                    ? 'from-yellow-50 to-orange-50 border-yellow-200'
                    : 'from-red-50 to-pink-50 border-red-200'
                }`}>
                  <div className="text-center">
                    <h3 className="font-bold text-gray-800 mb-2">Asset Protection Score</h3>
                    <div className="text-4xl font-bold mb-2" style={{
                      color: protectionEffectiveness.status === 'excellent' || protectionEffectiveness.status === 'good' ? '#059669' :
                             protectionEffectiveness.status === 'fair' ? '#D97706' : '#DC2626'
                    }}>
                      {Math.round(protectionEffectiveness.score)}%
                    </div>
                    <p className="text-gray-600 mb-4">{protectionEffectiveness.message}</p>
                    <Progress value={protectionEffectiveness.score} className="h-3 max-w-md mx-auto" />
                  </div>
                </Card>

                {/* Asset Protection Status */}
                <div className="space-y-4 mb-6">
                  <h3 className="font-bold text-gray-800">Asset Protection Status</h3>
                  {sortedAssets.length > 0 ? (
                    sortedAssets.map((asset) => {
                      const category = assetCategories.find(cat => cat.id === asset.category);
                      const IconComponent = getIconComponent(category?.icon || 'Shield');
                      
                      return (
                        <Card key={asset.id} className="p-6 bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200">
                          <div className="flex items-start gap-4">
                            <div 
                              className="w-16 h-16 rounded-lg flex items-center justify-center flex-shrink-0"
                              style={{ backgroundColor: `${category?.color}20` }}
                            >
                              <IconComponent className="h-8 w-8" style={{ color: category?.color }} />
                            </div>
                            
                            <div className="flex-1">
                              <div className="flex items-start justify-between mb-3">
                                <div>
                                  <h4 className="font-bold text-gray-800 mb-1">{asset.name}</h4>
                                  <div className="flex items-center gap-4 text-sm text-gray-600">
                                    <span>{category?.name}</span>
                                    {asset.estimatedValue && (
                                      <span>Value: {formatAmount(asset.estimatedValue)}</span>
                                    )}
                                    <Badge 
                                      variant="outline" 
                                      style={{ 
                                        borderColor: getAssetRiskColor(asset.riskLevel),
                                        color: getAssetRiskColor(asset.riskLevel)
                                      }}
                                    >
                                      {asset.riskLevel.toUpperCase()} RISK
                                    </Badge>
                                  </div>
                                </div>
                                
                                <div className="flex items-center gap-2">
                                  <span className="text-sm text-gray-600">Protected?</span>
                                  <Switch
                                    checked={asset.isProtected}
                                    onCheckedChange={(checked) => handleUpdateProtectionStatus(asset.id, checked)}
                                  />
                                </div>
                              </div>
                              
                              {asset.isProtected && asset.protectionMethod && (
                                <div className="p-3 bg-green-50 rounded-lg mb-3">
                                  <p className="text-sm font-medium text-green-800">
                                    Protection: {asset.protectionMethod}
                                  </p>
                                  {asset.protectionDetails && (
                                    <p className="text-xs text-green-700 mt-1">{asset.protectionDetails}</p>
                                  )}
                                </div>
                              )}
                              
                              {asset.isProtected && !asset.protectionMethod && (
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedAsset(asset);
                                    setProtectionMethod('');
                                    setProtectionDetails('');
                                  }}
                                  className="text-amber-600 hover:text-amber-700"
                                >
                                  Add Protection Details
                                </Button>
                              )}
                              
                              {!asset.isProtected && (
                                <Alert className="border-orange-200 bg-orange-50 mt-3">
                                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                                  <AlertDescription className="text-orange-800">
                                    This asset is not protected. Consider implementing protection strategies.
                                  </AlertDescription>
                                </Alert>
                              )}
                            </div>
                          </div>
                        </Card>
                      );
                    })
                  ) : (
                    <Card className="p-8 text-center bg-gray-50">
                      <p className="text-gray-600">No assets to review. Add assets in the previous step.</p>
                    </Card>
                  )}
                </div>

                {/* Protection Method Modal */}
                {selectedAsset && (
                  <Card className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 mb-6">
                    <h3 className="font-bold text-gray-800 mb-4">
                      Add Protection Details for "{selectedAsset.name}"
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Protection Method
                        </label>
                        <Select
                          value={protectionMethod}
                          onValueChange={setProtectionMethod}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select protection method" />
                          </SelectTrigger>
                          <SelectContent>
                            {commonProtectionMethods.map(method => (
                              <SelectItem key={method} value={method}>{method}</SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Protection Details
                        </label>
                        <Textarea
                          placeholder="Describe the protection structure or method..."
                          value={protectionDetails}
                          onChange={(e) => setProtectionDetails(e.target.value)}
                          rows={3}
                        />
                      </div>
                    </div>
                    
                    <div className="flex gap-3 mt-6">
                      <Button 
                        onClick={() => handleUpdateAssetProtection(selectedAsset.id, protectionMethod, protectionDetails)}
                        disabled={!protectionMethod}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        Save Protection Details
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => setSelectedAsset(null)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </Card>
                )}

                {/* Recommendations */}
                {assetProtectionData.recommendations.length > 0 && (
                  <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <Sparkles className="h-5 w-5 text-blue-600" />
                      Protection Recommendations
                    </h3>
                    <div className="space-y-3">
                      {assetProtectionData.recommendations.map((recommendation, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 bg-white rounded-lg">
                          <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                          <p className="text-gray-700">{recommendation}</p>
                        </div>
                      ))}
                    </div>
                  </Card>
                )}
              </Card>
            </motion.div>
          )}

          {/* Step 4: Summary */}
          {currentStep === 4 && (
            <motion.div
              key="step4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-amber-100 rounded-full mb-4">
                    <CheckCircle className="h-8 w-8 text-amber-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Asset Protection Summary</h2>
                  <p className="text-gray-600">
                    Your asset protection strategy is now in place. Continue Stage 3: Protect to complete your wealth protection plan.
                  </p>
                </div>

                <div className="space-y-6">
                  {/* Overall Protection Score */}
                  <Card className={`p-6 bg-gradient-to-r ${
                    protectionEffectiveness.status === 'excellent' || protectionEffectiveness.status === 'good'
                      ? 'from-green-50 to-emerald-50 border-green-200'
                      : protectionEffectiveness.status === 'fair'
                      ? 'from-yellow-50 to-orange-50 border-yellow-200'
                      : 'from-red-50 to-pink-50 border-red-200'
                  }`}>
                    <div className="text-center">
                      <h3 className="font-bold text-gray-800 mb-2">Asset Protection Health</h3>
                      <p className="text-3xl font-bold mb-2" style={{
                        color: protectionEffectiveness.status === 'excellent' || protectionEffectiveness.status === 'good' ? '#059669' :
                               protectionEffectiveness.status === 'fair' ? '#D97706' : '#DC2626'
                      }}>
                        {Math.round(protectionEffectiveness.score)}% - {protectionEffectiveness.message}
                      </p>
                      <div className="grid md:grid-cols-4 gap-4 mt-4">
                        <div>
                          <p className="text-sm text-gray-600">Assets</p>
                          <p className="font-bold text-amber-600">{assets.length}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Total Value</p>
                          <p className="font-bold text-orange-600">{formatAmount(assetProtectionData.totalAssetValue)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Protected</p>
                          <p className="font-bold text-green-600">{assetProtectionData.protectedAssets}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Need Attention</p>
                          <p className="font-bold text-red-600">{assetProtectionData.unprotectedAssets}</p>
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* Asset Portfolio Summary */}
                  <Card className="p-6 bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200">
                    <h3 className="font-bold text-gray-800 mb-4 text-center">Asset Portfolio</h3>
                    <div className="space-y-3">
                      {assets.map((asset) => {
                        const category = assetCategories.find(cat => cat.id === asset.category);
                        return (
                          <div key={asset.id} className="flex items-center justify-between p-3 bg-white rounded-lg">
                            <div className="flex items-center gap-3">
                              <Shield className={`h-5 w-5 ${asset.isProtected ? 'text-green-600' : 'text-red-600'}`} />
                              <div>
                                <p className="font-medium text-gray-800">{asset.name}</p>
                                <p className="text-sm text-gray-600">
                                  {category?.name} • {asset.riskLevel.toUpperCase()} risk
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-amber-600">
                                {asset.estimatedValue ? formatAmount(asset.estimatedValue) : 'N/A'}
                              </p>
                              <p className={`text-xs ${asset.isProtected ? 'text-green-600' : 'text-red-600'}`}>
                                {asset.isProtected ? 'Protected' : 'Not Protected'}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </Card>

                  {/* Next Steps */}
                  <Card className="p-6 bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <Sparkles className="h-5 w-5 text-purple-600" />
                      What Happens Next
                    </h3>
                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="w-12 h-12 bg-amber-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <FileText className="h-6 w-6 text-amber-600" />
                        </div>
                        <h4 className="font-bold text-gray-800 mb-2">Professional Review</h4>
                        <p className="text-sm text-gray-600">Consider professional legal advice for complex protection strategies.</p>
                      </div>
                      
                      <div className="text-center">
                        <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Users className="h-6 w-6 text-orange-600" />
                        </div>
                        <h4 className="font-bold text-gray-800 mb-2">Regular Reviews</h4>
                        <p className="text-sm text-gray-600">Review asset protection annually as your wealth and circumstances change.</p>
                      </div>
                      
                      <div className="text-center">
                        <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Target className="h-6 w-6 text-red-600" />
                        </div>
                        <h4 className="font-bold text-gray-800 mb-2">Implementation</h4>
                        <p className="text-sm text-gray-600">Implement protection strategies for unprotected high-value assets.</p>
                      </div>
                    </div>
                  </Card>

                  {/* Success Message */}
                  <Alert className="border-amber-200 bg-amber-50">
                    <CheckCircle className="h-4 w-4 text-amber-600" />
                    <AlertDescription className="text-amber-800">
                      <strong>Well Done!</strong> You've completed asset protection planning in Stage 3: Protect! Your asset inventory and protection strategies are now organized to safeguard your wealth from potential risks and claims.
                    </AlertDescription>
                  </Alert>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8">
          <Button
            variant="outline"
            onClick={handleBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft size={16} />
            {currentStep === 1 ? 'Back to Dashboard' : 'Back'}
          </Button>
          
          <div className="flex gap-3">
            {currentStep < 4 ? (
              <Button
                onClick={handleNext}
                disabled={currentStep === 2 && assets.length === 0}
                className="flex items-center gap-2 bg-amber-600 hover:bg-amber-700"
              >
                Continue
                <ArrowRight size={16} />
              </Button>
            ) : (
              <Button
                onClick={handleComplete}
                className="flex items-center gap-2 bg-amber-600 hover:bg-amber-700"
              >
                Complete Asset Protection
                <CheckCircle size={16} />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}