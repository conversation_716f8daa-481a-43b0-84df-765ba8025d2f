import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { 
  FileText, 
  ArrowLeft, 
  ArrowRight, 
  Users, 
  CheckCircle, 
  Plus,
  Info,
  Trash2,
  Upload,
  Download,
  AlertTriangle,
  Sparkles,
  Heart,
  Shield,
  Target,
  Calendar,
  Eye,
  EyeOff,
  RefreshCw,
  Calculator,
  Folder
} from "lucide-react";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Progress } from "./ui/progress";
import { Alert, AlertDescription } from "./ui/alert";
import { Badge } from "./ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Switch } from "./ui/switch";
import { Textarea } from "./ui/textarea";
import { Checkbox } from "./ui/checkbox";
import { Will, PowerOfAttorney, ChecklistItem, EstatePlanningData } from "../types/estatePlanning";
import { 
  estatePlanningEducation, 
  willGuidelines, 
  defaultChecklistItems, 
  willTypes, 
  documentTypes, 
  relationshipTypes,
  priorityLevels,
  categories
} from "../constants/estatePlanning";
import { 
  calculateEstatePlanningData,
  getEstatePlanningStatus,
  sortChecklistItems,
  getCompletionMessage,
  formatFileSize,
  isValidFileType,
  getFileTypeIcon,
  formatAmount
} from "../utils/estatePlanning";
import obieLogo from 'figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png';

interface FinancialData {
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlySavings: number;
  monthlyInvestment: number;
  availableFromIncome: number;
  availableCashSavings: number;
}

interface EstatePlanningProps {
  financialData: FinancialData;
  totalAssets: number;
  onComplete: (data: EstatePlanningData) => void;
  onBack: () => void;
}

export function EstatePlanning({ financialData, totalAssets, onComplete, onBack }: EstatePlanningProps) {
  const [currentStep, setCurrentStep] = useState(1);
  
  // Will state
  const [will, setWill] = useState<Will>({
    id: Date.now().toString(),
    hasWill: false,
    willType: 'simple',
    isVerified: false,
    createdDate: new Date().toISOString(),
    updatedDate: new Date().toISOString()
  });

  // Power of Attorney state
  const [powerOfAttorney, setPowerOfAttorney] = useState<PowerOfAttorney>({
    id: Date.now().toString(),
    hasFinancialPOA: false,
    hasMedicalPOA: false,
    isCompleted: false
  });

  // Checklist state
  const [checklistItems, setChecklistItems] = useState<ChecklistItem[]>(defaultChecklistItems);
  const [showCompletedItems, setShowCompletedItems] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  // Document upload state
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [dragActive, setDragActive] = useState(false);

  // Calculate estate planning data
  const estatePlanningData = calculateEstatePlanningData(will, powerOfAttorney, checklistItems, totalAssets);
  const planningStatus = getEstatePlanningStatus(estatePlanningData.completionScore);
  const sortedItems = sortChecklistItems(checklistItems);
  const filteredItems = sortedItems.filter(item => {
    if (!showCompletedItems && item.isCompleted) return false;
    if (selectedCategory !== 'all' && item.category !== selectedCategory) return false;
    return true;
  });

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      onBack();
    }
  };

  const handleUpdateWill = (updates: Partial<Will>) => {
    setWill({
      ...will,
      ...updates,
      updatedDate: new Date().toISOString()
    });
  };

  const handleUpdatePowerOfAttorney = (updates: Partial<PowerOfAttorney>) => {
    setPowerOfAttorney({
      ...powerOfAttorney,
      ...updates,
      isCompleted: updates.hasFinancialPOA && updates.hasMedicalPOA
    });
  };

  const handleToggleChecklistItem = (itemId: string) => {
    setChecklistItems(items => 
      items.map(item => 
        item.id === itemId 
          ? { 
              ...item, 
              isCompleted: !item.isCompleted,
              completedDate: !item.isCompleted ? new Date().toISOString() : undefined
            }
          : item
      )
    );
  };

  const handleUpdateChecklistItem = (itemId: string, updates: Partial<ChecklistItem>) => {
    setChecklistItems(items => 
      items.map(item => 
        item.id === itemId 
          ? { ...item, ...updates }
          : item
      )
    );
  };

  const handleFileUpload = (files: FileList) => {
    Array.from(files).forEach(file => {
      if (isValidFileType(file.name)) {
        const newFile = {
          id: Date.now().toString() + Math.random(),
          fileName: file.name,
          fileType: file.type,
          fileSize: file.size,
          uploadDate: new Date().toISOString(),
          documentType: 'will',
          isVerified: false
        };
        setUploadedFiles(prev => [...prev, newFile]);
      }
    });
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    if (e.dataTransfer.files) {
      handleFileUpload(e.dataTransfer.files);
    }
  };

  const handleComplete = () => {
    const finalData = calculateEstatePlanningData(will, powerOfAttorney, checklistItems, totalAssets);
    onComplete(finalData);
  };

  const getIconComponent = (iconName: string) => {
    const icons = {
      FileText,
      Users,
      Heart,
      Folder,
      Calculator,
      RefreshCw,
      Shield
    };
    return icons[iconName as keyof typeof icons] || FileText;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 via-indigo-50 to-blue-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center gap-4">
            <img src={obieLogo} alt="Obiemoney Logo" className="h-10 w-auto" />
            <div>
              <h1 className="text-3xl font-bold text-purple-600">Estate Planning</h1>
              <p className="text-gray-600">Step 9: Who's in charge when I can't be?</p>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-xs bg-purple-100 text-purple-600 px-2 py-1 rounded-full font-medium">
                  STAGE 3: PROTECT - FINAL STEP
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 bg-white/60 rounded-full px-4 py-2">
              <span className="text-sm font-medium text-gray-600">Step {currentStep} of 4</span>
            </div>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <div className="mb-8">
          <Progress value={(currentStep / 4) * 100} className="h-2 bg-white/60" />
          <div className="flex justify-between mt-2 text-sm text-gray-600">
            <span className={currentStep >= 1 ? 'text-purple-600 font-medium' : ''}>Education</span>
            <span className={currentStep >= 2 ? 'text-purple-600 font-medium' : ''}>Will & POA</span>
            <span className={currentStep >= 3 ? 'text-purple-600 font-medium' : ''}>Checklist</span>
            <span className={currentStep >= 4 ? 'text-purple-600 font-medium' : ''}>Complete</span>
          </div>
        </div>

        <AnimatePresence mode="wait">
          {/* Step 1: Estate Planning Education */}
          {currentStep === 1 && (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-8"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-purple-100 rounded-full mb-4">
                    <FileText className="h-8 w-8 text-purple-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">
                    {estatePlanningEducation.title}
                  </h2>
                  <p className="text-gray-600 max-w-2xl mx-auto">
                    {estatePlanningEducation.description}
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  <Card className="p-6 bg-gradient-to-br from-purple-50 to-indigo-50 border-purple-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-purple-100 rounded-full mb-4">
                        <FileText className="h-6 w-6 text-purple-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Legal Documents</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Create essential legal documents that clearly state your wishes and protect your family.
                      </p>
                      <div className="text-xs text-purple-600 bg-purple-50 rounded-lg p-2">
                        Will, Power of Attorney, Trusts
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6 bg-gradient-to-br from-indigo-50 to-blue-50 border-indigo-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-indigo-100 rounded-full mb-4">
                        <Users className="h-6 w-6 text-indigo-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Decision Makers</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Appoint trusted people to make decisions on your behalf when you cannot.
                      </p>
                      <div className="text-xs text-indigo-600 bg-indigo-50 rounded-lg p-2">
                        Executors, Attorneys, Guardians
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6 bg-gradient-to-br from-blue-50 to-teal-50 border-blue-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-blue-100 rounded-full mb-4">
                        <Shield className="h-6 w-6 text-blue-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-3">Asset Protection</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Ensure your assets are distributed according to your wishes while minimizing taxes.
                      </p>
                      <div className="text-xs text-blue-600 bg-blue-50 rounded-lg p-2">
                        Tax planning, Asset distribution
                      </div>
                    </div>
                  </Card>
                </div>

                <div className="space-y-6 mb-8">
                  <h3 className="font-bold text-gray-800 text-center">Key Estate Planning Components</h3>
                  
                  <div className="grid md:grid-cols-2 gap-4">
                    {estatePlanningEducation.keyComponents.slice(0, 4).map((component, index) => (
                      <div key={index} className="flex items-start gap-3 p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg">
                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-purple-600 font-bold text-sm">{index + 1}</span>
                        </div>
                        <div>
                          <h4 className="font-bold text-gray-800 mb-1">{component.name}</h4>
                          <p className="text-sm text-gray-700 mb-2">{component.description}</p>
                          <div className="flex items-center gap-2 text-xs">
                            <Badge variant={component.importance === 'essential' ? 'default' : 'secondary'}>
                              {component.importance.toUpperCase()}
                            </Badge>
                            <span className="text-gray-500">{component.timeframe}</span>
                            <span className="text-gray-500">•</span>
                            <span className="text-gray-500">{component.cost.toUpperCase()} cost</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6 mb-8">
                  <div>
                    <h4 className="font-bold text-gray-800 mb-3">Why Estate Planning Matters</h4>
                    <div className="space-y-2">
                      {estatePlanningEducation.importanceFactors.slice(0, 4).map((factor, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm text-gray-700">
                          <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
                          {factor}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-bold text-gray-800 mb-3">Common Mistakes to Avoid</h4>
                    <div className="space-y-2">
                      {estatePlanningEducation.commonMistakes.slice(0, 4).map((mistake, index) => (
                        <div key={index} className="flex items-center gap-2 text-sm text-gray-700">
                          <div className="w-2 h-2 bg-red-500 rounded-full flex-shrink-0"></div>
                          {mistake}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <Alert className="border-purple-200 bg-purple-50">
                  <Info className="h-4 w-4 text-purple-600" />
                  <AlertDescription className="text-purple-800">
                    <strong>Important:</strong> While this tool helps you organize your estate planning, 
                    always seek professional legal advice for complex situations or when creating legal documents.
                  </AlertDescription>
                </Alert>
              </Card>
            </motion.div>
          )}

          {/* Step 2: Will & Power of Attorney */}
          {currentStep === 2 && (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-purple-100 rounded-full mb-4">
                    <FileText className="h-8 w-8 text-purple-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Will & Power of Attorney</h2>
                  <p className="text-gray-600">
                    Set up your essential estate planning documents and appoint trusted decision-makers.
                  </p>
                </div>

                {/* Will Section */}
                <div className="space-y-6 mb-8">
                  <h3 className="font-bold text-gray-800">Your Will</h3>
                  
                  <div className="p-6 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-lg">
                    <div className="flex items-center gap-3 mb-4">
                      <Checkbox
                        checked={will.hasWill}
                        onCheckedChange={(checked) => handleUpdateWill({ hasWill: checked as boolean })}
                      />
                      <label className="font-medium text-gray-800">
                        I have a valid will
                      </label>
                    </div>

                    {will.hasWill && (
                      <div className="space-y-4 ml-6">
                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Will Type
                            </label>
                            <Select
                              value={will.willType}
                              onValueChange={(value: any) => handleUpdateWill({ willType: value })}
                            >
                              <SelectTrigger>
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {willTypes.map(type => (
                                  <SelectItem key={type.id} value={type.id}>{type.name}</SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Last Updated
                            </label>
                            <Input
                              type="date"
                              value={will.lastUpdated || ''}
                              onChange={(e) => handleUpdateWill({ lastUpdated: e.target.value })}
                            />
                          </div>
                        </div>

                        <div className="grid md:grid-cols-2 gap-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Executor Name
                            </label>
                            <Input
                              placeholder="Full name of your executor"
                              value={will.executorName || ''}
                              onChange={(e) => handleUpdateWill({ executorName: e.target.value })}
                            />
                          </div>
                          
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                              Executor Contact
                            </label>
                            <Input
                              placeholder="Phone or email"
                              value={will.executorContact || ''}
                              onChange={(e) => handleUpdateWill({ executorContact: e.target.value })}
                            />
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Lawyer Details
                          </label>
                          <div className="grid md:grid-cols-2 gap-4">
                            <Input
                              placeholder="Lawyer name"
                              value={will.lawyerName || ''}
                              onChange={(e) => handleUpdateWill({ lawyerName: e.target.value })}
                            />
                            <Input
                              placeholder="Lawyer contact"
                              value={will.lawyerContact || ''}
                              onChange={(e) => handleUpdateWill({ lawyerContact: e.target.value })}
                            />
                          </div>
                        </div>

                        <div className="flex items-center gap-3">
                          <Checkbox
                            checked={will.isVerified}
                            onCheckedChange={(checked) => handleUpdateWill({ isVerified: checked as boolean })}
                          />
                          <label className="text-sm text-gray-700">
                            My will has been reviewed by a qualified lawyer
                          </label>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Will Guidelines */}
                  <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                    <h4 className="font-bold text-gray-800 mb-3">Will Requirements Checklist</h4>
                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <h5 className="font-medium text-gray-700 mb-2">Basic Requirements:</h5>
                        <div className="space-y-1">
                          {willGuidelines.basicRequirements.slice(0, 4).map((req, index) => (
                            <div key={index} className="flex items-center gap-2 text-sm text-gray-600">
                              <CheckCircle className="h-3 w-3 text-green-600 flex-shrink-0" />
                              {req}
                            </div>
                          ))}
                        </div>
                      </div>
                      <div>
                        <h5 className="font-medium text-gray-700 mb-2">When to Update:</h5>
                        <div className="space-y-1">
                          {willGuidelines.whenToUpdate.slice(0, 4).map((when, index) => (
                            <div key={index} className="flex items-center gap-2 text-sm text-gray-600">
                              <AlertTriangle className="h-3 w-3 text-orange-600 flex-shrink-0" />
                              {when}
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </Card>
                </div>

                {/* Power of Attorney Section */}
                <div className="space-y-6">
                  <h3 className="font-bold text-gray-800">Power of Attorney</h3>
                  
                  <div className="p-6 bg-gradient-to-r from-indigo-50 to-blue-50 rounded-lg">
                    <div className="space-y-4">
                      <div className="flex items-center gap-3">
                        <Checkbox
                          checked={powerOfAttorney.hasFinancialPOA}
                          onCheckedChange={(checked) => handleUpdatePowerOfAttorney({ hasFinancialPOA: checked as boolean })}
                        />
                        <label className="font-medium text-gray-800">
                          I have a Financial Power of Attorney
                        </label>
                      </div>

                      {powerOfAttorney.hasFinancialPOA && (
                        <div className="ml-6 grid md:grid-cols-2 gap-4">
                          <Input
                            placeholder="Financial attorney name"
                            value={powerOfAttorney.financialAttorneyName || ''}
                            onChange={(e) => handleUpdatePowerOfAttorney({ financialAttorneyName: e.target.value })}
                          />
                          <Input
                            placeholder="Contact information"
                            value={powerOfAttorney.financialAttorneyContact || ''}
                            onChange={(e) => handleUpdatePowerOfAttorney({ financialAttorneyContact: e.target.value })}
                          />
                        </div>
                      )}

                      <div className="flex items-center gap-3">
                        <Checkbox
                          checked={powerOfAttorney.hasMedicalPOA}
                          onCheckedChange={(checked) => handleUpdatePowerOfAttorney({ hasMedicalPOA: checked as boolean })}
                        />
                        <label className="font-medium text-gray-800">
                          I have a Medical Power of Attorney
                        </label>
                      </div>

                      {powerOfAttorney.hasMedicalPOA && (
                        <div className="ml-6 grid md:grid-cols-2 gap-4">
                          <Input
                            placeholder="Medical attorney name"
                            value={powerOfAttorney.medicalAttorneyName || ''}
                            onChange={(e) => handleUpdatePowerOfAttorney({ medicalAttorneyName: e.target.value })}
                          />
                          <Input
                            placeholder="Contact information"
                            value={powerOfAttorney.medicalAttorneyContact || ''}
                            onChange={(e) => handleUpdatePowerOfAttorney({ medicalAttorneyContact: e.target.value })}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Document Upload Section */}
                <div className="space-y-6 mt-8">
                  <h3 className="font-bold text-gray-800">Document Storage</h3>
                  
                  <div 
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                      dragActive ? 'border-purple-500 bg-purple-50' : 'border-gray-300 bg-gray-50'
                    }`}
                    onDragOver={handleDragOver}
                    onDragLeave={handleDragLeave}
                    onDrop={handleDrop}
                  >
                    <Upload className="h-8 w-8 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 mb-2">Drag and drop your estate planning documents here</p>
                    <p className="text-sm text-gray-500 mb-4">Supported formats: PDF, DOC, DOCX, JPG, PNG</p>
                    <Button variant="outline" onClick={() => document.getElementById('fileInput')?.click()}>
                      Choose Files
                    </Button>
                    <input
                      id="fileInput"
                      type="file"
                      multiple
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                      className="hidden"
                      onChange={(e) => e.target.files && handleFileUpload(e.target.files)}
                    />
                  </div>

                  {/* Uploaded Files */}
                  {uploadedFiles.length > 0 && (
                    <div className="space-y-3">
                      <h4 className="font-medium text-gray-800">Uploaded Documents</h4>
                      {uploadedFiles.map((file) => (
                        <div key={file.id} className="flex items-center justify-between p-3 bg-white rounded-lg border">
                          <div className="flex items-center gap-3">
                            <span className="text-lg">{getFileTypeIcon(file.fileName)}</span>
                            <div>
                              <p className="font-medium text-gray-800">{file.fileName}</p>
                              <p className="text-sm text-gray-500">{formatFileSize(file.fileSize)}</p>
                            </div>
                          </div>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setUploadedFiles(files => files.filter(f => f.id !== file.id))}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </Card>
            </motion.div>
          )}

          {/* Step 3: Estate Planning Checklist */}
          {currentStep === 3 && (
            <motion.div
              key="step3"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-purple-100 rounded-full mb-4">
                    <Target className="h-8 w-8 text-purple-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Estate Planning Checklist</h2>
                  <p className="text-gray-600">
                    Complete these essential tasks to ensure comprehensive estate planning coverage.
                  </p>
                </div>

                {/* Progress Overview */}
                <Card className={`p-6 mb-6 bg-gradient-to-r ${
                  planningStatus.status === 'excellent' || planningStatus.status === 'good'
                    ? 'from-green-50 to-emerald-50 border-green-200'
                    : planningStatus.status === 'in_progress'
                    ? 'from-yellow-50 to-orange-50 border-yellow-200'
                    : 'from-red-50 to-pink-50 border-red-200'
                }`}>
                  <div className="text-center">
                    <h3 className="font-bold text-gray-800 mb-2">Estate Planning Progress</h3>
                    <div className="text-4xl font-bold mb-2" style={{ color: planningStatus.color }}>
                      {estatePlanningData.completionScore}%
                    </div>
                    <p className="text-gray-600 mb-4">{planningStatus.message}</p>
                    <Progress value={estatePlanningData.completionScore} className="h-3 max-w-md mx-auto" />
                    <p className="text-sm text-gray-600 mt-2">{getCompletionMessage(estatePlanningData.completionScore)}</p>
                  </div>
                </Card>

                {/* Filters and Controls */}
                <div className="flex flex-wrap items-center gap-4 mb-6">
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-48">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories.map(category => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <div className="flex items-center gap-2">
                    <Switch
                      checked={showCompletedItems}
                      onCheckedChange={setShowCompletedItems}
                    />
                    <span className="text-sm text-gray-600">Show completed</span>
                  </div>

                  <div className="flex-1"></div>
                  
                  <div className="text-sm text-gray-600">
                    {filteredItems.filter(item => item.isCompleted).length} of {filteredItems.length} completed
                  </div>
                </div>

                {/* Checklist Items */}
                <div className="space-y-4 mb-6">
                  {filteredItems.map((item) => {
                    const category = categories.find(cat => cat.id === item.category);
                    const IconComponent = getIconComponent(category?.icon || 'FileText');
                    const priority = priorityLevels.find(p => p.id === item.priority);
                    
                    return (
                      <Card 
                        key={item.id} 
                        className={`p-6 transition-all ${
                          item.isCompleted 
                            ? 'bg-green-50 border-green-200' 
                            : 'bg-white hover:shadow-md'
                        }`}
                      >
                        <div className="flex items-start gap-4">
                          <Checkbox
                            checked={item.isCompleted}
                            onCheckedChange={() => handleToggleChecklistItem(item.id)}
                            className="mt-1"
                          />
                          
                          <div 
                            className="w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0"
                            style={{ backgroundColor: `${category?.color}20` }}
                          >
                            <IconComponent className="h-6 w-6" style={{ color: category?.color }} />
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-start justify-between mb-2">
                              <div>
                                <h4 className={`font-bold ${item.isCompleted ? 'text-green-800' : 'text-gray-800'} mb-1`}>
                                  {item.title}
                                </h4>
                                <p className={`text-sm ${item.isCompleted ? 'text-green-600' : 'text-gray-600'} mb-3`}>
                                  {item.description}
                                </p>
                              </div>
                              
                              <div className="flex items-center gap-2">
                                <Badge 
                                  variant="outline" 
                                  style={{ 
                                    borderColor: priority?.color,
                                    color: priority?.color
                                  }}
                                >
                                  {item.priority.toUpperCase()}
                                </Badge>
                                <Badge variant="secondary">
                                  {category?.name}
                                </Badge>
                              </div>
                            </div>
                            
                            {item.completedDate && (
                              <div className="text-xs text-green-600 mb-2">
                                Completed: {new Date(item.completedDate).toLocaleDateString()}
                              </div>
                            )}
                            
                            <Textarea
                              placeholder="Add notes about this item..."
                              value={item.notes || ''}
                              onChange={(e) => handleUpdateChecklistItem(item.id, { notes: e.target.value })}
                              rows={2}
                              className="text-sm"
                            />
                          </div>
                        </div>
                      </Card>
                    );
                  })}
                </div>

                {/* Recommendations */}
                {estatePlanningData.recommendations.length > 0 && (
                  <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <Sparkles className="h-5 w-5 text-blue-600" />
                      Recommendations
                    </h3>
                    <div className="space-y-3">
                      {estatePlanningData.recommendations.map((recommendation, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 bg-white rounded-lg">
                          <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0"></div>
                          <p className="text-gray-700">{recommendation}</p>
                        </div>
                      ))}
                    </div>
                  </Card>
                )}
              </Card>
            </motion.div>
          )}

          {/* Step 4: Summary and Completion */}
          {currentStep === 4 && (
            <motion.div
              key="step4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-purple-100 rounded-full mb-4">
                    <CheckCircle className="h-8 w-8 text-purple-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Estate Planning Complete</h2>
                  <p className="text-gray-600">
                    Congratulations! You've completed the final step of your comprehensive financial journey.
                  </p>
                </div>

                <div className="space-y-6">
                  {/* Final Progress Score */}
                  <Card className={`p-6 bg-gradient-to-r ${
                    planningStatus.status === 'excellent' || planningStatus.status === 'good'
                      ? 'from-green-50 to-emerald-50 border-green-200'
                      : planningStatus.status === 'in_progress'
                      ? 'from-yellow-50 to-orange-50 border-yellow-200'
                      : 'from-red-50 to-pink-50 border-red-200'
                  }`}>
                    <div className="text-center">
                      <h3 className="font-bold text-gray-800 mb-2">Estate Planning Health</h3>
                      <p className="text-3xl font-bold mb-2" style={{ color: planningStatus.color }}>
                        {estatePlanningData.completionScore}% - {planningStatus.message}
                      </p>
                      <div className="grid md:grid-cols-4 gap-4 mt-4">
                        <div>
                          <p className="text-sm text-gray-600">Will Status</p>
                          <p className="font-bold text-purple-600">{will.hasWill ? 'Completed' : 'Pending'}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Power of Attorney</p>
                          <p className="font-bold text-indigo-600">{powerOfAttorney.isCompleted ? 'Completed' : 'Pending'}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Tasks Done</p>
                          <p className="font-bold text-blue-600">{checklistItems.filter(item => item.isCompleted).length}/{checklistItems.length}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Documents</p>
                          <p className="font-bold text-teal-600">{uploadedFiles.length} uploaded</p>
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* Estate Planning Summary */}
                  <Card className="p-6 bg-gradient-to-r from-purple-50 to-indigo-50 border-purple-200">
                    <h3 className="font-bold text-gray-800 mb-4 text-center">Your Estate Planning Summary</h3>
                    <div className="space-y-4">
                      <div className="grid md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-bold text-gray-800 mb-2">Essential Documents</h4>
                          <div className="space-y-2">
                            <div className="flex items-center justify-between p-2 bg-white rounded">
                              <span className="text-gray-700">Will & Testament</span>
                              <Badge variant={will.hasWill ? "default" : "destructive"}>
                                {will.hasWill ? 'Complete' : 'Pending'}
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-white rounded">
                              <span className="text-gray-700">Financial POA</span>
                              <Badge variant={powerOfAttorney.hasFinancialPOA ? "default" : "destructive"}>
                                {powerOfAttorney.hasFinancialPOA ? 'Complete' : 'Pending'}
                              </Badge>
                            </div>
                            <div className="flex items-center justify-between p-2 bg-white rounded">
                              <span className="text-gray-700">Medical POA</span>
                              <Badge variant={powerOfAttorney.hasMedicalPOA ? "default" : "destructive"}>
                                {powerOfAttorney.hasMedicalPOA ? 'Complete' : 'Pending'}
                              </Badge>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-bold text-gray-800 mb-2">Key Information</h4>
                          <div className="space-y-2 text-sm">
                            {will.executorName && (
                              <div className="flex justify-between p-2 bg-white rounded">
                                <span className="text-gray-600">Executor:</span>
                                <span className="font-medium">{will.executorName}</span>
                              </div>
                            )}
                            {will.lastUpdated && (
                              <div className="flex justify-between p-2 bg-white rounded">
                                <span className="text-gray-600">Will Updated:</span>
                                <span className="font-medium">{new Date(will.lastUpdated).toLocaleDateString()}</span>
                              </div>
                            )}
                            <div className="flex justify-between p-2 bg-white rounded">
                              <span className="text-gray-600">Total Estate Value:</span>
                              <span className="font-medium">{formatAmount(totalAssets)}</span>
                            </div>
                            {estatePlanningData.nextReviewDate && (
                              <div className="flex justify-between p-2 bg-white rounded">
                                <span className="text-gray-600">Next Review:</span>
                                <span className="font-medium">{new Date(estatePlanningData.nextReviewDate).toLocaleDateString()}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* 9-Step Journey Complete */}
                  <Card className="p-8 bg-gradient-to-r from-green-50 via-emerald-50 to-teal-50 border-green-200">
                    <div className="text-center">
                      <div className="inline-flex p-4 bg-green-100 rounded-full mb-4">
                        <CheckCircle className="h-8 w-8 text-green-600" />
                      </div>
                      <h2 className="text-2xl font-bold text-gray-800 mb-2">🎉 9-Step Journey Complete!</h2>
                      <p className="text-xl text-green-600 font-medium mb-4">
                        You've mastered your money with Obiemoney's 9 simple steps!
                      </p>
                      <p className="text-gray-700 max-w-2xl mx-auto mb-6">
                        From optimizing your budget to protecting your legacy, you've built a comprehensive financial strategy. 
                        Your money is now working smarter, your wealth is growing, and your future is secured.
                      </p>
                      
                      <div className="grid md:grid-cols-3 gap-6 mb-8">
                        <div className="text-center">
                          <div className="inline-flex p-3 bg-orange-100 rounded-full mb-3">
                            <Target className="h-6 w-6 text-orange-600" />
                          </div>
                          <h4 className="font-bold text-gray-800 mb-2">Stage 1: Optimise</h4>
                          <p className="text-sm text-gray-600">Budget, Safety Net, Debt, Savings</p>
                          <div className="flex justify-center -space-x-1 mt-2">
                            {[1,2,3,4].map(step => (
                              <div key={step} className="w-6 h-6 bg-green-500 rounded-full border border-white flex items-center justify-center">
                                <CheckCircle className="h-3 w-3 text-white" />
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        <div className="text-center">
                          <div className="inline-flex p-3 bg-blue-100 rounded-full mb-3">
                            <TrendingUp className="h-6 w-6 text-blue-600" />
                          </div>
                          <h4 className="font-bold text-gray-800 mb-2">Stage 2: Maximise</h4>
                          <p className="text-sm text-gray-600">Investment, Superannuation</p>
                          <div className="flex justify-center -space-x-1 mt-2">
                            {[5,6].map(step => (
                              <div key={step} className="w-6 h-6 bg-green-500 rounded-full border border-white flex items-center justify-center">
                                <CheckCircle className="h-3 w-3 text-white" />
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        <div className="text-center">
                          <div className="inline-flex p-3 bg-purple-100 rounded-full mb-3">
                            <Shield className="h-6 w-6 text-purple-600" />
                          </div>
                          <h4 className="font-bold text-gray-800 mb-2">Stage 3: Protect</h4>
                          <p className="text-sm text-gray-600">Insurance, Assets, Estate</p>
                          <div className="flex justify-center -space-x-1 mt-2">
                            {[7,8,9].map(step => (
                              <div key={step} className="w-6 h-6 bg-green-500 rounded-full border border-white flex items-center justify-center">
                                <CheckCircle className="h-3 w-3 text-white" />
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      <p className="text-green-600 font-medium">
                        Welcome to financial freedom! 🌟
                      </p>
                    </div>
                  </Card>

                  {/* Next Steps */}
                  <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <Sparkles className="h-5 w-5 text-blue-600" />
                      What Happens Next
                    </h3>
                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Calendar className="h-6 w-6 text-purple-600" />
                        </div>
                        <h4 className="font-bold text-gray-800 mb-2">Regular Reviews</h4>
                        <p className="text-sm text-gray-600">Review your estate plan every 3-5 years or after major life events.</p>
                      </div>
                      
                      <div className="text-center">
                        <div className="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Users className="h-6 w-6 text-indigo-600" />
                        </div>
                        <h4 className="font-bold text-gray-800 mb-2">Inform Family</h4>
                        <p className="text-sm text-gray-600">Share your estate plan details with executors and family members.</p>
                      </div>
                      
                      <div className="text-center">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Shield className="h-6 w-6 text-blue-600" />
                        </div>
                        <h4 className="font-bold text-gray-800 mb-2">Stay Protected</h4>
                        <p className="text-sm text-gray-600">Keep documents secure and update as your circumstances change.</p>
                      </div>
                    </div>
                  </Card>

                  {/* Success Message */}
                  <Alert className="border-purple-200 bg-purple-50">
                    <CheckCircle className="h-4 w-4 text-purple-600" />
                    <AlertDescription className="text-purple-800">
                      <strong>Congratulations!</strong> You've completed all 9 steps of your financial journey. Your estate plan ensures your legacy is protected and your loved ones are cared for according to your wishes.
                    </AlertDescription>
                  </Alert>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8">
          <Button
            variant="outline"
            onClick={handleBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft size={16} />
            {currentStep === 1 ? 'Back to Dashboard' : 'Back'}
          </Button>
          
          <div className="flex gap-3">
            {currentStep < 4 ? (
              <Button
                onClick={handleNext}
                className="flex items-center gap-2 bg-purple-600 hover:bg-purple-700"
              >
                Continue
                <ArrowRight size={16} />
              </Button>
            ) : (
              <Button
                onClick={handleComplete}
                className="flex items-center gap-2 bg-purple-600 hover:bg-purple-700"
              >
                Complete Estate Planning
                <CheckCircle size={16} />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}