import React from 'react';
import { CheckCircle } from 'lucide-react';
import { VerticalStepData } from '../constants/steps';

interface VerticalStepperProps {
  steps: VerticalStepData[];
  completedSteps?: string[];
  currentStep?: string;
  onStepClick?: (stepName: string) => void;
}

export function VerticalStepper({ steps, completedSteps = [], currentStep, onStepClick }: VerticalStepperProps) {
  // Dynamically set step status based on completion state
  const updatedSteps = steps.map(step => {
    if (completedSteps.includes(step.name)) {
      return { ...step, status: "completed" as const };
    } else if (step.name === currentStep) {
      return { ...step, status: "current" as const };
    } else {
      return { ...step, status: "future" as const };
    }
  });

  const handleStepClick = (step: VerticalStepData & { status: "completed" | "current" | "future" }) => {
    // Only allow clicking on completed steps
    if (step.status === "completed" && onStepClick) {
      onStepClick(step.name);
    }
  };

  return (
    <div className="flex flex-col w-full max-w-[202px]">
      {updatedSteps.map((step, index) => (
        <div key={step.id} className="relative">
          {step.status === "current" ? (
            // Active step with purple background and border
            <div className="bg-[rgba(97,48,223,0.1)] box-border flex flex-col gap-1 px-[18px] py-3.5 rounded-[3px] relative border-l-[3px] border-[#6130df]">
              <div className="flex flex-col gap-1 w-full">
                <div className="flex items-center gap-2">
                  <h4 className="text-[#6130df] font-medium leading-[20px]">
                    {step.name}
                  </h4>
                  {step.status === "completed" && (
                    <CheckCircle className="h-4 w-4 text-[#6130df] flex-shrink-0" />
                  )}
                </div>
                <p className="text-[#343434] text-xs leading-[16px]">
                  {step.description}
                </p>
              </div>
            </div>
          ) : (
            // Inactive steps - make completed ones clickable
            <div 
              className={`box-border flex flex-col gap-1 px-[18px] py-3.5 transition-colors duration-200 ${
                step.status === "completed" 
                  ? "cursor-pointer hover:bg-[rgba(97,48,223,0.05)] rounded-[3px]" 
                  : ""
              }`}
              onClick={() => handleStepClick(step)}
            >
              <div className="flex flex-col gap-1 w-full">
                <div className="flex items-center gap-2">
                  <h4 className={`font-medium leading-[20px] transition-colors duration-200 ${
                    step.status === "completed" ? "text-[#6130df]" : "text-[#777777]"
                  }`}>
                    {step.name}
                  </h4>
                  {step.status === "completed" && (
                    <CheckCircle className="h-4 w-4 text-[#6130df] flex-shrink-0" />
                  )}
                </div>
                <p className={`text-xs leading-[16px] transition-colors duration-200 ${
                  step.status === "completed" ? "text-[#343434]" : "text-[#959595]"
                }`}>
                  {step.description}
                </p>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}