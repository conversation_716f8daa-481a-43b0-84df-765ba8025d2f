import { useState } from "react";
import { motion } from "motion/react";
import { 
  Plus, 
  ChevronRight,
  ChevronLeft,
  CheckCircle,
  PiggyBank,
  TrendingUp
} from "lucide-react";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import obieLogo from 'figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png';

interface SavingsInvestment {
  amount: number;
  frequency: 'weekly' | 'monthly' | 'yearly';
}

interface SavingsInvestmentProps {
  onComplete: (data: { savings: SavingsInvestment | null, investment: SavingsInvestment | null }) => void;
  onBack: () => void;
}

export function SavingsInvestment({ onComplete, onBack }: SavingsInvestmentProps) {
  // Savings state
  const [savings, setSavings] = useState({
    amount: '',
    frequency: 'monthly' as const
  });
  const [savingsAdded, setSavingsAdded] = useState(false);

  // Investment state
  const [investment, setInvestment] = useState({
    amount: '',
    frequency: 'monthly' as const
  });
  const [investmentAdded, setInvestmentAdded] = useState(false);

  const addSavings = () => {
    if (!savings.amount) return;
    setSavingsAdded(true);
  };

  const addInvestment = () => {
    if (!investment.amount) return;
    setInvestmentAdded(true);
  };

  const handleComplete = () => {
    onComplete({
      savings: savingsAdded && savings.amount ? { amount: parseFloat(savings.amount), frequency: savings.frequency } : null,
      investment: investmentAdded && investment.amount ? { amount: parseFloat(investment.amount), frequency: investment.frequency } : null
    });
  };

  const canProceed = savingsAdded && investmentAdded;

  return (
    <div className="min-h-screen py-8" style={{ backgroundColor: '#F8FAFC' }}>
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="flex justify-center mb-6">
            <img 
              src={obieLogo} 
              alt="Obiemoney Logo" 
              className="h-10 w-auto"
            />
          </div>
          
          <span className="px-4 py-2 rounded-full text-sm font-medium mb-6 inline-block" style={{ 
            backgroundColor: '#A37AF9' + '20', 
            color: '#A37AF9' 
          }}>
            Stage 1: Optimise • Budget
          </span>

          <h1 className="text-4xl font-bold mb-4" style={{ color: '#A37AF9' }}>
            Current Savings & Investment
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Let's understand your current financial position. Tell us about any regular savings or investments you're already making.
          </p>
        </motion.div>

        {/* Savings Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <div className="bg-white rounded-2xl p-8 shadow-sm">
            <div className="flex items-center gap-4 mb-6">
              <div className="flex items-center justify-center w-16 h-16 bg-green-100 rounded-2xl">
                <PiggyBank size={32} className="text-green-600" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-gray-800">Savings</h2>
                <p className="text-gray-600">How much do you currently save regularly?</p>
              </div>
            </div>

            {!savingsAdded ? (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Amount ($)
                    </label>
                    <Input
                      type="number"
                      placeholder="0"
                      value={savings.amount}
                      onChange={(e) => setSavings(prev => ({ ...prev, amount: e.target.value }))}
                      className="border-2 focus:border-green-500 h-12"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      How Often?
                    </label>
                    <Select 
                      value={savings.frequency} 
                      onValueChange={(value: 'weekly' | 'monthly' | 'yearly') => 
                        setSavings(prev => ({ ...prev, frequency: value }))
                      }
                    >
                      <SelectTrigger className="border-2 focus:border-green-500 h-12">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex items-end">
                    <Button
                      onClick={addSavings}
                      disabled={!savings.amount}
                      className="w-full h-12 bg-green-600 hover:bg-green-700"
                    >
                      <Plus size={16} className="mr-2" />
                      Add Savings
                    </Button>
                  </div>
                </div>
                <div className="flex justify-center">
                  <Button
                    variant="outline"
                    onClick={() => setSavingsAdded(true)}
                    className="text-green-600 border-green-300 hover:bg-green-50"
                  >
                    Skip Savings for now
                  </Button>
                </div>
              </div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="flex items-center justify-between p-6 bg-green-50 rounded-xl border-2 border-green-200"
              >
                <div className="flex items-center gap-3">
                  <CheckCircle size={24} className="text-green-600" />
                  <div>
                    <p className="font-semibold text-gray-800">
                      {savings.amount ? `$${parseFloat(savings.amount).toLocaleString()}` : 'No regular savings'} 
                    </p>
                    <p className="text-sm text-gray-600">
                      {savings.amount ? `Saved ${savings.frequency}` : 'Skipped for now'}
                    </p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSavingsAdded(false)}
                  className="text-green-600 hover:text-green-700"
                >
                  Edit
                </Button>
              </motion.div>
            )}
          </div>
        </motion.div>

        {/* Investment Section */}
        {savingsAdded && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mb-8"
          >
            <div className="bg-white rounded-2xl p-8 shadow-sm">
              <div className="flex items-center gap-4 mb-6">
                <div className="flex items-center justify-center w-16 h-16 bg-blue-100 rounded-2xl">
                  <TrendingUp size={32} className="text-blue-600" />
                </div>
                <div>
                  <h2 className="text-2xl font-bold text-gray-800">Current Investment</h2>
                  <p className="text-gray-600">How much do you currently invest regularly?</p>
                </div>
              </div>

              {!investmentAdded ? (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Amount ($)
                      </label>
                      <Input
                        type="number"
                        placeholder="0"
                        value={investment.amount}
                        onChange={(e) => setInvestment(prev => ({ ...prev, amount: e.target.value }))}
                        className="border-2 focus:border-blue-500 h-12"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        How Often?
                      </label>
                      <Select 
                        value={investment.frequency} 
                        onValueChange={(value: 'weekly' | 'monthly' | 'yearly') => 
                          setInvestment(prev => ({ ...prev, frequency: value }))
                        }
                      >
                        <SelectTrigger className="border-2 focus:border-blue-500 h-12">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="monthly">Monthly</SelectItem>
                          <SelectItem value="yearly">Yearly</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-end">
                      <Button
                        onClick={addInvestment}
                        disabled={!investment.amount}
                        className="w-full h-12 bg-blue-600 hover:bg-blue-700"
                      >
                        <Plus size={16} className="mr-2" />
                        Add Investment
                      </Button>
                    </div>
                  </div>
                  <div className="flex justify-center">
                    <Button
                      variant="outline"
                      onClick={() => setInvestmentAdded(true)}
                      className="text-blue-600 border-blue-300 hover:bg-blue-50"
                    >
                      Skip Investment for now
                    </Button>
                  </div>
                </div>
              ) : (
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="flex items-center justify-between p-6 bg-blue-50 rounded-xl border-2 border-blue-200"
                >
                  <div className="flex items-center gap-3">
                    <CheckCircle size={24} className="text-blue-600" />
                    <div>
                      <p className="font-semibold text-gray-800">
                        {investment.amount ? `$${parseFloat(investment.amount).toLocaleString()}` : 'No regular investment'}
                      </p>
                      <p className="text-sm text-gray-600">
                        {investment.amount ? `Invested ${investment.frequency}` : 'Skipped for now'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setInvestmentAdded(false)}
                    className="text-blue-600 hover:text-blue-700"
                  >
                    Edit
                  </Button>
                </motion.div>
              )}
            </div>
          </motion.div>
        )}

        {/* Navigation Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="flex justify-between items-center"
        >
          <Button
            variant="outline"
            onClick={onBack}
            className="flex items-center gap-2 px-8 py-4 border-2 border-gray-300 text-gray-700 hover:bg-gray-50 rounded-2xl text-lg font-medium"
          >
            <ChevronLeft size={20} />
            Back to Expenses
          </Button>
          
          <Button
            onClick={handleComplete}
            disabled={!canProceed}
            className={`px-12 py-4 text-lg font-semibold rounded-2xl transition-all duration-300 ${
              canProceed 
                ? 'bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600 shadow-lg hover:shadow-xl' 
                : 'bg-gray-300 text-gray-500'
            }`}
          >
            {canProceed ? (
              <>
                Continue to Dashboard
                <ChevronRight size={20} className="ml-2" />
              </>
            ) : (
              'Complete both sections to continue'
            )}
          </Button>
        </motion.div>
      </div>
    </div>
  );
}