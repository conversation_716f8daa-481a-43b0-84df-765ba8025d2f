import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { 
  Shield, 
  ArrowLeft, 
  ArrowRight, 
  Target, 
  AlertCircle, 
  CheckCircle, 
  Calendar, 
  DollarSign,
  PiggyBank,
  Plus,
  Minus,
  Info,
  TrendingUp,
  Clock,
  CreditCard
} from "lucide-react";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Progress } from "./ui/progress";
import { Alert, AlertDescription } from "./ui/alert";
import { Separator } from "./ui/separator";
import obieLogo from 'figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png';

interface FinancialData {
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlySavings: number;
  monthlyInvestment: number;
  availableFromIncome: number;
  availableCashSavings: number;
}

interface SafetyNetData {
  emergencyFund: number;
  bigExpenses: number;
  threeMonthBuffer: number;
  totalTarget: number;
  currentAmount: number;
  monthlyContribution: number;
  isComplete: boolean;
  lastContributionDate?: string;
}

interface SafetyNetProps {
  financialData: FinancialData;
  onComplete: (data: SafetyNetData) => void;
  onBack: () => void;
}

interface SafetyNetGoals {
  emergencyFund: string;
  bigExpenses: string;
  threeMonthBuffer: string;
}

interface Allocation {
  fromCashSavings: number;
  monthlyContribution: number;
}

export function SafetyNet({ financialData, onComplete, onBack }: SafetyNetProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [goals, setGoals] = useState<SafetyNetGoals>({
    emergencyFund: '',
    bigExpenses: '',
    threeMonthBuffer: (financialData.monthlyExpenses * 3).toString()
  });
  const [allocation, setAllocation] = useState<Allocation>({
    fromCashSavings: 0,
    monthlyContribution: 0
  });

  const totalGoal = parseFloat(goals.emergencyFund || '0') + 
                   parseFloat(goals.bigExpenses || '0') + 
                   parseFloat(goals.threeMonthBuffer || '0');

  const remainingAfterCash = Math.max(0, totalGoal - allocation.fromCashSavings);
  const estimatedMonthsToComplete = allocation.monthlyContribution > 0 
    ? Math.ceil(remainingAfterCash / allocation.monthlyContribution)
    : Infinity;

  const formatAmount = (amount: number) => `$${amount.toLocaleString()}`;

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      onBack();
    }
  };

  const handleComplete = () => {
    const safetyNetData: SafetyNetData = {
      emergencyFund: parseFloat(goals.emergencyFund || '0'),
      bigExpenses: parseFloat(goals.bigExpenses || '0'),
      threeMonthBuffer: parseFloat(goals.threeMonthBuffer || '0'),
      totalTarget: totalGoal,
      currentAmount: allocation.fromCashSavings,
      monthlyContribution: allocation.monthlyContribution,
      isComplete: allocation.fromCashSavings >= totalGoal,
      lastContributionDate: new Date().toISOString()
    };
    onComplete(safetyNetData);
  };

  // Updated validation logic for 3 steps
  const canProceedFromStep1 = goals.emergencyFund && goals.bigExpenses && goals.threeMonthBuffer;
  const canProceedFromStep2 = allocation.fromCashSavings >= 0 && 
                              allocation.fromCashSavings <= Math.min(totalGoal, financialData.availableCashSavings);
  const canProceedFromStep3 = allocation.monthlyContribution >= 0 && 
                              allocation.monthlyContribution <= financialData.availableFromIncome;

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 via-red-50 to-pink-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center gap-4">
            <img src={obieLogo} alt="Obiemoney Logo" className="h-10 w-auto" />
            <div>
              <h1 className="text-3xl font-bold text-orange-600">Create Your Safety Net</h1>
              <p className="text-gray-600">Step 2: Protect your financial future</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 bg-white/60 rounded-full px-4 py-2">
              <span className="text-sm font-medium text-gray-600">Step {currentStep} of 3</span>
            </div>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <div className="mb-8">
          <Progress value={(currentStep / 3) * 100} className="h-2 bg-white/60" />
          <div className="flex justify-between mt-2 text-sm text-gray-600">
            <span className={currentStep >= 1 ? 'text-orange-600 font-medium' : ''}>Set Goals</span>
            <span className={currentStep >= 2 ? 'text-orange-600 font-medium' : ''}>Allocate</span>
            <span className={currentStep >= 3 ? 'text-orange-600 font-medium' : ''}>Summary</span>
          </div>
        </div>

        <AnimatePresence mode="wait">
          {/* Step 1: Learn + Set Goals Combined - Three Column Layout */}
          {currentStep === 1 && (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-8"
            >
              {/* Header Section */}
              <Card className="p-8 bg-white/80 backdrop-blur-sm text-center">
                {/* <div className="flex p-3 bg-orange-100 rounded-full mb-4">
                  <Shield className="h-6 w-6 text-orange-600" />
                </div> */}
                <h2 className="text-2xl font-bold text-gray-800 mb-4">
                  Building Your Financial Safety Net
                </h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  A safety net protects you from unexpected financial storms. Let's build yours with three essential components.
                </p>
              </Card>

              {/* Three Column Layout */}
              <div className="grid md:grid-cols-3 gap-6">
                {/* Emergency Fund Card */}
                <Card className="p-6 bg-gradient-to-br from-red-50 to-orange-50 border-red-200 bg-white/80 backdrop-blur-sm">
                  <div className="flex flex-col items-center text-center">
                    <div className="inline-flex p-3 bg-red-100 rounded-full mb-3">
                      <AlertCircle className="h-6 w-6 text-red-600" />
                    </div>
                    <h3 className="text-lg font-bold text-gray-800 mb-2">Emergency Fund</h3>
                    <p className="text-sm text-gray-700">
                      For sudden unexpected expenses like medical bills, car repairs, or urgent home fixes.
                    </p>
                  </div>
                  
                  {/* Input Section */}
                  <div className="bg-white/70 rounded-xl p-4">
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-lg">$</span>
                      <Input
                        type="number"
                        placeholder="3,000"
                        value={goals.emergencyFund}
                        onChange={(e) => setGoals({...goals, emergencyFund: e.target.value})}
                        className="text-lg font-medium pl-8 bg-white border-red-300 focus:border-red-500"
                      />
                    </div>
                  </div>
                </Card>

                {/* Big Expenses Card */}
                <Card className="p-6 bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200 bg-white/80 backdrop-blur-sm">
                  <div className="flex flex-col items-center text-center">
                    <div className="inline-flex p-3 bg-blue-100 rounded-full mb-3">
                      <Target className="h-6 w-6 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-bold text-gray-800 mb-2">Big Expenses</h3>
                    <p className="text-sm text-gray-700">
                      For planned major purchases like vacations, appliances, education costs, or home improvements.
                    </p>
                  </div>
                  
                  {/* Input Section */}
                  <div className="bg-white/70 rounded-xl p-4">
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-lg">$</span>
                      <Input
                        type="number"
                        placeholder="5,000"
                        value={goals.bigExpenses}
                        onChange={(e) => setGoals({...goals, bigExpenses: e.target.value})}
                        className="text-lg font-medium pl-8 bg-white border-blue-300 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </Card>

                {/* 3-Month Buffer Card */}
                <Card className="p-6 bg-gradient-to-br from-green-50 to-emerald-50 border-green-200 bg-white/80 backdrop-blur-sm">
                  <div className="flex flex-col items-center text-center">
                    <div className="inline-flex p-3 bg-green-100 rounded-full mb-3">
                      <Shield className="h-6 w-6 text-green-600" />
                    </div>
                    <h3 className="text-lg font-bold text-gray-800 mb-2">3-Month Living Buffer</h3>
                    <p className="text-sm text-gray-700">
                      Covers 3 months of living expenses for job loss or income reduction.
                    </p>
                  </div>
                  
                  {/* Input Section */}
                  <div className="bg-white/70 rounded-xl p-4">
                    <div className="relative">
                      <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-lg">$</span>
                      <Input
                        type="number"
                        value={goals.threeMonthBuffer}
                        onChange={(e) => setGoals({...goals, threeMonthBuffer: e.target.value})}
                        className="text-lg font-medium pl-8 bg-white border-green-300 focus:border-green-500"
                      />
                    </div>
                  </div>
                </Card>
              </div>

              {/* Total Summary */}
              {totalGoal > 0 && (
                <Card className="p-8 bg-gradient-to-r from-orange-50 to-yellow-50 border-orange-200 bg-white/80 backdrop-blur-sm">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="inline-flex p-3 bg-orange-100 rounded-full">
                        <PiggyBank className="h-8 w-8 text-orange-600" />
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-800">Total Safety Net Goal</h3>
                        <p className="text-gray-600">Your complete financial protection plan</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-4xl font-bold text-orange-600">{formatAmount(totalGoal)}</p>
                      <p className="text-sm text-gray-600">Target amount</p>
                    </div>
                  </div>
                </Card>
              )}

              {/* Pro Tip */}
              <Alert className="border-orange-200 bg-orange-50">
                <Info className="h-4 w-4 text-orange-600" />
                <AlertDescription className="text-orange-800">
                  <strong>Pro Tip:</strong> Start small and build gradually. Even $500 in your emergency fund can make a huge difference! 
                  You can always adjust these amounts later as your situation changes.
                </AlertDescription>
              </Alert>
            </motion.div>
          )}

          {/* Step 2: Allocation */}
          {currentStep === 2 && (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-green-100 rounded-full mb-4">
                    <DollarSign className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Fund Your Safety Net</h2>
                  <p className="text-gray-600">
                    Let's allocate money from your current cash and set up monthly contributions.
                  </p>
                </div>

                <div className="space-y-8">
                  {/* Current Cash Allocation */}
                  <Card className="p-6 bg-green-50 border-green-200">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-3">
                        <CreditCard className="h-6 w-6 text-green-600" />
                        <div>
                          <h3 className="font-bold text-gray-800">From Current Cash Savings</h3>
                          <p className="text-sm text-gray-600">Available: {formatAmount(financialData.availableCashSavings)}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold text-green-600">{formatAmount(allocation.fromCashSavings)}</p>
                        <p className="text-sm text-gray-600">Allocated now</p>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <Input
                        type="number"
                        placeholder="0"
                        value={allocation.fromCashSavings}
                        onChange={(e) => setAllocation({...allocation, fromCashSavings: parseFloat(e.target.value) || 0})}
                        max={Math.min(totalGoal, financialData.availableCashSavings)}
                        className="text-lg font-medium"
                      />
                      
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setAllocation({...allocation, fromCashSavings: Math.min(totalGoal * 0.25, financialData.availableCashSavings)})}
                        >
                          25% of goal
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setAllocation({...allocation, fromCashSavings: Math.min(totalGoal * 0.5, financialData.availableCashSavings)})}
                        >
                          50% of goal
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setAllocation({...allocation, fromCashSavings: Math.min(totalGoal, financialData.availableCashSavings)})}
                        >
                          All available
                        </Button>
                      </div>
                    </div>
                  </Card>

                  {/* Monthly Contribution */}
                  <Card className="p-6 bg-blue-50 border-blue-200">
                    <div className="flex items-center justify-between mb-6">
                      <div className="flex items-center gap-3">
                        <Calendar className="h-6 w-6 text-blue-600" />
                        <div>
                          <h3 className="font-bold text-gray-800">Monthly Contribution</h3>
                          <p className="text-sm text-gray-600">Available from income: {formatAmount(financialData.availableFromIncome)}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-2xl font-bold text-blue-600">{formatAmount(allocation.monthlyContribution)}</p>
                        <p className="text-sm text-gray-600">Per month</p>
                      </div>
                    </div>
                    
                    <div className="space-y-4">
                      <Input
                        type="number"
                        placeholder="0"
                        value={allocation.monthlyContribution}
                        onChange={(e) => setAllocation({...allocation, monthlyContribution: parseFloat(e.target.value) || 0})}
                        max={financialData.availableFromIncome}
                        className="text-lg font-medium"
                      />
                      
                      <div className="flex gap-2">
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setAllocation({...allocation, monthlyContribution: Math.round(financialData.availableFromIncome * 0.25)})}
                        >
                          25% of available
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setAllocation({...allocation, monthlyContribution: Math.round(financialData.availableFromIncome * 0.5)})}
                        >
                          50% of available
                        </Button>
                        <Button 
                          variant="outline" 
                          size="sm"
                          onClick={() => setAllocation({...allocation, monthlyContribution: Math.round(financialData.availableFromIncome)})}
                        >
                          All available
                        </Button>
                      </div>
                    </div>
                  </Card>

                  {/* Remaining Amount */}
                  {remainingAfterCash > 0 && (
                    <Alert className="border-orange-200 bg-orange-50">
                      <Clock className="h-4 w-4 text-orange-600" />
                      <AlertDescription className="text-orange-800">
                        <strong>Remaining to fund:</strong> {formatAmount(remainingAfterCash)}
                        {allocation.monthlyContribution > 0 && (
                          <span> • Estimated completion: {estimatedMonthsToComplete} months</span>
                        )}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              </Card>
            </motion.div>
          )}

          {/* Step 3: Summary */}
          {currentStep === 3 && (
            <motion.div
              key="step3"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-green-100 rounded-full mb-4">
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Safety Net Summary</h2>
                  <p className="text-gray-600">
                    Review your safety net plan. You can always adjust these amounts later.
                  </p>
                </div>

                <div className="space-y-6">
                  {/* Goal Breakdown */}
                  <Card className="p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <Target className="h-5 w-5 text-blue-600" />
                      Your Safety Net Goals
                    </h3>
                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <p className="text-sm text-gray-600">Emergency Fund</p>
                        <p className="text-xl font-bold text-red-600">{formatAmount(parseFloat(goals.emergencyFund || '0'))}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600">Big Expenses</p>
                        <p className="text-xl font-bold text-blue-600">{formatAmount(parseFloat(goals.bigExpenses || '0'))}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-600">3-Month Buffer</p>
                        <p className="text-xl font-bold text-green-600">{formatAmount(parseFloat(goals.threeMonthBuffer || '0'))}</p>
                      </div>
                    </div>
                    <Separator className="my-4" />
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Total Target</p>
                      <p className="text-3xl font-bold text-purple-600">{formatAmount(totalGoal)}</p>
                    </div>
                  </Card>

                  {/* Funding Plan */}
                  <Card className="p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <DollarSign className="h-5 w-5 text-green-600" />
                      Your Funding Plan
                    </h3>
                    <div className="grid md:grid-cols-2 gap-6">
                      <div>
                        <p className="text-sm text-gray-600 mb-2">Starting Today</p>
                        <p className="text-2xl font-bold text-green-600">{formatAmount(allocation.fromCashSavings)}</p>
                        <p className="text-xs text-gray-500">From current cash savings</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 mb-2">Monthly Contribution</p>
                        <p className="text-2xl font-bold text-blue-600">{formatAmount(allocation.monthlyContribution)}</p>
                        <p className="text-xs text-gray-500">From monthly income</p>
                      </div>
                    </div>
                  </Card>

                  {/* Progress and Timeline */}
                  <Card className="p-6 bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-purple-600" />
                      Progress & Timeline
                    </h3>
                    <div className="space-y-4">
                      <div>
                        <div className="flex justify-between mb-2">
                          <span className="text-sm text-gray-600">Initial Progress</span>
                          <span className="text-sm font-medium">{Math.round((allocation.fromCashSavings / totalGoal) * 100)}%</span>
                        </div>
                        <Progress value={(allocation.fromCashSavings / totalGoal) * 100} className="h-2" />
                      </div>
                      
                      {remainingAfterCash > 0 && allocation.monthlyContribution > 0 && (
                        <div className="bg-white/60 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <p className="font-medium text-gray-800">Estimated Completion</p>
                              <p className="text-sm text-gray-600">At current monthly rate</p>
                            </div>
                            <div className="text-right">
                              <p className="text-xl font-bold text-purple-600">{estimatedMonthsToComplete} months</p>
                              <p className="text-xs text-gray-500">
                                {new Date(Date.now() + estimatedMonthsToComplete * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { 
                                  month: 'short', 
                                  year: 'numeric' 
                                })}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                      
                      {allocation.fromCashSavings >= totalGoal && (
                        <Alert className="border-green-200 bg-green-50">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <AlertDescription className="text-green-800">
                            <strong>Congratulations!</strong> Your safety net is fully funded! 🎉
                          </AlertDescription>
                        </Alert>
                      )}
                    </div>
                  </Card>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8">
          <Button
            variant="outline"
            onClick={handleBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {currentStep === 1 ? 'Back to Dashboard' : 'Back'}
          </Button>

          <div className="flex items-center gap-4">
            {currentStep < 3 ? (
              <Button
                onClick={handleNext}
                disabled={
                  (currentStep === 1 && !canProceedFromStep1) ||
                  (currentStep === 2 && !canProceedFromStep2)
                }
                className="flex items-center gap-2 bg-orange-600 hover:bg-orange-700"
              >
                Continue
                <ArrowRight className="h-4 w-4" />
              </Button>
            ) : (
              <Button
                onClick={handleComplete}
                className="flex items-center gap-2 bg-green-600 hover:bg-green-700"
              >
                Complete Safety Net
                <CheckCircle className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}