import { useState } from "react";
import { Eye, EyeOff, Calendar, CheckCircle, AlertCircle } from "lucide-react";
import { motion } from "motion/react";
import svgPaths from "../imports/svg-vd034xtudd";
import imgImage1 from "figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png";

// Background decoration components (keeping original SVG groups)
function Group() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 138 120"
      >
        <g id="Group">
          <path
            d={svgPaths.p32e40200}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group1() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 17 43"
      >
        <g id="Group">
          <path
            d={svgPaths.p6277b80}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group2() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 10 52"
      >
        <g id="Group">
          <path
            d={svgPaths.p1ff7cd00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group3() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 83 49"
      >
        <g id="Group">
          <path
            d={svgPaths.p32bff200}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group4() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 126 98"
      >
        <g id="Group">
          <path
            d={svgPaths.p2ec51100}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

// Additional Groups truncated for brevity but would include all from original

function Component2() {
  return (
    <div className="absolute bg-[#ffffff] h-[900px] left-0 overflow-clip top-px w-[1440px]" data-name="Component 2">
      {/* Background decorative elements */}
      <div className="absolute contents inset-[27.02%_39.1%_54.19%_48.53%]" data-name="Group">
        <div className="absolute flex inset-[27.14%_39.1%_54.19%_48.69%] items-center justify-center">
          <div className="flex-none h-[119.886px] rotate-[26.181deg] w-[137.04px]">
            <Group />
          </div>
        </div>
        <div className="absolute flex inset-[30.3%_47.72%_64.65%_49.95%] items-center justify-center">
          <div className="flex-none h-[42.659px] rotate-[26.181deg] w-[16.355px]">
            <Group1 />
          </div>
        </div>
        <div className="absolute flex inset-[31.79%_48.66%_62.64%_49.18%] items-center justify-center">
          <div className="flex-none h-[51.147px] rotate-[26.181deg] w-[9.561px]">
            <Group2 />
          </div>
        </div>
        <div className="absolute flex inset-[27.83%_42.2%_63.22%_51.13%] items-center justify-center">
          <div className="flex-none h-[48.954px] rotate-[26.181deg] w-[82.971px]">
            <Group3 />
          </div>
        </div>
        <div className="absolute flex inset-[28.18%_39.73%_55.94%_49.47%] items-center justify-center">
          <div className="flex-none h-[97.769px] rotate-[26.181deg] w-[125.179px]">
            <Group4 />
          </div>
        </div>
      </div>
    </div>
  );
}

function GoogleIcon() {
  return (
    <div className="relative shrink-0 size-6" data-name="google">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 24 24">
        <g id="google">
          <circle cx="12" cy="12" fill="var(--fill-0, white)" id="Ellipse" r="12" />
          <g id="Logo">
            <path
              clipRule="evenodd"
              d={svgPaths.p191cc400}
              fill="var(--fill-0, #4285F4)"
              fillRule="evenodd"
              id="Vector"
            />
            <path
              clipRule="evenodd"
              d={svgPaths.p3ac5f1f0}
              fill="var(--fill-0, #34A853)"
              fillRule="evenodd"
              id="Vector_2"
            />
            <path
              clipRule="evenodd"
              d={svgPaths.p387fcf40}
              fill="var(--fill-0, #FBBC05)"
              fillRule="evenodd"
              id="Vector_3"
            />
            <path
              clipRule="evenodd"
              d={svgPaths.p34b65e00}
              fill="var(--fill-0, #EA4335)"
              fillRule="evenodd"
              id="Vector_4"
            />
          </g>
        </g>
      </svg>
    </div>
  );
}

function UbrandLogo() {
  return (
    <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid leading-[0] place-items-start relative shrink-0" data-name="ubrand-logo">
      <div
        className="[grid-area:1_/_1] bg-center bg-cover bg-no-repeat h-[52.946px] ml-0 mt-0 w-56"
        data-name="image 1"
        style={{ backgroundImage: `url('${imgImage1}')` }}
      />
    </div>
  );
}

function CustomInput({ 
  label, 
  type = "text", 
  value, 
  onChange, 
  placeholder, 
  icon, 
  error, 
  success, 
  hint 
}: {
  label: string;
  type?: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  icon?: React.ReactNode;
  error?: string;
  success?: string;
  hint?: string;
}) {
  const [showPassword, setShowPassword] = useState(false);
  const [isFocused, setIsFocused] = useState(false);

  const inputType = type === "password" && showPassword ? "text" : type;

  return (
    <div className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-full">
      <div className="relative rounded shrink-0 w-full">
        <div
          className={`absolute border border-solid inset-0 pointer-events-none rounded transition-colors duration-200 ${
            error 
              ? 'border-red-400' 
              : success 
              ? 'border-green-400' 
              : isFocused 
              ? 'border-[#6130df]' 
              : 'border-[#d0d0d2]'
          }`}
        />
        <div className="flex flex-col justify-center relative size-full">
          <div className="box-border content-stretch flex flex-col items-start justify-center px-4 py-0 relative size-full">
            <div className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-12 overflow-clip px-0 py-0 relative shrink-0 w-full">
              <input
                type={inputType}
                value={value}
                onChange={(e) => onChange(e.target.value)}
                onFocus={() => setIsFocused(true)}
                onBlur={() => setIsFocused(false)}
                placeholder={placeholder || label}
                className="basis-0 font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal grow leading-[20px] min-h-px min-w-px relative shrink-0 text-[14px] text-left bg-transparent border-none outline-none placeholder:text-neutral-500 text-neutral-900"
              />
              <div className="flex items-center gap-2">
                {type === "password" && (
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="p-1 hover:bg-gray-100 rounded transition-colors duration-200"
                  >
                    {showPassword ? <EyeOff size={20} className="text-gray-500" /> : <Eye size={20} className="text-gray-500" />}
                  </button>
                )}
                {icon && <div className="text-gray-500">{icon}</div>}
                {success && <CheckCircle size={20} className="text-green-500" />}
                {error && <AlertCircle size={20} className="text-red-500" />}
              </div>
            </div>
          </div>
        </div>
      </div>
      {(error || success || hint) && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mt-1 text-sm"
        >
          {error && <p className="text-red-500 flex items-center gap-1"><AlertCircle size={16} />{error}</p>}
          {success && <p className="text-green-500 flex items-center gap-1"><CheckCircle size={16} />{success}</p>}
          {hint && !error && !success && <p className="text-gray-500">{hint}</p>}
        </motion.div>
      )}
    </div>
  );
}

export function SignUp({ onComplete }: { onComplete: () => void }) {
  const [formData, setFormData] = useState({
    name: "",
    dateOfBirth: "",
    email: "",
    password: "",
    confirmPassword: ""
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  const validateField = (field: string, value: string) => {
    const newErrors = { ...errors };

    switch (field) {
      case 'name':
        if (!value.trim()) {
          newErrors.name = "We'd love to know what to call you!";
        } else if (value.trim().length < 2) {
          newErrors.name = "A bit longer please - we want to get your name just right!";
        } else {
          delete newErrors.name;
        }
        break;
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!value) {
          newErrors.email = "Where should we send your wealth-building tips?";
        } else if (!emailRegex.test(value)) {
          newErrors.email = "Hmm, that doesn't look like an email address";
        } else {
          delete newErrors.email;
        }
        break;
      case 'password':
        if (!value) {
          newErrors.password = "You'll need a password to protect your financial journey!";
        } else if (value.length < 8) {
          newErrors.password = "Make it at least 8 characters - your future self will thank you!";
        } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(value)) {
          newErrors.password = "Mix it up! Try uppercase, lowercase, and numbers";
        } else {
          delete newErrors.password;
        }
        break;
      case 'confirmPassword':
        if (!value) {
          newErrors.confirmPassword = "Just making sure you've got it! 🔄";
        } else if (value !== formData.password) {
          newErrors.confirmPassword = "Passwords don't match - let's try that again!";
        } else {
          delete newErrors.confirmPassword;
        }
        break;
    }

    setErrors(newErrors);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    // Move to onboarding flow
    onComplete();
  };

  const isFormValid = Object.keys(errors).length === 0 && 
    formData.name && formData.email && formData.password && formData.confirmPassword;

  return (
    <div className="relative size-full min-h-screen bg-gradient-to-br from-blue-50 via-purple-50 to-orange-50" data-name="Sign up">
      <Component2 />
      
      {/* Left side content */}
      <div className="absolute box-border content-stretch flex flex-col gap-[110px] items-center justify-start left-[196px] p-0 translate-y-[-50%]" style={{ top: "calc(50% + 0.473px)" }}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <UbrandLogo />
        </motion.div>
        
        <div className="box-border content-stretch flex flex-col gap-[74px] items-center justify-start overflow-clip p-[20px] relative shrink-0">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="box-border content-stretch flex flex-col gap-[18px] items-start justify-start leading-[0] p-0 relative shrink-0 text-center w-[450px]"
          >
            <div className="flex flex-col font-['Schibsted_Grotesk:ExtraBold',_sans-serif] font-extrabold justify-end relative shrink-0 text-[#6130df] text-[62px] w-full">
              <p className="block leading-[68px]">
                Build yourself to wealth in 9 simple steps
              </p>
            </div>
            <div className="flex flex-col font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal justify-center relative shrink-0 text-[#0f0935] text-[20px] w-full">
              <p className="block leading-[normal]">
                Learn to budget like Barefoot and build wealth like a Buffet.
              </p>
            </div>
          </motion.div>
          
          {/* Slider dots */}
          <div className="box-border content-stretch flex flex-row gap-1.5 items-center justify-start p-0 relative shrink-0">
            <div className="bg-[#ee672d] h-1.5 rounded-[31px] shrink-0 w-4" />
            <div className="bg-[rgba(18,18,18,0.46)] rounded-[55px] shrink-0 size-1.5" />
            <div className="bg-[rgba(18,18,18,0.46)] rounded-[55px] shrink-0 size-1.5" />
          </div>
        </div>
      </div>

      {/* Right side form */}
      <motion.div
        initial={{ opacity: 0, x: 20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="absolute bg-[#ffffff] box-border content-stretch flex flex-row gap-2.5 items-center justify-center p-[50px] rounded-2xl shadow-2xl top-1/2 translate-x-[-50%] translate-y-[-50%] w-[450px]"
        style={{ left: "calc(50% + 386px)" }}
      >
        <div className="box-border content-stretch flex flex-col gap-[25px] items-start justify-start p-0 relative shrink-0 w-[350px]">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.6 }}
            className="box-border content-stretch flex flex-col gap-2 items-start justify-start p-0 relative shrink-0 w-full"
          >
            <div className="flex flex-col font-['Schibsted_Grotesk:Bold',_sans-serif] font-bold justify-center leading-[0] relative shrink-0 text-[#100937] text-[30px] text-center w-full">
              <p className="block leading-[normal]">Your wealth journey starts here!</p>
            </div>
            <div className="text-center w-full text-sm text-gray-600 font-['Schibsted_Grotesk:Regular',_sans-serif]">
              <p>Join thousands who've already taken control of their financial future</p>
            </div>
          </motion.div>

          {/* Google Sign Up */}
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            className="bg-[#f2eefc] box-border content-stretch flex flex-row gap-2 items-center justify-center p-4 relative rounded-[50px] shrink-0 w-full hover:bg-[#e8e0fc] transition-colors duration-200"
          >
            <GoogleIcon />
            <span className="font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal text-[#424242] text-[16px]">
              Quick start with Google
            </span>
          </motion.button>

          {/* Divider */}
          <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-center p-0 relative shrink-0 w-full">
            <div className="basis-0 grow h-0.5 bg-[#E0E0E0] relative shrink-0" />
            <div className="bg-[#f4f4f4] box-border content-stretch flex flex-row gap-2.5 items-center justify-center p-[4px] relative rounded shrink-0">
              <div className="font-['Schibsted_Grotesk:Medium',_sans-serif] font-medium text-[#616161] text-[12.8px]">
                OR
              </div>
            </div>
            <div className="basis-0 grow h-0.5 bg-[#E0E0E0] relative shrink-0" />
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="box-border content-stretch flex flex-col gap-[18px] items-start justify-start p-0 relative shrink-0 w-full">
            {/* Name and Date Row */}
            <div className="box-border content-stretch flex flex-row gap-[18px] items-start justify-start p-0 relative shrink-0 w-full">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.7 }}
                className="basis-0 grow"
              >
                <CustomInput
                  label="What should we call you?"
                  value={formData.name}
                  onChange={(value) => {
                    setFormData({ ...formData, name: value });
                    validateField('name', value);
                  }}
                  placeholder="Your awesome name"
                  error={errors.name}
                  success={formData.name && !errors.name ? "Nice to meet you!" : undefined}
                />
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.8 }}
                className="basis-0 grow"
              >
                <CustomInput
                  label="When's your birthday?"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(value) => setFormData({ ...formData, dateOfBirth: value })}
                  icon={<Calendar size={20} />}
                  hint="We'll help you plan for every life stage"
                />
              </motion.div>
            </div>

            {/* Email */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9 }}
            >
              <CustomInput
                label="Your email address"
                type="email"
                value={formData.email}
                onChange={(value) => {
                  setFormData({ ...formData, email: value });
                  validateField('email', value);
                }}
                placeholder="<EMAIL>"
                error={errors.email}
                success={formData.email && !errors.email ? "Perfect! We'll send you wealth-building tips" : undefined}
              />
            </motion.div>

            {/* Password */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.0 }}
            >
              <CustomInput
                label="Create a strong password"
                type="password"
                value={formData.password}
                onChange={(value) => {
                  setFormData({ ...formData, password: value });
                  validateField('password', value);
                }}
                placeholder="Make it memorable & secure"
                error={errors.password}
                success={formData.password && !errors.password ? "That's a fortress-level password!" : undefined}
                hint="At least 8 characters with uppercase, lowercase, and numbers"
              />
            </motion.div>

            {/* Confirm Password */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.1 }}
            >
              <CustomInput
                label="Confirm your password"
                type="password"
                value={formData.confirmPassword}
                onChange={(value) => {
                  setFormData({ ...formData, confirmPassword: value });
                  validateField('confirmPassword', value);
                }}
                placeholder="One more time for security"
                error={errors.confirmPassword}
                success={formData.confirmPassword && !errors.confirmPassword ? "Perfect match!" : undefined}
              />
            </motion.div>

            {/* Submit Button */}
            <motion.button
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2 }}
              whileHover={{ scale: isFormValid ? 1.02 : 1 }}
              whileTap={{ scale: isFormValid ? 0.98 : 1 }}
              type="submit"
              disabled={!isFormValid || isSubmitting}
              className={`h-12 relative rounded-lg shrink-0 w-full transition-all duration-200 ${
                isFormValid 
                  ? 'bg-[#6130df] hover:bg-[#5528c7] shadow-lg hover:shadow-xl' 
                  : 'bg-gray-300 cursor-not-allowed'
              }`}
            >
              <div className="flex flex-col items-center justify-center overflow-clip relative size-full">
                <div className="box-border content-stretch flex flex-col h-12 items-center justify-center px-[19px] py-2 relative w-full">
                  <div className="box-border content-stretch flex flex-row gap-2 items-center justify-center p-0 relative shrink-0">
                    {isSubmitting ? (
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                        <span className="font-['Schibsted_Grotesk:Medium',_sans-serif] font-medium text-[#ffffff] text-[14px]">
                          Creating Your Wealth Account...
                        </span>
                      </div>
                    ) : (
                      <span className="font-['Schibsted_Grotesk:Medium',_sans-serif] font-medium text-[#ffffff] text-[14px]">
                        Start My Wealth Journey!
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </motion.button>

            {/* Terms */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.3 }}
              className="font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal text-[#757575] text-[12px] text-center w-full"
            >
              <p className="leading-[18px]">
                <span>By joining Obiemoney, you're agreeing to our </span>
                <span className="[text-decoration-line:underline] [text-decoration-skip-ink:none] [text-decoration-style:solid] [text-underline-position:from-font] text-[#6130df] hover:text-[#5528c7] cursor-pointer">
                  Privacy Policy
                </span>
                <span> and </span>
                <span className="[text-decoration-line:underline] [text-decoration-skip-ink:none] [text-decoration-style:solid] [text-underline-position:from-font] text-[#6130df] hover:text-[#5528c7] cursor-pointer">
                  Terms of Service
                </span>
                <span>. Don't worry, no financial jargon!</span>
              </p>
            </motion.div>
          </form>

          {/* Login Link */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.4 }}
            className="box-border content-stretch flex flex-row gap-1 items-start justify-center leading-[0] p-0 relative shrink-0 text-[12.8px] text-center text-nowrap"
          >
            <div className="font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal relative shrink-0 text-[#616161]">
              <p className="block leading-[normal] text-nowrap whitespace-pre">
                Already building wealth with us?
              </p>
            </div>
            <div className="font-['Schibsted_Grotesk:Bold',_sans-serif] font-bold relative shrink-0 text-[#6130df] hover:text-[#5528c7] cursor-pointer transition-colors duration-200">
              <p className="[text-decoration-line:underline] [text-decoration-skip-ink:none] [text-decoration-style:solid] [text-underline-position:from-font] block leading-[normal] text-nowrap whitespace-pre">
                Log In
              </p>
            </div>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}