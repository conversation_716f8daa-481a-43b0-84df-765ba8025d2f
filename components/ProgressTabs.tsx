import svgPaths from "../imports/svg-ckccvkfnjv";
import { CheckCircle } from "lucide-react";

interface ProgressTabsProps {
  activeTab: "optimise" | "maximise" | "protect";
  completedTabs?: ("optimise" | "maximise" | "protect")[];
  onTabClick?: (tab: "optimise" | "maximise" | "protect") => void;
}

export function ProgressTabs({ 
  activeTab, 
  completedTabs = [], 
  onTabClick 
}: ProgressTabsProps) {
  
  const handleTabClick = (tab: "optimise" | "maximise" | "protect") => {
    // Only allow clicking on completed tabs or the current active tab
    if (completedTabs.includes(tab) && onTabClick) {
      onTabClick(tab);
    }
  };

  const getTabClasses = (tab: "optimise" | "maximise" | "protect") => {
    const isActive = activeTab === tab;
    const isCompleted = completedTabs.includes(tab);
    const isClickable = isCompleted && onTabClick;
    
    let baseClasses = "flex flex-col gap-2 items-center flex-1";
    if (isClickable) {
      baseClasses += " cursor-pointer hover:opacity-80 transition-opacity";
    }
    
    return baseClasses;
  };

  const getIconClasses = (tab: "optimise" | "maximise" | "protect") => {
    const isActive = activeTab === tab;
    const isCompleted = completedTabs.includes(tab);
    
    if (isCompleted && !isActive) {
      return "bg-gradient-to-br from-green-500 to-emerald-500 shadow-md";
    } else if (isActive) {
      return "bg-gradient-to-br from-orange-500 to-red-500 shadow-md";
    } else {
      return "bg-gray-200";
    }
  };

  const getTextClasses = (tab: "optimise" | "maximise" | "protect") => {
    const isActive = activeTab === tab;
    const isCompleted = completedTabs.includes(tab);
    
    if (isCompleted && !isActive) {
      return "text-green-600";
    } else if (isActive) {
      return "text-orange-600";
    } else {
      return "text-gray-500";
    }
  };

  const renderTabContent = (tab: "optimise" | "maximise" | "protect", label: string) => {
    const isActive = activeTab === tab;
    const isCompleted = completedTabs.includes(tab);
    
    return (
      <div 
        className={getTabClasses(tab)}
        onClick={() => handleTabClick(tab)}
      >
        <div className="flex items-center gap-2">
          <div className={`relative w-8 h-8 rounded-lg transition-all duration-300 ${getIconClasses(tab)}`}>
            {isCompleted && !isActive ? (
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <CheckCircle className="w-4 h-4 text-white" />
              </div>
            ) : (
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4 h-4">
                <svg className="w-full h-full" fill="none" viewBox="0 0 17 17">
                  <path 
                    d={svgPaths.p23992100} 
                    fill={isActive || isCompleted ? "white" : "#666666"} 
                    opacity={isActive || isCompleted ? "0.8" : "0.6"}
                  />
                  <path 
                    d={svgPaths.p1489a800} 
                    fill={isActive || isCompleted ? "white" : "#666666"} 
                  />
                </svg>
              </div>
            )}
          </div>
          <span className={`font-medium transition-colors duration-300 ${getTextClasses(tab)}`}>
            {label}
            {isCompleted && !isActive && (
              <span className="ml-1 text-xs">✓</span>
            )}
          </span>
        </div>
        {isActive && (
          <div className="w-full h-1 bg-gradient-to-r from-orange-500 to-red-500 rounded-full" />
        )}
        {isCompleted && !isActive && (
          <div className="w-full h-1 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full" />
        )}
      </div>
    );
  };

  return (
    <div className="flex items-center justify-between mb-6 w-full">
      {renderTabContent("optimise", "Optimise")}
      {renderTabContent("maximise", "Maximise")}
      {renderTabContent("protect", "Protect")}
    </div>
  );
}