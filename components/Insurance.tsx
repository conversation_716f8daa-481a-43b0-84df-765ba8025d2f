import { useState } from "react";
import { motion, AnimatePresence } from "motion/react";
import { 
  Shield, 
  ArrowLeft, 
  ArrowRight, 
  Target, 
  AlertCircle, 
  CheckCircle, 
  Calendar, 
  DollarSign,
  Plus,
  Info,
  Clock,
  Trash2,
  Phone,
  FileText,
  Upload,
  Bell,
  Heart,
  Home,
  Car,
  Plane,
  Briefcase,
  Users,
  Package,
  Sparkles
} from "lucide-react";
import { Button } from "./ui/button";
import { Card } from "./ui/card";
import { Input } from "./ui/input";
import { Progress } from "./ui/progress";
import { Alert, AlertDescription } from "./ui/alert";
import { Badge } from "./ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Switch } from "./ui/switch";
import { Textarea } from "./ui/textarea";
import { Separator } from "./ui/separator";
import { InsurancePolicy, InsuranceData, NewInsurancePolicy } from "../types/insurance";
import { insuranceTypes, popularInsuranceCompanies, insuranceEducationContent, documentTypes } from "../constants/insurance";
import { 
  calculateMonthlyPremium,
  calculateTotalPremiums,
  calculateTotalCoverage,
  getUpcomingRenewals,
  calculateRecommendedCoverage,
  analyzeCoverageGaps,
  formatAmount,
  formatDate,
  getDaysUntilRenewal,
  getInsuranceHealth
} from "../utils/insurance";
import obieLogo from 'figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png';

interface FinancialData {
  monthlyIncome: number;
  monthlyExpenses: number;
  monthlySavings: number;
  monthlyInvestment: number;
  availableFromIncome: number;
  availableCashSavings: number;
}

interface InsuranceProps {
  financialData: FinancialData;
  currentAge: number;
  totalAssets: number;
  dependents: number;
  onComplete: (data: InsuranceData) => void;
  onBack: () => void;
}

export function Insurance({ financialData, currentAge, totalAssets, dependents, onComplete, onBack }: InsuranceProps) {
  const [currentStep, setCurrentStep] = useState(1);
  const [policies, setPolicies] = useState<InsurancePolicy[]>([]);
  const [newPolicy, setNewPolicy] = useState<NewInsurancePolicy>({
    type: 'life',
    policyNumber: '',
    itemOrMemberInsured: '',
    insuranceCompany: '',
    amountInsured: '',
    insideSuper: false,
    premiumAmount: '',
    premiumFrequency: 'monthly',
    renewalDate: '',
    contactNumber: '',
    claimFormLink: '',
    notes: ''
  });
  const [showAddPolicy, setShowAddPolicy] = useState(false);

  // Calculate insurance data
  const totalCoverage = calculateTotalCoverage(policies);
  const totalPremiums = calculateTotalPremiums(policies);
  const upcomingRenewals = getUpcomingRenewals(policies, 60);
  const recommendedCoverage = calculateRecommendedCoverage(financialData.monthlyIncome, totalAssets, dependents, currentAge);
  const coverageGaps = analyzeCoverageGaps(policies, recommendedCoverage, financialData.monthlyIncome);
  const insuranceHealth = getInsuranceHealth(policies, recommendedCoverage);

  const handleNext = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    } else {
      onBack();
    }
  };

  const handleAddPolicy = () => {
    if (!newPolicy.policyNumber.trim() || !newPolicy.amountInsured || !newPolicy.premiumAmount) {
      alert('Please fill in all required fields');
      return;
    }

    const policy: InsurancePolicy = {
      id: Date.now().toString(),
      type: newPolicy.type,
      policyNumber: newPolicy.policyNumber.trim(),
      itemOrMemberInsured: newPolicy.itemOrMemberInsured.trim(),
      insuranceCompany: newPolicy.insuranceCompany,
      amountInsured: parseFloat(newPolicy.amountInsured) || 0,
      insideSuper: newPolicy.insideSuper,
      premiumAmount: parseFloat(newPolicy.premiumAmount) || 0,
      premiumFrequency: newPolicy.premiumFrequency,
      renewalDate: newPolicy.renewalDate,
      contactNumber: newPolicy.contactNumber.trim(),
      claimFormLink: newPolicy.claimFormLink.trim(),
      isActive: true,
      lastUpdated: new Date().toISOString(),
      notes: newPolicy.notes.trim()
    };

    setPolicies([...policies, policy]);
    setNewPolicy({
      type: 'life',
      policyNumber: '',
      itemOrMemberInsured: '',
      insuranceCompany: '',
      amountInsured: '',
      insideSuper: false,
      premiumAmount: '',
      premiumFrequency: 'monthly',
      renewalDate: '',
      contactNumber: '',
      claimFormLink: '',
      notes: ''
    });
    setShowAddPolicy(false);
  };

  const handleRemovePolicy = (policyId: string) => {
    setPolicies(policies.filter(policy => policy.id !== policyId));
  };

  const handleComplete = () => {
    const insuranceData: InsuranceData = {
      policies,
      totalCoverage,
      totalPremiums,
      upcomingRenewals,
      coverageGaps
    };

    onComplete(insuranceData);
  };

  const getIconComponent = (iconName: string) => {
    const icons = {
      Heart,
      Shield,
      DollarSign,
      Plus: Plus,
      Home,
      Package,
      Car,
      Plane,
      Briefcase,
      Users
    };
    return icons[iconName as keyof typeof icons] || Shield;
  };

  // Get next renewal date for urgency display
  const nextRenewal = upcomingRenewals[0];

  return (
    <div className="min-h-screen bg-gradient-to-br from-emerald-50 via-teal-50 to-cyan-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between mb-8"
        >
          <div className="flex items-center gap-4">
            <img src={obieLogo} alt="Obiemoney Logo" className="h-10 w-auto" />
            <div>
              <h1 className="text-3xl font-bold text-emerald-600">Insurance</h1>
              <p className="text-gray-600">Step 7: Do I have Insurance I need?</p>
              <div className="flex items-center gap-2 mt-1">
                <span className="text-xs bg-emerald-100 text-emerald-600 px-2 py-1 rounded-full font-medium">
                  STAGE 3: PROTECT
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 bg-white/60 rounded-full px-4 py-2">
              <span className="text-sm font-medium text-gray-600">Step {currentStep} of 4</span>
            </div>
          </div>
        </motion.div>

        {/* Progress Bar */}
        <div className="mb-8">
          <Progress value={(currentStep / 4) * 100} className="h-2 bg-white/60" />
          <div className="flex justify-between mt-2 text-sm text-gray-600">
            <span className={currentStep >= 1 ? 'text-emerald-600 font-medium' : ''}>Learn</span>
            <span className={currentStep >= 2 ? 'text-emerald-600 font-medium' : ''}>Add Policies</span>
            <span className={currentStep >= 3 ? 'text-emerald-600 font-medium' : ''}>Gap Analysis</span>
            <span className={currentStep >= 4 ? 'text-emerald-600 font-medium' : ''}>Summary</span>
          </div>
        </div>

        <AnimatePresence mode="wait">
          {/* Step 1: Insurance Education */}
          {currentStep === 1 && (
            <motion.div
              key="step1"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-8"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-emerald-100 rounded-full mb-4">
                    <Shield className="h-8 w-8 text-emerald-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">
                    {insuranceEducationContent.title}
                  </h2>
                  <p className="text-gray-600 max-w-2xl mx-auto">
                    {insuranceEducationContent.description}
                  </p>
                </div>

                <div className="grid md:grid-cols-3 gap-6 mb-8">
                  <Card className="p-6 bg-gradient-to-br from-emerald-50 to-teal-50 border-emerald-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-emerald-100 rounded-full mb-4">
                        <Shield className="h-6 w-6 text-emerald-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Risk Transfer</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Insurance transfers financial risk from you to the insurance company, protecting your wealth.
                      </p>
                      <div className="text-xs text-emerald-600 bg-emerald-50 rounded-lg p-2">
                        Pay small premiums to avoid large losses
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6 bg-gradient-to-br from-teal-50 to-cyan-50 border-teal-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-teal-100 rounded-full mb-4">
                        <Heart className="h-6 w-6 text-teal-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Family Protection</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Protect your family's financial future and maintain their lifestyle if something happens to you.
                      </p>
                      <div className="text-xs text-teal-600 bg-teal-50 rounded-lg p-2">
                        Income replacement + lump sum benefits
                      </div>
                    </div>
                  </Card>

                  <Card className="p-6 bg-gradient-to-br from-cyan-50 to-blue-50 border-cyan-200">
                    <div className="text-center">
                      <div className="inline-flex p-3 bg-cyan-100 rounded-full mb-4">
                        <Target className="h-6 w-6 text-cyan-600" />
                      </div>
                      <h3 className="font-bold text-gray-800 mb-2">Asset Protection</h3>
                      <p className="text-sm text-gray-600 mb-4">
                        Protect your valuable assets like your home, car, and personal belongings from unexpected events.
                      </p>
                      <div className="text-xs text-cyan-600 bg-cyan-50 rounded-lg p-2">
                        Replacement cost coverage
                      </div>
                    </div>
                  </Card>
                </div>

                <div className="space-y-6 mb-8">
                  <h3 className="font-bold text-gray-800 text-center">Types of Insurance You Need</h3>
                  
                  <div className="grid md:grid-cols-2 gap-4">
                    {insuranceTypes.slice(0, 6).map(type => {
                      const IconComponent = getIconComponent(type.icon);
                      return (
                        <Card key={type.id} className="p-4 bg-gray-50 border-gray-200">
                          <div className="flex items-start gap-3">
                            <div 
                              className="p-2 rounded-lg"
                              style={{ backgroundColor: `${type.color}20` }}
                            >
                              <IconComponent className="h-4 w-4" style={{ color: type.color }} />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between mb-1">
                                <h4 className="font-bold text-gray-800">{type.name}</h4>
                                <Badge 
                                  variant={type.importance === 'essential' ? 'destructive' : type.importance === 'recommended' ? 'default' : 'secondary'}
                                  className="text-xs"
                                >
                                  {type.importance}
                                </Badge>
                              </div>
                              <p className="text-sm text-gray-600 mb-2">{type.description}</p>
                              <div className="flex justify-between text-xs">
                                <span className="text-gray-500">Average Cost:</span>
                                <span className="font-medium" style={{ color: type.color }}>{type.averageCost}</span>
                              </div>
                            </div>
                          </div>
                        </Card>
                      );
                    })}
                  </div>
                </div>

                <div className="space-y-4 mb-8">
                  <h3 className="font-bold text-gray-800">Key Insurance Tips</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    {insuranceEducationContent.tips.map((tip, index) => (
                      <div key={index} className="flex items-start gap-3 p-4 bg-emerald-50 rounded-lg">
                        <div className="w-6 h-6 bg-emerald-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <CheckCircle className="h-3 w-3 text-emerald-600" />
                        </div>
                        <p className="text-sm text-gray-700">{tip}</p>
                      </div>
                    ))}
                  </div>
                </div>

                <Alert className="border-emerald-200 bg-emerald-50">
                  <Info className="h-4 w-4 text-emerald-600" />
                  <AlertDescription className="text-emerald-800">
                    <strong>Remember:</strong> Insurance is not an investment - it's protection. The goal is to never need to claim, 
                    but to have peace of mind knowing you're covered if the unexpected happens.
                  </AlertDescription>
                </Alert>
              </Card>
            </motion.div>
          )}

          {/* Step 2: Add Insurance Policies */}
          {currentStep === 2 && (
            <motion.div
              key="step2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="flex justify-between items-center mb-6">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">Manage Your Insurance Policies</h2>
                    <p className="text-gray-600">
                      Add your current insurance policies to track coverage, premiums, and renewal dates.
                    </p>
                  </div>
                  <Button 
                    onClick={() => setShowAddPolicy(true)}
                    className="bg-emerald-600 hover:bg-emerald-700"
                  >
                    <Plus size={16} className="mr-2" />
                    Add Policy
                  </Button>
                </div>

                {/* Existing Policies */}
                <div className="space-y-4 mb-6">
                  {policies.length > 0 ? (
                    policies.map((policy) => {
                      const insuranceType = insuranceTypes.find(type => type.id === policy.type);
                      const daysUntilRenewal = getDaysUntilRenewal(policy.renewalDate);
                      const isRenewalSoon = daysUntilRenewal <= 30 && daysUntilRenewal >= 0;
                      
                      return (
                        <Card key={policy.id} className="p-6 bg-gradient-to-r from-emerald-50 to-teal-50 border-emerald-200">
                          <div className="flex items-start gap-4">
                            <div 
                              className="w-16 h-16 rounded-lg flex items-center justify-center flex-shrink-0"
                              style={{ backgroundColor: `${insuranceType?.color}20` }}
                            >
                              <Shield className="h-8 w-8" style={{ color: insuranceType?.color }} />
                            </div>
                            
                            <div className="flex-1">
                              <div className="flex items-start justify-between mb-2">
                                <div>
                                  <h3 className="font-bold text-gray-800 mb-1">{insuranceType?.name || policy.type}</h3>
                                  <div className="flex items-center gap-4 text-sm text-gray-600">
                                    <span>Policy: {policy.policyNumber}</span>
                                    <span>{policy.insuranceCompany}</span>
                                    <span>Coverage: {formatAmount(policy.amountInsured)}</span>
                                  </div>
                                </div>
                                <div className="flex items-center gap-2">
                                  {isRenewalSoon && (
                                    <Badge variant="destructive" className="text-xs">
                                      Renewal in {daysUntilRenewal} days
                                    </Badge>
                                  )}
                                  {policy.insideSuper && (
                                    <Badge variant="secondary" className="text-xs">
                                      Inside Super
                                    </Badge>
                                  )}
                                  <Button 
                                    variant="outline" 
                                    size="sm"
                                    onClick={() => handleRemovePolicy(policy.id)}
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 size={16} />
                                  </Button>
                                </div>
                              </div>
                              
                              <div className="grid md:grid-cols-3 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-600">Premium:</span>
                                  <p className="font-medium text-emerald-600">
                                    {formatAmount(policy.premiumAmount)}/{policy.premiumFrequency}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-gray-600">Renewal:</span>
                                  <p className="font-medium">{formatDate(policy.renewalDate)}</p>
                                </div>
                                <div>
                                  <span className="text-gray-600">Insured:</span>
                                  <p className="font-medium">{policy.itemOrMemberInsured}</p>
                                </div>
                              </div>
                              
                              {policy.contactNumber && (
                                <div className="flex items-center gap-2 mt-3 text-sm">
                                  <Phone className="h-4 w-4 text-gray-500" />
                                  <span className="text-gray-600">Contact: {policy.contactNumber}</span>
                                </div>
                              )}
                            </div>
                          </div>
                        </Card>
                      );
                    })
                  ) : (
                    <Card className="p-8 text-center bg-gray-50">
                      <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600 mb-4">No insurance policies added yet</p>
                      <Button 
                        onClick={() => setShowAddPolicy(true)}
                        className="bg-emerald-600 hover:bg-emerald-700"
                      >
                        Add Your First Policy
                      </Button>
                    </Card>
                  )}
                </div>

                {/* Add Policy Form */}
                {showAddPolicy && (
                  <Card className="p-6 bg-gradient-to-r from-teal-50 to-emerald-50 border-teal-200 mb-6">
                    <h3 className="font-bold text-gray-800 mb-4">Add Insurance Policy</h3>
                    <div className="space-y-4">
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Type of Insurance *
                          </label>
                          <Select
                            value={newPolicy.type}
                            onValueChange={(value: any) => setNewPolicy({...newPolicy, type: value})}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {insuranceTypes.map(type => (
                                <SelectItem key={type.id} value={type.id}>{type.name}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Policy Number *
                          </label>
                          <Input
                            placeholder="POL123456789"
                            value={newPolicy.policyNumber}
                            onChange={(e) => setNewPolicy({...newPolicy, policyNumber: e.target.value})}
                          />
                        </div>
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Item/Member Insured
                          </label>
                          <Input
                            placeholder="John Smith, 2020 Toyota Camry, etc."
                            value={newPolicy.itemOrMemberInsured}
                            onChange={(e) => setNewPolicy({...newPolicy, itemOrMemberInsured: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Insurance Company *
                          </label>
                          <Select
                            value={newPolicy.insuranceCompany}
                            onValueChange={(value) => setNewPolicy({...newPolicy, insuranceCompany: value})}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select company" />
                            </SelectTrigger>
                            <SelectContent>
                              {popularInsuranceCompanies.map(company => (
                                <SelectItem key={company} value={company}>{company}</SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Amount Insured *
                          </label>
                          <Input
                            type="number"
                            placeholder="500000"
                            value={newPolicy.amountInsured}
                            onChange={(e) => setNewPolicy({...newPolicy, amountInsured: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Premium Amount *
                          </label>
                          <Input
                            type="number"
                            placeholder="150"
                            value={newPolicy.premiumAmount}
                            onChange={(e) => setNewPolicy({...newPolicy, premiumAmount: e.target.value})}
                          />
                        </div>
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Premium Frequency
                          </label>
                          <Select
                            value={newPolicy.premiumFrequency}
                            onValueChange={(value: any) => setNewPolicy({...newPolicy, premiumFrequency: value})}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="weekly">Weekly</SelectItem>
                              <SelectItem value="monthly">Monthly</SelectItem>
                              <SelectItem value="quarterly">Quarterly</SelectItem>
                              <SelectItem value="yearly">Yearly</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Renewal Date
                          </label>
                          <Input
                            type="date"
                            value={newPolicy.renewalDate}
                            onChange={(e) => setNewPolicy({...newPolicy, renewalDate: e.target.value})}
                          />
                        </div>
                      </div>
                      
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Contact Number
                          </label>
                          <Input
                            placeholder="1300 123 456"
                            value={newPolicy.contactNumber}
                            onChange={(e) => setNewPolicy({...newPolicy, contactNumber: e.target.value})}
                          />
                        </div>
                        
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Claim Form Link
                          </label>
                          <Input
                            placeholder="https://company.com/claims"
                            value={newPolicy.claimFormLink}
                            onChange={(e) => setNewPolicy({...newPolicy, claimFormLink: e.target.value})}
                          />
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={newPolicy.insideSuper}
                          onCheckedChange={(checked) => setNewPolicy({...newPolicy, insideSuper: checked})}
                        />
                        <label className="text-sm font-medium text-gray-700">
                          Inside Super? (Life/TPD insurance held within superannuation)
                        </label>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Notes
                        </label>
                        <Textarea
                          placeholder="Additional notes about this policy..."
                          value={newPolicy.notes}
                          onChange={(e) => setNewPolicy({...newPolicy, notes: e.target.value})}
                          rows={3}
                        />
                      </div>
                    </div>
                    
                    <div className="flex gap-3 mt-6">
                      <Button 
                        onClick={handleAddPolicy}
                        disabled={!newPolicy.policyNumber.trim() || !newPolicy.amountInsured || !newPolicy.premiumAmount}
                        className="bg-emerald-600 hover:bg-emerald-700"
                      >
                        Add Policy
                      </Button>
                      <Button 
                        variant="outline"
                        onClick={() => setShowAddPolicy(false)}
                      >
                        Cancel
                      </Button>
                    </div>
                  </Card>
                )}

                {/* Summary */}
                {policies.length > 0 && (
                  <Card className="p-6 bg-gradient-to-r from-emerald-50 to-teal-50 border-emerald-200">
                    <div className="grid md:grid-cols-4 gap-6 text-center">
                      <div>
                        <p className="text-sm text-gray-600">Total Policies</p>
                        <p className="text-2xl font-bold text-emerald-600">{policies.length}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Total Coverage</p>
                        <p className="text-2xl font-bold text-teal-600">{formatAmount(totalCoverage)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Monthly Premiums</p>
                        <p className="text-2xl font-bold text-cyan-600">{formatAmount(totalPremiums)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Upcoming Renewals</p>
                        <p className="text-2xl font-bold text-orange-600">{upcomingRenewals.length}</p>
                      </div>
                    </div>
                  </Card>
                )}
              </Card>
            </motion.div>
          )}

          {/* Step 3: Gap Analysis */}
          {currentStep === 3 && (
            <motion.div
              key="step3"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-emerald-100 rounded-full mb-4">
                    <AlertCircle className="h-8 w-8 text-emerald-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Insurance Gap Analysis</h2>
                  <p className="text-gray-600">
                    Let's analyze your current coverage and identify any gaps in your insurance protection.
                  </p>
                </div>

                {/* Insurance Health Score */}
                <Card className={`p-6 mb-6 bg-gradient-to-r ${
                  insuranceHealth.status === 'excellent' || insuranceHealth.status === 'good'
                    ? 'from-green-50 to-emerald-50 border-green-200'
                    : insuranceHealth.status === 'fair'
                    ? 'from-yellow-50 to-orange-50 border-yellow-200'
                    : 'from-red-50 to-pink-50 border-red-200'
                }`}>
                  <div className="text-center">
                    <h3 className="font-bold text-gray-800 mb-2">Insurance Health Score</h3>
                    <div className="text-4xl font-bold mb-2" style={{
                      color: insuranceHealth.status === 'excellent' || insuranceHealth.status === 'good' ? '#059669' :
                             insuranceHealth.status === 'fair' ? '#D97706' : '#DC2626'
                    }}>
                      {Math.round(insuranceHealth.score)}%
                    </div>
                    <p className="text-gray-600 mb-4">{insuranceHealth.message}</p>
                    <Progress value={insuranceHealth.score} className="h-3 max-w-md mx-auto" />
                  </div>
                </Card>

                {/* Recommended vs Actual Coverage */}
                <Card className="p-6 mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                  <h3 className="font-bold text-gray-800 mb-4 text-center">Recommended vs Your Coverage</h3>
                  <div className="space-y-4">
                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="p-4 bg-white rounded-lg">
                        <h4 className="font-bold text-gray-800 mb-3">Life Insurance</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Recommended:</span>
                            <span className="font-medium">{formatAmount(recommendedCoverage.lifeInsurance)}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Your Coverage:</span>
                            <span className="font-medium text-emerald-600">
                              {formatAmount(policies.filter(p => p.type === 'life').reduce((sum, p) => sum + p.amountInsured, 0))}
                            </span>
                          </div>
                          <Progress 
                            value={Math.min(100, (policies.filter(p => p.type === 'life').reduce((sum, p) => sum + p.amountInsured, 0) / recommendedCoverage.lifeInsurance) * 100)} 
                            className="h-2" 
                          />
                        </div>
                      </div>

                      <div className="p-4 bg-white rounded-lg">
                        <h4 className="font-bold text-gray-800 mb-3">TPD Insurance</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Recommended:</span>
                            <span className="font-medium">{formatAmount(recommendedCoverage.tpdInsurance)}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Your Coverage:</span>
                            <span className="font-medium text-emerald-600">
                              {formatAmount(policies.filter(p => p.type === 'tpd').reduce((sum, p) => sum + p.amountInsured, 0))}
                            </span>
                          </div>
                          <Progress 
                            value={Math.min(100, (policies.filter(p => p.type === 'tpd').reduce((sum, p) => sum + p.amountInsured, 0) / recommendedCoverage.tpdInsurance) * 100)} 
                            className="h-2" 
                          />
                        </div>
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div className="p-4 bg-white rounded-lg">
                        <h4 className="font-bold text-gray-800 mb-3">Income Protection</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Recommended (Monthly):</span>
                            <span className="font-medium">{formatAmount(recommendedCoverage.incomeProtection)}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Your Coverage:</span>
                            <span className="font-medium text-emerald-600">
                              {formatAmount(policies.filter(p => p.type === 'income_protection').reduce((sum, p) => sum + p.amountInsured, 0))}
                            </span>
                          </div>
                          <Progress 
                            value={Math.min(100, (policies.filter(p => p.type === 'income_protection').reduce((sum, p) => sum + p.amountInsured, 0) / recommendedCoverage.incomeProtection) * 100)} 
                            className="h-2" 
                          />
                        </div>
                      </div>

                      <div className="p-4 bg-white rounded-lg">
                        <h4 className="font-bold text-gray-800 mb-3">Home/Contents Insurance</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Estimated Need:</span>
                            <span className="font-medium">{formatAmount(recommendedCoverage.homeInsurance)}</span>
                          </div>
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-600">Your Coverage:</span>
                            <span className="font-medium text-emerald-600">
                              {formatAmount(policies.filter(p => p.type === 'home_building' || p.type === 'home_contents').reduce((sum, p) => sum + p.amountInsured, 0))}
                            </span>
                          </div>
                          <Progress 
                            value={Math.min(100, (policies.filter(p => p.type === 'home_building' || p.type === 'home_contents').reduce((sum, p) => sum + p.amountInsured, 0) / recommendedCoverage.homeInsurance) * 100)} 
                            className="h-2" 
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>

                {/* Coverage Gaps */}
                {coverageGaps.length > 0 && (
                  <Card className="p-6 mb-6 bg-gradient-to-r from-orange-50 to-red-50 border-orange-200">
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <AlertCircle className="h-5 w-5 text-orange-600" />
                      Coverage Gaps Identified
                    </h3>
                    <div className="space-y-3">
                      {coverageGaps.map((gap, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 bg-white rounded-lg">
                          <div className="w-2 h-2 bg-orange-500 rounded-full flex-shrink-0"></div>
                          <p className="text-gray-700">{gap}</p>
                        </div>
                      ))}
                    </div>
                  </Card>
                )}

                {/* Upcoming Renewals */}
                {upcomingRenewals.length > 0 && (
                  <Card className="p-6 bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <Clock className="h-5 w-5 text-yellow-600" />
                      Upcoming Renewals
                    </h3>
                    <div className="space-y-3">
                      {upcomingRenewals.map((policy) => {
                        const daysUntil = getDaysUntilRenewal(policy.renewalDate);
                        return (
                          <div key={policy.id} className="flex items-center justify-between p-3 bg-white rounded-lg">
                            <div>
                              <p className="font-medium text-gray-800">{policy.type.replace('_', ' ').toUpperCase()}</p>
                              <p className="text-sm text-gray-600">{policy.insuranceCompany} • {policy.policyNumber}</p>
                            </div>
                            <div className="text-right">
                              <p className="font-medium text-orange-600">{formatDate(policy.renewalDate)}</p>
                              <p className="text-sm text-gray-500">{daysUntil} days remaining</p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </Card>
                )}
              </Card>
            </motion.div>
          )}

          {/* Step 4: Summary */}
          {currentStep === 4 && (
            <motion.div
              key="step4"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="space-y-6"
            >
              <Card className="p-8 bg-white/80 backdrop-blur-sm">
                <div className="text-center mb-8">
                  <div className="inline-flex p-4 bg-emerald-100 rounded-full mb-4">
                    <CheckCircle className="h-8 w-8 text-emerald-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-800 mb-4">Your Insurance Summary</h2>
                  <p className="text-gray-600">
                    You've entered Stage 3: Protect! Your insurance portfolio is now organized and optimized to protect your wealth and family.
                  </p>
                </div>

                <div className="space-y-6">
                  {/* Overall Health Score */}
                  <Card className={`p-6 bg-gradient-to-r ${
                    insuranceHealth.status === 'excellent' || insuranceHealth.status === 'good'
                      ? 'from-green-50 to-emerald-50 border-green-200'
                      : insuranceHealth.status === 'fair'
                      ? 'from-yellow-50 to-orange-50 border-yellow-200'
                      : 'from-red-50 to-pink-50 border-red-200'
                  }`}>
                    <div className="text-center">
                      <h3 className="font-bold text-gray-800 mb-2">Insurance Health</h3>
                      <p className="text-3xl font-bold mb-2" style={{
                        color: insuranceHealth.status === 'excellent' || insuranceHealth.status === 'good' ? '#059669' :
                               insuranceHealth.status === 'fair' ? '#D97706' : '#DC2626'
                      }}>
                        {Math.round(insuranceHealth.score)}% - {insuranceHealth.message}
                      </p>
                      <div className="grid md:grid-cols-4 gap-4 mt-4">
                        <div>
                          <p className="text-sm text-gray-600">Policies</p>
                          <p className="font-bold text-emerald-600">{policies.length}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Total Coverage</p>
                          <p className="font-bold text-teal-600">{formatAmount(totalCoverage)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Monthly Premiums</p>
                          <p className="font-bold text-cyan-600">{formatAmount(totalPremiums)}</p>
                        </div>
                        <div>
                          <p className="text-sm text-gray-600">Coverage Gaps</p>
                          <p className="font-bold text-orange-600">{coverageGaps.length}</p>
                        </div>
                      </div>
                    </div>
                  </Card>

                  {/* Policy Summary */}
                  <Card className="p-6 bg-gradient-to-r from-emerald-50 to-teal-50 border-emerald-200">
                    <h3 className="font-bold text-gray-800 mb-4 text-center">Policy Portfolio</h3>
                    <div className="space-y-3">
                      {policies.map((policy) => {
                        const insuranceType = insuranceTypes.find(type => type.id === policy.type);
                        return (
                          <div key={policy.id} className="flex items-center justify-between p-3 bg-white rounded-lg">
                            <div className="flex items-center gap-3">
                              <Shield className="h-5 w-5 text-emerald-600" />
                              <div>
                                <p className="font-medium text-gray-800">{insuranceType?.name || policy.type}</p>
                                <p className="text-sm text-gray-600">
                                  {policy.insuranceCompany} • {policy.policyNumber}
                                </p>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-bold text-emerald-600">{formatAmount(policy.amountInsured)}</p>
                              <p className="text-xs text-gray-500">
                                {formatAmount(policy.premiumAmount)}/{policy.premiumFrequency}
                              </p>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </Card>

                  {/* Next Steps */}
                  <Card className="p-6 bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
                    <h3 className="font-bold text-gray-800 mb-4 flex items-center gap-2">
                      <Sparkles className="h-5 w-5 text-purple-600" />
                      What Happens Next
                    </h3>
                    <div className="grid md:grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="w-12 h-12 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Bell className="h-6 w-6 text-emerald-600" />
                        </div>
                        <h4 className="font-bold text-gray-800 mb-2">Renewal Reminders</h4>
                        <p className="text-sm text-gray-600">Get notified before your policies expire to avoid coverage gaps.</p>
                      </div>
                      
                      <div className="text-center">
                        <div className="w-12 h-12 bg-teal-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <FileText className="h-6 w-6 text-teal-600" />
                        </div>
                        <h4 className="font-bold text-gray-800 mb-2">Document Storage</h4>
                        <p className="text-sm text-gray-600">Keep all your policy documents organized and easily accessible.</p>
                      </div>
                      
                      <div className="text-center">
                        <div className="w-12 h-12 bg-cyan-100 rounded-full flex items-center justify-center mx-auto mb-3">
                          <Target className="h-6 w-6 text-cyan-600" />
                        </div>
                        <h4 className="font-bold text-gray-800 mb-2">Annual Reviews</h4>
                        <p className="text-sm text-gray-600">Review your coverage annually as your life and assets change.</p>
                      </div>
                    </div>
                  </Card>

                  {/* Success Message */}
                  <Alert className="border-emerald-200 bg-emerald-50">
                    <CheckCircle className="h-4 w-4 text-emerald-600" />
                    <AlertDescription className="text-emerald-800">
                      <strong>Congratulations!</strong> You've begun Stage 3: Protect! Your insurance strategy is now in place to protect your wealth, assets, and family from unexpected events. Your financial safety net is secure.
                    </AlertDescription>
                  </Alert>
                </div>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8">
          <Button
            variant="outline"
            onClick={handleBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft size={16} />
            {currentStep === 1 ? 'Back to Dashboard' : 'Back'}
          </Button>
          
          <div className="flex gap-3">
            {currentStep < 4 ? (
              <Button
                onClick={handleNext}
                className="flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700"
              >
                Continue
                <ArrowRight size={16} />
              </Button>
            ) : (
              <Button
                onClick={handleComplete}
                className="flex items-center gap-2 bg-emerald-600 hover:bg-emerald-700"
              >
                Complete Insurance Setup
                <CheckCircle size={16} />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}