# Financial Planning App Integration Guide

## Dependencies Required

```bash
# Core dependencies
npm install react react-dom

# Radix UI components
npm install @radix-ui/react-accordion @radix-ui/react-alert-dialog @radix-ui/react-aspect-ratio @radix-ui/react-avatar @radix-ui/react-checkbox @radix-ui/react-collapsible @radix-ui/react-context-menu @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-hover-card @radix-ui/react-label @radix-ui/react-menubar @radix-ui/react-navigation-menu @radix-ui/react-popover @radix-ui/react-progress @radix-ui/react-radio-group @radix-ui/react-scroll-area @radix-ui/react-select @radix-ui/react-separator @radix-ui/react-slider @radix-ui/react-slot @radix-ui/react-switch @radix-ui/react-tabs @radix-ui/react-toast @radix-ui/react-toggle @radix-ui/react-toggle-group @radix-ui/react-tooltip

# Animation and utilities
npm install motion/react lucide-react class-variance-authority clsx tailwind-merge

# Form handling
npm install react-hook-form

# Charts and additional UI
npm install recharts sonner embla-carousel-react cmdk input-otp next-themes

# TypeScript types (if using TypeScript)
npm install -D @types/react @types/react-dom
```

## File Structure to Copy

```
your-project/
├── components/
│   ├── ui/           # All UI components (buttons, cards, etc.)
│   ├── *.tsx         # All main components (Dashboard, ExpenseTracker, etc.)
├── types/            # TypeScript type definitions
├── constants/        # Configuration and data constants
├── utils/            # Utility functions
└── styles/
    └── globals.css   # CSS variables and styling
```

## Integration Steps

1. Copy all directories listed above to your project
2. Install the dependencies using the npm command above
3. Import and configure the globals.css in your main CSS file
4. Replace figma asset imports with actual image files
5. Import and use components as needed

## Usage Example

```typescript
import { Dashboard } from './components/Dashboard';
import { CumulativeSavingsPool } from './components/CumulativeSavingsPool';

// Use individual components
function MyApp() {
  return (
    <div>
      <CumulativeSavingsPool 
        totalSavings={5000}
        onContributeSavings={(amount) => console.log('Contributing:', amount)}
      />
    </div>
  );
}
```

## Asset Replacements Needed

Replace these figma imports with actual image files:
- `figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png` (obieLogo)

## Tailwind Configuration

Ensure your tailwind.config.js includes the component paths:

```javascript
module.exports = {
  content: [
    "./components/**/*.{js,ts,jsx,tsx}",
    // ... your other paths
  ],
  // ... rest of config
}
```
