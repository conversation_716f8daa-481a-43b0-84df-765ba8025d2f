import imgImage8 from "figma:asset/0bcb4b7091d1c9d1066cda13ac944f32622bf913.png";

function Frame2() {
  return (
    <div className="bg-[rgba(255,75,0,0.13)] box-border content-stretch flex flex-row gap-2.5 items-start justify-start px-1 py-0.5 relative rounded-md shrink-0">
      <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic opacity-[0.85] relative shrink-0 text-[#4b4848] text-[12px] text-left text-nowrap">
        <p className="block leading-[16px] whitespace-pre">20%</p>
      </div>
    </div>
  );
}

function Frame1618873495() {
  return (
    <div className="box-border content-stretch flex flex-row gap-1.5 h-[21px] items-end justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#6130df] text-[24px] text-left text-nowrap">
        <p className="block leading-[21px] whitespace-pre">$2,238</p>
      </div>
      <Frame2 />
    </div>
  );
}

function Frame1618873489() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-col gap-2.5 grow items-start justify-end min-h-px min-w-px p-0 relative shrink-0">
      <div
        className="font-['Nohemi:Regular',_sans-serif] leading-[0] min-w-full not-italic relative shrink-0 text-[#4b4848] text-[14px] text-left"
        style={{ width: "min-content" }}
      >
        <p className="block leading-[normal]">Available from Income</p>
      </div>
      <Frame1618873495 />
    </div>
  );
}

function Frame1618873496() {
  return (
    <div className="box-border content-stretch flex flex-row gap-1.5 h-[21px] items-end justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#6130df] text-[24px] text-left text-nowrap">
        <p className="block leading-[21px] whitespace-pre">$17,500</p>
      </div>
    </div>
  );
}

function Frame1618873491() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-col gap-2.5 grow items-start justify-end min-h-px min-w-px p-0 relative shrink-0">
      <div
        className="font-['Nohemi:Regular',_sans-serif] leading-[0] min-w-full not-italic relative shrink-0 text-[#4b4848] text-[14px] text-left"
        style={{ width: "min-content" }}
      >
        <p className="block leading-[normal]">Available Cash Savings</p>
      </div>
      <Frame1618873496 />
    </div>
  );
}

function Frame1618873492() {
  return (
    <div className="box-border content-stretch flex flex-row gap-4 items-start justify-start px-3.5 py-[18px] relative rounded-[10px] shrink-0 w-[349px]">
      <div
        aria-hidden="true"
        className="absolute border border-[rgba(97,48,223,0.3)] border-solid inset-0 pointer-events-none rounded-[10px]"
      />
      <Frame1618873489 />
      <Frame1618873491 />
    </div>
  );
}

function Frame1618873615() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[30px] items-start justify-center p-0 relative shrink-0">
      <Frame1618873492 />
      <div
        className="bg-center bg-cover bg-no-repeat h-[313px] shrink-0 w-[318px]"
        data-name="image 8"
        style={{ backgroundImage: `url('${imgImage8}')` }}
      />
    </div>
  );
}

function Frame1618873598() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-2.5 grow items-start justify-start min-h-px min-w-px px-0 py-3 relative self-stretch shrink-0">
      <div
        aria-hidden="true"
        className="absolute border-[#d1d1d1] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="font-['Nohemi:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#787878] text-[26px] text-center text-nowrap">
        <p className="block leading-[normal] whitespace-pre">0</p>
      </div>
    </div>
  );
}

function Frame1618873595() {
  return (
    <div className="box-border content-stretch flex flex-row gap-[9px] items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873598 />
    </div>
  );
}

function Frame1618873593() {
  return (
    <div className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-full">
      <div className="font-['Nohemi:Semi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#131313] text-[18px] text-left w-full">
        <p className="block leading-[30px]">Emergency Fund</p>
      </div>
      <Frame1618873595 />
    </div>
  );
}

function Frame1618873581() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[18px] items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873593 />
    </div>
  );
}

function Frame1618873599() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-2.5 grow items-start justify-start min-h-px min-w-px px-0 py-3 relative self-stretch shrink-0">
      <div
        aria-hidden="true"
        className="absolute border-[#d1d1d1] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="font-['Nohemi:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#787878] text-[26px] text-center text-nowrap">
        <p className="block leading-[normal] whitespace-pre">0</p>
      </div>
    </div>
  );
}

function Frame1618873596() {
  return (
    <div className="box-border content-stretch flex flex-row gap-[9px] items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873599 />
    </div>
  );
}

function Frame1618873594() {
  return (
    <div className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-full">
      <div className="font-['Nohemi:Semi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#131313] text-[18px] text-left w-full">
        <p className="block leading-[30px]">Buffer for Lumpy Expenses</p>
      </div>
      <Frame1618873596 />
    </div>
  );
}

function Frame1618873589() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[18px] items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873594 />
    </div>
  );
}

function Frame1618873600() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-2.5 grow items-start justify-start min-h-px min-w-px px-0 py-3 relative self-stretch shrink-0">
      <div
        aria-hidden="true"
        className="absolute border-[#d1d1d1] border-[0px_0px_1px] border-solid inset-0 pointer-events-none"
      />
      <div className="font-['Nohemi:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#787878] text-[26px] text-center text-nowrap">
        <p className="block leading-[normal] whitespace-pre">12,432</p>
      </div>
    </div>
  );
}

function Frame1618873597() {
  return (
    <div className="box-border content-stretch flex flex-row gap-[9px] items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873600 />
    </div>
  );
}

function Frame1618873601() {
  return (
    <div className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-full">
      <div className="font-['Nohemi:Semi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#131313] text-[18px] text-left w-full">
        <p className="block leading-[30px]">3 Months of Expenses</p>
      </div>
      <Frame1618873597 />
    </div>
  );
}

function Frame1618873590() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[18px] items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873601 />
    </div>
  );
}

function Frame1618873588() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[26px] items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873581 />
      <Frame1618873589 />
      <Frame1618873590 />
    </div>
  );
}

function PrimaryBtn() {
  return (
    <div
      className="bg-[#6130df] box-border content-stretch flex flex-row h-[50px] items-center justify-center px-[19px] py-2 relative rounded shrink-0"
      data-name="Primary BTN"
    >
      <div className="font-['Nohemi:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#ffffff] text-[16px] text-left text-nowrap">
        <p className="block leading-[24px] whitespace-pre">Next</p>
      </div>
    </div>
  );
}

function Frame427319529() {
  return (
    <div className="box-border content-stretch flex flex-row gap-[31px] items-center justify-start p-0 relative shrink-0 w-full">
      <PrimaryBtn />
    </div>
  );
}

function Frame1618873402() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[18px] items-start justify-start p-0 relative shrink-0 w-[417px]">
      <Frame1618873588 />
      <div className="h-0 relative shrink-0 w-full">
        <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 417 1"
          >
            <line
              id="Line 185"
              stroke="var(--stroke-0, #ECECEC)"
              x2="417"
              y1="0.5"
              y2="0.5"
            />
          </svg>
        </div>
      </div>
      <Frame427319529 />
    </div>
  );
}

function Frame1618873611() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[27px] items-start justify-center p-0 relative shrink-0 w-[563px]">
      <div
        className="font-['Schibsted_Grotesk:Bold',_sans-serif] font-bold leading-[0] min-w-full relative shrink-0 text-[#6130df] text-[38px] text-left"
        style={{ width: "min-content" }}
      >
        <p className="block leading-[normal]">Creating Your Safety Net</p>
      </div>
      <Frame1618873402 />
    </div>
  );
}

function Frame1618873403() {
  return (
    <div className="basis-0 grow min-h-px min-w-px relative shrink-0 w-full">
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-row items-start justify-between pb-0 pt-[120px] px-[50px] relative size-full">
          <Frame1618873615 />
          <Frame1618873611 />
        </div>
      </div>
    </div>
  );
}

export default function Frame1618873476() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[19px] items-start justify-center p-0 relative size-full">
      <Frame1618873403 />
    </div>
  );
}