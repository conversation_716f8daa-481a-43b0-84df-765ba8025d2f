import svgPaths from "./svg-ae1gsql6bg";
import imgImage1 from "figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png";
import imgImage17 from "figma:asset/7f9c5fa1c5885bf19804a1f9db506c5b38b39f63.png";
import imgScreenshot20250807At62249Pm2 from "figma:asset/01a5dfda19bcd1ea803c9b555fc237314bd9056a.png";

function UbrandLogo() {
  return (
    <div
      className="absolute contents left-[50px] top-[15.84px]"
      data-name="ubrand-logo"
    >
      <div
        className="absolute bg-center bg-cover bg-no-repeat h-[33.327px] left-[50px] top-[15.84px] w-[141px]"
        data-name="image 1"
        style={{ backgroundImage: `url('${imgImage1}')` }}
      />
    </div>
  );
}

function Frame1000003363() {
  return (
    <div className="box-border content-stretch flex flex-col items-end justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#ffffff] text-[14px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">John Doe</p>
      </div>
    </div>
  );
}

function KeyboardArrowDown() {
  return (
    <div className="relative shrink-0 size-6" data-name="keyboard_arrow_down">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="keyboard_arrow_down">
          <path
            d={svgPaths.p3026c200}
            fill="var(--fill-0, white)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Frame1000003362() {
  return (
    <div
      className="absolute box-border content-stretch flex flex-row gap-1 items-center justify-center p-0 right-[50px] translate-y-[-50%]"
      style={{ top: "calc(50% + 0.5px)" }}
    >
      <Frame1000003363 />
      <KeyboardArrowDown />
    </div>
  );
}

function HeaderAdmin() {
  return (
    <div className="absolute bg-[#6130df] inset-0" data-name="Header-Admin">
      <div className="overflow-clip relative size-full">
        <UbrandLogo />
        <Frame1000003362 />
      </div>
      <div
        aria-hidden="true"
        className="absolute border-[0px_0px_1px] border-[rgba(81,81,81,0.04)] border-solid inset-0 pointer-events-none"
      />
    </div>
  );
}

function HeaderAdmin1() {
  return (
    <div
      className="h-[65px] pointer-events-auto sticky top-0 w-[1440px]"
      data-name="Header-Admin"
    >
      <HeaderAdmin />
    </div>
  );
}

function Frame1618873442() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3.5 items-center justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#ff4b00] text-[18px] text-center text-nowrap">
        <p className="block leading-[22px] whitespace-pre">Optimise</p>
      </div>
    </div>
  );
}

function Frame1618873447() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3.5 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873442 />
      <div className="h-0 relative shrink-0 w-full">
        <div className="absolute bottom-0 left-0 right-0 top-[-4px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 202 4"
          >
            <line
              id="Line 137"
              stroke="var(--stroke-0, #FF4B00)"
              strokeWidth="4"
              x2="202"
              y1="2"
              y2="2"
            />
          </svg>
        </div>
      </div>
    </div>
  );
}

function Content() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-[5px] items-start justify-center leading-[0] not-italic p-0 relative shrink-0 text-left w-full"
      data-name="Content"
    >
      <div className="flex flex-col font-['Nohemi:Medium',_sans-serif] justify-center relative shrink-0 text-[#6130df] text-[16px] w-full">
        <p className="block leading-[20px]">Budget</p>
      </div>
      <div className="flex flex-col font-['Nohemi:Regular',_sans-serif] justify-center relative shrink-0 text-[#343434] text-[12px] w-full">
        <p className="block leading-[16px]">
          Spend less without feeling like you’re missing out
        </p>
      </div>
    </div>
  );
}

function VerticalTabBoxed() {
  return (
    <div
      className="bg-[rgba(97,48,223,0.1)] box-border content-stretch flex flex-col gap-2.5 items-center justify-center px-[18px] py-3.5 relative rounded-[3px] shrink-0 w-[202px]"
      data-name="VerticalTab - boxed"
    >
      <div
        aria-hidden="true"
        className="absolute border-[#6130df] border-[0px_0px_0px_3px] border-solid inset-0 pointer-events-none rounded-[3px]"
      />
      <Content />
    </div>
  );
}

function PillerVerticalTab() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-5 items-start justify-start p-0 relative rounded-md shrink-0 w-[202px]"
      data-name="Piller - VerticalTab"
    >
      <Frame1618873447 />
      <VerticalTabBoxed />
    </div>
  );
}

function Frame427319527() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-col gap-2.5 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
      <div className="font-['Schibsted_Grotesk:Bold',_sans-serif] font-bold leading-[0] relative shrink-0 text-[#6130df] text-[32px] text-left w-full">
        <p className="block leading-[normal]">Add your current expenses</p>
      </div>
    </div>
  );
}

function Frame1618873554() {
  return (
    <div className="box-border content-stretch flex flex-row gap-[60px] items-center justify-start p-0 relative shrink-0 w-[638px]">
      <Frame427319527 />
    </div>
  );
}

function Frame1618873618() {
  return (
    <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873554 />
    </div>
  );
}

function Frame1618873524() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-col gap-px grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
      <div className="font-['Nohemi:Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#4b4848] text-[17px] text-left w-full">
        <p className="block leading-[normal]">Living Expenses</p>
      </div>
    </div>
  );
}

function Frame1618873526() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-3 grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0">
      <div
        className="bg-center bg-cover bg-no-repeat h-[31.347px] shrink-0 w-8"
        data-name="image 17"
        style={{ backgroundImage: `url('${imgImage17}')` }}
      />
      <Frame1618873524 />
    </div>
  );
}

function Frame1618873540() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-start justify-start p-0 relative shrink-0 w-[236px]">
      <Frame1618873526 />
    </div>
  );
}

function Frame82() {
  return (
    <div className="bg-[#ecf8ea] box-border content-stretch flex flex-row gap-2.5 items-start justify-start pb-px pt-0 px-1 relative rounded shrink-0">
      <div className="font-['Inter:Medium',_sans-serif] font-medium leading-[0] not-italic opacity-[0.85] relative shrink-0 text-[#27ae60] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">55%</p>
      </div>
    </div>
  );
}

function Frame1618873495() {
  return (
    <div className="box-border content-stretch flex flex-row gap-1.5 h-[21px] items-center justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Semi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#27ae60] text-[22px] text-left text-nowrap">
        <p className="block leading-[21px] whitespace-pre">$5,500</p>
      </div>
      <Frame82 />
    </div>
  );
}

function Frame1618873489() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#4b4848] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">
          Spending Plan Goal
        </p>
      </div>
      <Frame1618873495 />
    </div>
  );
}

function Frame83() {
  return (
    <div className="bg-[#ecf8ea] box-border content-stretch flex flex-row gap-2.5 items-start justify-start pb-px pt-0 px-1 relative rounded shrink-0">
      <div className="font-['Inter:Medium',_sans-serif] font-medium leading-[0] not-italic opacity-[0.85] relative shrink-0 text-[#27ae60] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">0%</p>
      </div>
    </div>
  );
}

function Frame1618873496() {
  return (
    <div className="box-border content-stretch flex flex-row gap-1.5 h-[21px] items-center justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Semi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#27ae60] text-[22px] text-left text-nowrap">
        <p className="block leading-[21px] whitespace-pre">$0</p>
      </div>
      <Frame83 />
    </div>
  );
}

function Frame1618873521() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#4b4848] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">
          New Actual Spend
        </p>
      </div>
      <Frame1618873496 />
    </div>
  );
}

function Frame1618873555() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-[66px] grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0">
      <Frame1618873489 />
      <Frame1618873521 />
    </div>
  );
}

function Frame1618873522() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row grow items-center justify-between min-h-px min-w-px p-0 relative shrink-0">
      <Frame1618873555 />
    </div>
  );
}

function Frame1618873559() {
  return (
    <div className="box-border content-stretch flex flex-row gap-[30px] items-center justify-start p-0 relative shrink-0 w-full">
      <Frame1618873540 />
      <Frame1618873522 />
    </div>
  );
}

function Frame1618873539() {
  return (
    <div className="box-border content-stretch flex flex-col gap-4 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873559 />
    </div>
  );
}

function Frame1618873527() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[18px] items-start justify-center p-0 relative shrink-0 w-full">
      <Frame1618873539 />
    </div>
  );
}

function Frame1618873532() {
  return (
    <div className="basis-0 grow min-h-px min-w-px relative rounded-[10px] shrink-0">
      <div
        aria-hidden="true"
        className="absolute border border-[rgba(172,172,172,0.3)] border-solid inset-0 pointer-events-none rounded-[10px]"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col gap-4 items-start justify-center p-[14px] relative w-full">
          <Frame1618873527 />
        </div>
      </div>
    </div>
  );
}

function ExpenseCard() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-5 items-start justify-start p-0 relative shrink-0 w-full"
      data-name="Expense card"
    >
      <Frame1618873532 />
    </div>
  );
}

function Frame1618873568() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3 items-start justify-start p-0 relative shrink-0 w-full">
      <ExpenseCard />
    </div>
  );
}

function MinHeight() {
  return (
    <div
      className="h-6 shrink-0"
      data-name="min-height"
      style={{ width: "1.04907e-06px" }}
    />
  );
}

function MinWidth() {
  return <div className="h-0 shrink-0 w-6" data-name="min-width" />;
}

function KeyboardArrowDown1() {
  return (
    <div className="relative shrink-0 size-6" data-name="keyboard_arrow_down">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="keyboard_arrow_down">
          <path
            d={svgPaths.p3026c200}
            fill="var(--fill-0, #737373)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Container() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-px min-w-px overflow-clip px-0 py-4 relative shrink-0 w-full"
      data-name="Container"
    >
      <MinHeight />
      <div className="basis-0 font-['Nohemi:Regular',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[14px] text-left text-neutral-500">
        <p className="block leading-[20px]">Category</p>
      </div>
      <MinWidth />
      <KeyboardArrowDown1 />
    </div>
  );
}

function Input() {
  return (
    <div
      className="basis-0 grow min-h-px min-w-px relative rounded shrink-0 w-full"
      data-name="Input"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#d0d0d2] border-solid inset-0 pointer-events-none rounded"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-start px-4 py-0 relative size-full">
          <Container />
        </div>
      </div>
    </div>
  );
}

function Select() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-col grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0 w-full"
      data-name="Select"
    >
      <Input />
    </div>
  );
}

function DatePicker() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-col grow h-12 items-start justify-start min-h-px min-w-px p-0 relative shrink-0"
      data-name="DatePicker"
    >
      <Select />
    </div>
  );
}

function Category() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row gap-4 grow items-start justify-start min-h-px min-w-px order-4 p-0 relative shrink-0"
      data-name="Category"
    >
      <DatePicker />
    </div>
  );
}

function Content1() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-6 min-w-6 overflow-clip px-0 py-3.5 relative shrink-0 w-full"
      data-name="Content"
    >
      <div className="basis-0 font-['Nohemi:Regular',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[14px] text-left text-neutral-500">
        <p className="block leading-[20px]">Expense name</p>
      </div>
    </div>
  );
}

function Input1() {
  return (
    <div
      className="basis-0 grow min-h-px min-w-px relative rounded shrink-0 w-full"
      data-name="Input"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#d0d0d2] border-solid inset-0 pointer-events-none rounded"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-center px-4 py-0 relative size-full">
          <Content1 />
        </div>
      </div>
    </div>
  );
}

function TextField() {
  return (
    <div
      className="box-border content-stretch flex flex-col h-12 items-start justify-start order-3 p-0 relative shrink-0 w-[378px]"
      data-name="TextField"
    >
      <Input1 />
    </div>
  );
}

function Content2() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-6 min-w-6 overflow-clip px-0 py-3.5 relative shrink-0 w-full"
      data-name="Content"
    >
      <div className="basis-0 font-['Nohemi:Regular',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[14px] text-left text-neutral-500">
        <p className="block leading-[20px]">Amount</p>
      </div>
    </div>
  );
}

function Input2() {
  return (
    <div
      className="basis-0 grow min-h-px min-w-px relative rounded shrink-0 w-full"
      data-name="Input"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#d0d0d2] border-solid inset-0 pointer-events-none rounded"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-center px-4 py-0 relative size-full">
          <Content2 />
        </div>
      </div>
    </div>
  );
}

function TextField1() {
  return (
    <div
      className="box-border content-stretch flex flex-col h-12 items-start justify-start order-2 p-0 relative shrink-0 w-[166px]"
      data-name="TextField"
    >
      <Input2 />
    </div>
  );
}

function MinHeight1() {
  return (
    <div
      className="h-6 shrink-0"
      data-name="min-height"
      style={{ width: "1.04907e-06px" }}
    />
  );
}

function MinWidth1() {
  return <div className="h-0 shrink-0 w-6" data-name="min-width" />;
}

function KeyboardArrowDown2() {
  return (
    <div className="relative shrink-0 size-6" data-name="keyboard_arrow_down">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="keyboard_arrow_down">
          <path
            d={svgPaths.p3026c200}
            fill="var(--fill-0, #737373)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Container1() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-px min-w-px overflow-clip px-0 py-4 relative shrink-0 w-full"
      data-name="Container"
    >
      <MinHeight1 />
      <div className="basis-0 font-['Nohemi:Regular',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[14px] text-left text-neutral-500">
        <p className="block leading-[20px]">Select frequency</p>
      </div>
      <MinWidth1 />
      <KeyboardArrowDown2 />
    </div>
  );
}

function Input3() {
  return (
    <div className="h-12 relative rounded shrink-0 w-full" data-name="Input">
      <div
        aria-hidden="true"
        className="absolute border border-[#d0d0d2] border-solid inset-0 pointer-events-none rounded"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col h-12 items-start justify-start px-4 py-0 relative w-full">
          <Container1 />
        </div>
      </div>
    </div>
  );
}

function Select1() {
  return (
    <div
      className="box-border content-stretch flex flex-col h-12 items-start justify-start order-1 p-0 relative shrink-0 w-[198px]"
      data-name="Select"
    >
      <Input3 />
    </div>
  );
}

function Col() {
  return (
    <div
      className="box-border content-stretch flex flex-row-reverse gap-4 items-start justify-start p-0 relative shrink-0 w-full"
      data-name="Col"
    >
      <Category />
      <TextField />
      <TextField1 />
      <Select1 />
    </div>
  );
}

function Add() {
  return (
    <div
      className="absolute size-[17px] top-1/2 translate-x-[-50%] translate-y-[-50%]"
      data-name="add"
      style={{ left: "calc(50% - 1.5px)" }}
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 17 17"
      >
        <g id="add">
          <path
            d={svgPaths.p3c2b0b00}
            fill="var(--fill-0, #6130DF)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function MaskedIcon() {
  return (
    <div className="h-[15px] relative shrink-0 w-3" data-name="Masked Icon">
      <Add />
    </div>
  );
}

function Frame1618873538() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-center pb-0 pt-0.5 px-0 relative shrink-0">
      <div className="font-['Nohemi:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#6130df] text-[14px] text-left text-nowrap">
        <p className="block leading-[13px] whitespace-pre">Add Expense</p>
      </div>
    </div>
  );
}

function Base() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-1 items-center justify-center p-0 relative shrink-0"
      data-name="Base"
    >
      <MaskedIcon />
      <Frame1618873538 />
    </div>
  );
}

function Button() {
  return (
    <div
      className="bg-[#efe9ff] box-border content-stretch flex flex-col h-[35px] items-center justify-center overflow-clip px-[19px] py-2.5 relative rounded shrink-0"
      data-name="Button"
    >
      <Base />
    </div>
  );
}

function Frame1618873617() {
  return (
    <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start p-0 relative shrink-0 w-full">
      <Button />
    </div>
  );
}

function Frame1618873619() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3.5 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873568 />
      <Col />
      <Frame1618873617 />
    </div>
  );
}

function Frame1618873525() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-col gap-px grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
      <div className="font-['Nohemi:Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#4b4848] text-[17px] text-left w-full">
        <p className="block leading-[normal]">{`Medical & Insurance`}</p>
      </div>
    </div>
  );
}

function Frame1618873528() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-3 grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0">
      <div
        className="bg-no-repeat bg-size-[133.65%_119.96%] bg-top-left h-[34.444px] shrink-0 w-8"
        data-name="Screenshot 2025-08-07 at 6.22.49 PM 2"
        style={{ backgroundImage: `url('${imgScreenshot20250807At62249Pm2}')` }}
      />
      <Frame1618873525 />
    </div>
  );
}

function Frame1618873541() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-start justify-start p-0 relative shrink-0 w-[236px]">
      <Frame1618873528 />
    </div>
  );
}

function Frame84() {
  return (
    <div className="bg-[#ecf8ea] box-border content-stretch flex flex-row gap-2.5 items-start justify-start pb-px pt-0 px-1 relative rounded shrink-0">
      <div className="font-['Inter:Medium',_sans-serif] font-medium leading-[0] not-italic opacity-[0.85] relative shrink-0 text-[#27ae60] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">55%</p>
      </div>
    </div>
  );
}

function Frame1618873497() {
  return (
    <div className="box-border content-stretch flex flex-row gap-1.5 h-[21px] items-center justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Semi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#27ae60] text-[22px] text-left text-nowrap">
        <p className="block leading-[21px] whitespace-pre">$500</p>
      </div>
      <Frame84 />
    </div>
  );
}

function Frame1618873490() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#4b4848] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">
          Spending Plan Goal
        </p>
      </div>
      <Frame1618873497 />
    </div>
  );
}

function Frame85() {
  return (
    <div className="bg-[#ecf8ea] box-border content-stretch flex flex-row gap-2.5 items-start justify-start pb-px pt-0 px-1 relative rounded shrink-0">
      <div className="font-['Inter:Medium',_sans-serif] font-medium leading-[0] not-italic opacity-[0.85] relative shrink-0 text-[#27ae60] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">0%</p>
      </div>
    </div>
  );
}

function Frame1618873498() {
  return (
    <div className="box-border content-stretch flex flex-row gap-1.5 h-[21px] items-center justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Semi_Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#27ae60] text-[22px] text-left text-nowrap">
        <p className="block leading-[21px] whitespace-pre">$0</p>
      </div>
      <Frame85 />
    </div>
  );
}

function Frame1618873523() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Regular',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#4b4848] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">
          New Actual Spend
        </p>
      </div>
      <Frame1618873498 />
    </div>
  );
}

function Frame1618873556() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-[66px] grow items-center justify-start min-h-px min-w-px p-0 relative shrink-0">
      <Frame1618873490 />
      <Frame1618873523 />
    </div>
  );
}

function Frame1618873529() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row grow items-center justify-between min-h-px min-w-px p-0 relative shrink-0">
      <Frame1618873556 />
    </div>
  );
}

function Frame1618873560() {
  return (
    <div className="box-border content-stretch flex flex-row gap-[30px] items-center justify-start p-0 relative shrink-0 w-full">
      <Frame1618873541 />
      <Frame1618873529 />
    </div>
  );
}

function Frame1618873542() {
  return (
    <div className="box-border content-stretch flex flex-col gap-4 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873560 />
    </div>
  );
}

function Frame1618873530() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[18px] items-start justify-center p-0 relative shrink-0 w-full">
      <Frame1618873542 />
    </div>
  );
}

function Frame1618873533() {
  return (
    <div className="basis-0 grow min-h-px min-w-px relative rounded-[10px] shrink-0">
      <div
        aria-hidden="true"
        className="absolute border border-[rgba(172,172,172,0.3)] border-solid inset-0 pointer-events-none rounded-[10px]"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col gap-4 items-start justify-center p-[14px] relative w-full">
          <Frame1618873530 />
        </div>
      </div>
    </div>
  );
}

function ExpenseCard1() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-5 items-start justify-start p-0 relative shrink-0 w-full"
      data-name="Expense card"
    >
      <Frame1618873533 />
    </div>
  );
}

function Frame1618873569() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3 items-start justify-start p-0 relative shrink-0 w-full">
      <ExpenseCard1 />
    </div>
  );
}

function MinHeight2() {
  return (
    <div
      className="h-6 shrink-0"
      data-name="min-height"
      style={{ width: "1.04907e-06px" }}
    />
  );
}

function MinWidth2() {
  return <div className="h-0 shrink-0 w-6" data-name="min-width" />;
}

function KeyboardArrowDown3() {
  return (
    <div className="relative shrink-0 size-6" data-name="keyboard_arrow_down">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="keyboard_arrow_down">
          <path
            d={svgPaths.p3026c200}
            fill="var(--fill-0, #737373)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Container2() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-px min-w-px overflow-clip px-0 py-4 relative shrink-0 w-full"
      data-name="Container"
    >
      <MinHeight2 />
      <div className="basis-0 font-['Nohemi:Regular',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[14px] text-left text-neutral-500">
        <p className="block leading-[20px]">Category</p>
      </div>
      <MinWidth2 />
      <KeyboardArrowDown3 />
    </div>
  );
}

function Input4() {
  return (
    <div
      className="basis-0 grow min-h-px min-w-px relative rounded shrink-0 w-full"
      data-name="Input"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#d0d0d2] border-solid inset-0 pointer-events-none rounded"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-start px-4 py-0 relative size-full">
          <Container2 />
        </div>
      </div>
    </div>
  );
}

function Select2() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-col grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0 w-full"
      data-name="Select"
    >
      <Input4 />
    </div>
  );
}

function DatePicker1() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-col grow h-12 items-start justify-start min-h-px min-w-px p-0 relative shrink-0"
      data-name="DatePicker"
    >
      <Select2 />
    </div>
  );
}

function Category1() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row gap-4 grow items-start justify-start min-h-px min-w-px order-4 p-0 relative shrink-0"
      data-name="Category"
    >
      <DatePicker1 />
    </div>
  );
}

function Content3() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-6 min-w-6 overflow-clip px-0 py-3.5 relative shrink-0 w-full"
      data-name="Content"
    >
      <div className="basis-0 font-['Nohemi:Regular',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[14px] text-left text-neutral-500">
        <p className="block leading-[20px]">Expense name</p>
      </div>
    </div>
  );
}

function Input5() {
  return (
    <div
      className="basis-0 grow min-h-px min-w-px relative rounded shrink-0 w-full"
      data-name="Input"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#d0d0d2] border-solid inset-0 pointer-events-none rounded"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-center px-4 py-0 relative size-full">
          <Content3 />
        </div>
      </div>
    </div>
  );
}

function TextField2() {
  return (
    <div
      className="box-border content-stretch flex flex-col h-12 items-start justify-start order-3 p-0 relative shrink-0 w-[378px]"
      data-name="TextField"
    >
      <Input5 />
    </div>
  );
}

function Content4() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-6 min-w-6 overflow-clip px-0 py-3.5 relative shrink-0 w-full"
      data-name="Content"
    >
      <div className="basis-0 font-['Nohemi:Regular',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[14px] text-left text-neutral-500">
        <p className="block leading-[20px]">Amount</p>
      </div>
    </div>
  );
}

function Input6() {
  return (
    <div
      className="basis-0 grow min-h-px min-w-px relative rounded shrink-0 w-full"
      data-name="Input"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#d0d0d2] border-solid inset-0 pointer-events-none rounded"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-center px-4 py-0 relative size-full">
          <Content4 />
        </div>
      </div>
    </div>
  );
}

function TextField3() {
  return (
    <div
      className="box-border content-stretch flex flex-col h-12 items-start justify-start order-2 p-0 relative shrink-0 w-[166px]"
      data-name="TextField"
    >
      <Input6 />
    </div>
  );
}

function MinHeight3() {
  return (
    <div
      className="h-6 shrink-0"
      data-name="min-height"
      style={{ width: "1.04907e-06px" }}
    />
  );
}

function MinWidth3() {
  return <div className="h-0 shrink-0 w-6" data-name="min-width" />;
}

function KeyboardArrowDown4() {
  return (
    <div className="relative shrink-0 size-6" data-name="keyboard_arrow_down">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="keyboard_arrow_down">
          <path
            d={svgPaths.p3026c200}
            fill="var(--fill-0, #737373)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Container3() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-px min-w-px overflow-clip px-0 py-4 relative shrink-0 w-full"
      data-name="Container"
    >
      <MinHeight3 />
      <div className="basis-0 font-['Nohemi:Regular',_sans-serif] grow leading-[0] min-h-px min-w-px not-italic relative shrink-0 text-[14px] text-left text-neutral-500">
        <p className="block leading-[20px]">Select frequency</p>
      </div>
      <MinWidth3 />
      <KeyboardArrowDown4 />
    </div>
  );
}

function Input7() {
  return (
    <div className="h-12 relative rounded shrink-0 w-full" data-name="Input">
      <div
        aria-hidden="true"
        className="absolute border border-[#d0d0d2] border-solid inset-0 pointer-events-none rounded"
      />
      <div className="relative size-full">
        <div className="box-border content-stretch flex flex-col h-12 items-start justify-start px-4 py-0 relative w-full">
          <Container3 />
        </div>
      </div>
    </div>
  );
}

function Select3() {
  return (
    <div
      className="box-border content-stretch flex flex-col h-12 items-start justify-start order-1 p-0 relative shrink-0 w-[198px]"
      data-name="Select"
    >
      <Input7 />
    </div>
  );
}

function Col1() {
  return (
    <div
      className="box-border content-stretch flex flex-row-reverse gap-4 items-start justify-start p-0 relative shrink-0 w-full"
      data-name="Col"
    >
      <Category1 />
      <TextField2 />
      <TextField3 />
      <Select3 />
    </div>
  );
}

function Add1() {
  return (
    <div
      className="absolute size-[17px] top-1/2 translate-x-[-50%] translate-y-[-50%]"
      data-name="add"
      style={{ left: "calc(50% - 1.5px)" }}
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 17 17"
      >
        <g id="add">
          <path
            d={svgPaths.p3c2b0b00}
            fill="var(--fill-0, #6130DF)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function MaskedIcon1() {
  return (
    <div className="h-[15px] relative shrink-0 w-3" data-name="Masked Icon">
      <Add1 />
    </div>
  );
}

function Frame1618873543() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-center pb-0 pt-0.5 px-0 relative shrink-0">
      <div className="font-['Nohemi:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#6130df] text-[14px] text-left text-nowrap">
        <p className="block leading-[13px] whitespace-pre">Add Expense</p>
      </div>
    </div>
  );
}

function Base1() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-1 items-center justify-center p-0 relative shrink-0"
      data-name="Base"
    >
      <MaskedIcon1 />
      <Frame1618873543 />
    </div>
  );
}

function Button1() {
  return (
    <div
      className="bg-[#efe9ff] box-border content-stretch flex flex-col h-[35px] items-center justify-center overflow-clip px-[19px] py-2.5 relative rounded shrink-0"
      data-name="Button"
    >
      <Base1 />
    </div>
  );
}

function Frame1618873621() {
  return (
    <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start p-0 relative shrink-0 w-full">
      <Button1 />
    </div>
  );
}

function Frame1618873620() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3.5 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873569 />
      <Col1 />
      <Frame1618873621 />
    </div>
  );
}

function Frame1618873628() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[18px] items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873619 />
      <div className="h-0 relative shrink-0 w-full">
        <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 1100 1"
          >
            <line
              id="Line 186"
              stroke="var(--stroke-0, #ECECEC)"
              x2="1100"
              y1="0.5"
              y2="0.5"
            />
          </svg>
        </div>
      </div>
      <Frame1618873620 />
    </div>
  );
}

function Frame1618873616() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[18px] items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873618 />
      <Frame1618873628 />
    </div>
  );
}

function Base2() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-2 items-center justify-center p-0 relative shrink-0"
      data-name="Base"
    >
      <div className="font-['Nohemi:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#232323] text-[16px] text-left text-nowrap">
        <p className="block leading-[24px] whitespace-pre">Back</p>
      </div>
    </div>
  );
}

function Button2() {
  return (
    <div className="relative rounded shrink-0" data-name="<Button>">
      <div className="box-border content-stretch flex flex-col items-center justify-center overflow-clip px-5 py-3 relative">
        <Base2 />
      </div>
      <div
        aria-hidden="true"
        className="absolute border border-[#232323] border-solid inset-0 pointer-events-none rounded"
      />
    </div>
  );
}

function PrimaryBtn() {
  return (
    <div
      className="bg-[#6130df] box-border content-stretch flex flex-row h-[50px] items-center justify-center px-[19px] py-2 relative rounded shrink-0"
      data-name="Primary BTN"
    >
      <div className="font-['Nohemi:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#ffffff] text-[16px] text-left text-nowrap">
        <p className="block leading-[24px] whitespace-pre">Next</p>
      </div>
    </div>
  );
}

function Frame427319529() {
  return (
    <div className="box-border content-stretch flex flex-row items-center justify-between p-0 relative shrink-0 w-full">
      <Button2 />
      <PrimaryBtn />
    </div>
  );
}

function Frame1618873402() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[38px] items-start justify-start overflow-x-clip overflow-y-auto p-0 relative shrink-0 w-full">
      <Frame1618873616 />
      <Frame427319529 />
    </div>
  );
}

function Frame1618873613() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-col gap-2.5 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0 w-full">
      <Frame1618873402 />
    </div>
  );
}

function Frame1618873476() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-col gap-[19px] grow h-full items-center justify-start min-h-px min-w-px p-0 relative shrink-0">
      <Frame1618873613 />
    </div>
  );
}

function Frame1618873475() {
  return (
    <div className="absolute box-border content-stretch flex flex-row gap-[19px] h-[835px] items-start justify-start left-[50px] overflow-clip pb-0 pt-[30px] px-0 top-[65px] w-[1340px]">
      <PillerVerticalTab />
      <div
        className="flex h-full items-center justify-center relative shrink-0"
        style={
          {
            "--transform-inner-width": "805",
            "--transform-inner-height": "805",
            width:
              "calc(1px * ((var(--transform-inner-height) * 1) + (var(--transform-inner-width) * 0)))",
          } as React.CSSProperties
        }
      >
        <div className="flex-none h-full rotate-[90deg]">
          <div className="h-full relative w-[805px]">
            <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
              <svg
                className="block size-full"
                fill="none"
                preserveAspectRatio="none"
                viewBox="0 0 805 1"
              >
                <line
                  id="Line 206"
                  stroke="var(--stroke-0, #E0E0E0)"
                  x2="805"
                  y1="0.5"
                  y2="0.5"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
      <Frame1618873476 />
    </div>
  );
}

export default function OptimiseYourIncome() {
  return (
    <div
      className="bg-[#ffffff] relative size-full"
      data-name="Optimise your Income"
    >
      <div className="absolute bottom-0 left-0 pointer-events-none top-0">
        <HeaderAdmin1 />
      </div>
      <div className="absolute font-['Inter:Bold',_sans-serif] font-bold leading-[0] left-[313px] not-italic text-[#ffffff] text-[20px] text-left top-[152px] w-[328px]">
        <p className="block leading-[26px]">Manage Cashflow and Budgeting</p>
      </div>
      <Frame1618873475 />
    </div>
  );
}