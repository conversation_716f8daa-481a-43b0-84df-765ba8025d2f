function Content() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-[5px] items-start justify-center leading-[0] not-italic p-0 relative shrink-0 text-left w-full"
      data-name="Content"
    >
      <div className="flex flex-col font-['Nohemi:Medium',_sans-serif] justify-center relative shrink-0 text-[#6130df] text-[16px] w-full">
        <p className="block leading-[20px]">Budget</p>
      </div>
      <div className="flex flex-col font-['Nohemi:Regular',_sans-serif] justify-center relative shrink-0 text-[#343434] text-[12px] w-full">
        <p className="block leading-[16px]">Spend less without feeling like you’re missing out</p>
      </div>
    </div>
  );
}

function VerticalTabBoxed() {
  return (
    <div
      className="bg-[rgba(97,48,223,0.1)] box-border content-stretch flex flex-col gap-2.5 items-center justify-center px-[18px] py-3.5 relative rounded-[3px] shrink-0 w-[202px]"
      data-name="VerticalTab - boxed"
    >
      <div
        aria-hidden="true"
        className="absolute border-[#6130df] border-[0px_0px_0px_3px] border-solid inset-0 pointer-events-none rounded-[3px]"
      />
      <Content />
    </div>
  );
}

function Content1() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-[5px] items-start justify-center leading-[0] not-italic p-0 relative shrink-0 text-left w-full"
      data-name="Content"
    >
      <div className="flex flex-col font-['Nohemi:Medium',_sans-serif] justify-center relative shrink-0 text-[#777777] text-[16px] w-full">
        <p className="block leading-[20px]">Safety Net</p>
      </div>
      <div className="flex flex-col font-['Nohemi:Regular',_sans-serif] justify-center relative shrink-0 text-[#959595] text-[12px] w-full">
        <p className="block leading-[16px]">Build your financial safety net (because life happens)</p>
      </div>
    </div>
  );
}

function VerticalTabBoxed1() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-2.5 items-center justify-center px-[18px] py-3.5 relative shrink-0 w-[202px]"
      data-name="VerticalTab - boxed"
    >
      <Content1 />
    </div>
  );
}

function Content2() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-[5px] items-start justify-center leading-[0] not-italic p-0 relative shrink-0 text-left w-full"
      data-name="Content"
    >
      <div className="flex flex-col font-['Nohemi:Medium',_sans-serif] justify-center relative shrink-0 text-[#777777] text-[16px] w-full">
        <p className="block leading-[20px]">Debt</p>
      </div>
      <div className="flex flex-col font-['Nohemi:Regular',_sans-serif] justify-center relative shrink-0 text-[#959595] text-[12px] w-full">
        <p className="block leading-[16px]">Crush your debt for good</p>
      </div>
    </div>
  );
}

function VerticalTabBoxed2() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-2.5 items-center justify-center px-[18px] py-3.5 relative shrink-0 w-[202px]"
      data-name="VerticalTab - boxed"
    >
      <Content2 />
    </div>
  );
}

function Content3() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-[5px] items-start justify-center leading-[0] not-italic p-0 relative shrink-0 text-left w-full"
      data-name="Content"
    >
      <div className="flex flex-col font-['Nohemi:Medium',_sans-serif] justify-center relative shrink-0 text-[#777777] text-[16px] w-full">
        <p className="block leading-[20px]">{`Save `}</p>
      </div>
      <div className="flex flex-col font-['Nohemi:Regular',_sans-serif] justify-center relative shrink-0 text-[#959595] text-[12px] w-full">
        <p className="block leading-[16px]">Save more without even thinking about it</p>
      </div>
    </div>
  );
}

function VerticalTabBoxed3() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-2.5 items-center justify-center px-[18px] py-3.5 relative shrink-0 w-[202px]"
      data-name="VerticalTab - boxed"
    >
      <Content3 />
    </div>
  );
}

export default function PillerVerticalTab() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative rounded-md size-full"
      data-name="Piller - VerticalTab"
    >
      <VerticalTabBoxed />
      <VerticalTabBoxed1 />
      <VerticalTabBoxed2 />
      <VerticalTabBoxed3 />
    </div>
  );
}