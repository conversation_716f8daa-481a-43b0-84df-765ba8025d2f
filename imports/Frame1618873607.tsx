import svgPaths from "./svg-ckccvkfnjv";
import imgScreenshot20250812At51853Pm2 from "figma:asset/330b77e3437adbea21a94568af717ec309be5a98.png";

function Group39522() {
  return (
    <div className="[grid-area:1_/_1] h-[16.25px] ml-[6.875px] mt-[6.875px] relative w-[16.249px]">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 17 17">
        <g id="Group 39522">
          <path d={svgPaths.p23992100} fill="var(--fill-0, #FF4B00)" id="Ellipse 208" opacity="0.3" />
          <path d={svgPaths.p1489a800} fill="var(--fill-0, #FF4B00)" id="Ellipse 209" />
        </g>
      </svg>
    </div>
  );
}

function Group39586() {
  return (
    <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid place-items-start relative shrink-0">
      <div className="[grid-area:1_/_1] bg-[#ff4b00] ml-0 mt-0 opacity-10 rounded-[8.75px] size-[30px]" />
      <Group39522 />
    </div>
  );
}

function Frame1618873442() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3.5 items-center justify-start leading-[0] p-0 relative shrink-0">
      <Group39586 />
      <div className="font-['Nohemi:Medium',_sans-serif] not-italic relative shrink-0 text-[#ff4b00] text-[18px] text-center text-nowrap">
        <p className="block leading-[22px] whitespace-pre">Optimise</p>
      </div>
    </div>
  );
}

function Frame1618873447() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3.5 h-11 items-center justify-start p-0 relative shrink-0 w-[220px]">
      <Frame1618873442 />
      <div className="h-0 relative shrink-0 w-full">
        <div className="absolute bottom-0 left-0 right-0 top-[-4px]">
          <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 220 4">
            <line id="Line 137" stroke="var(--stroke-0, #FF4B00)" strokeWidth="4" x2="220" y1="2" y2="2" />
          </svg>
        </div>
      </div>
    </div>
  );
}

function Group39523() {
  return (
    <div className="[grid-area:1_/_1] h-[16.25px] ml-[6.875px] mt-[6.875px] relative w-[16.249px]">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 17 17">
        <g id="Group 39522">
          <path d={svgPaths.p23992100} fill="var(--fill-0, #959595)" id="Ellipse 208" opacity="0.3" />
          <path d={svgPaths.p1489a800} fill="var(--fill-0, #959595)" id="Ellipse 209" />
        </g>
      </svg>
    </div>
  );
}

function Group39587() {
  return (
    <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid place-items-start relative shrink-0">
      <div className="[grid-area:1_/_1] bg-[#959595] ml-0 mt-0 opacity-10 rounded-[8.75px] size-[30px]" />
      <Group39523 />
    </div>
  );
}

function Frame1618873443() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3.5 items-center justify-start leading-[0] p-0 relative shrink-0">
      <Group39587 />
      <div className="font-['Nohemi:Medium',_sans-serif] not-italic relative shrink-0 text-[#959595] text-[18px] text-center text-nowrap">
        <p className="block leading-[22px] whitespace-pre">Maximise</p>
      </div>
    </div>
  );
}

function Frame1618873448() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3.5 items-center justify-start p-0 relative shrink-0 w-[220px]">
      <Frame1618873443 />
      <div className="h-0 relative shrink-0 w-full">
        <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 32 32">
          <line id="Line 137" opacity="0" stroke="var(--stroke-0, #6130DF)" strokeWidth="4" x2="220" y1="-2" y2="-2" />
        </svg>
      </div>
    </div>
  );
}

function Group39524() {
  return (
    <div className="[grid-area:1_/_1] h-[16.25px] ml-[6.875px] mt-[6.875px] relative w-[16.249px]">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 17 17">
        <g id="Group 39522">
          <path d={svgPaths.p23992100} fill="var(--fill-0, #959595)" id="Ellipse 208" opacity="0.3" />
          <path d={svgPaths.p1489a800} fill="var(--fill-0, #959595)" id="Ellipse 209" />
        </g>
      </svg>
    </div>
  );
}

function Group39588() {
  return (
    <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid place-items-start relative shrink-0">
      <div className="[grid-area:1_/_1] bg-[#959595] ml-0 mt-0 opacity-10 rounded-[8.75px] size-[30px]" />
      <Group39524 />
    </div>
  );
}

function Frame1618873444() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3.5 items-center justify-start leading-[0] p-0 relative shrink-0">
      <Group39588 />
      <div className="font-['Nohemi:Medium',_sans-serif] not-italic relative shrink-0 text-[#959595] text-[18px] text-center text-nowrap">
        <p className="block leading-[22px] whitespace-pre">Protect</p>
      </div>
    </div>
  );
}

function Frame1618873449() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3.5 items-center justify-start p-0 relative shrink-0 w-[220px]">
      <Frame1618873444 />
      <div className="h-0 relative shrink-0 w-full">
        <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 32 32">
          <line id="Line 137" opacity="0" stroke="var(--stroke-0, #100935)" strokeWidth="4" x2="220" y1="-2" y2="-2" />
        </svg>
      </div>
    </div>
  );
}

function Component6() {
  return (
    <div
      className="box-border content-stretch flex flex-row items-center justify-start p-0 relative shrink-0"
      data-name="Component 6"
    >
      <Frame1618873447 />
      <Frame1618873448 />
      <Frame1618873449 />
    </div>
  );
}

function PillerTab() {
  return (
    <div
      className="box-border content-stretch flex flex-row items-center justify-start p-0 relative shrink-0"
      data-name="Piller tab"
    >
      <Component6 />
    </div>
  );
}

function Frame1618873450() {
  return (
    <div className="box-border content-stretch flex flex-row items-center justify-start p-0 relative shrink-0">
      <PillerTab />
    </div>
  );
}

function BxCheckSvg() {
  return (
    <div className="relative shrink-0 size-3" data-name="bx-check.svg">
      <svg className="block size-full" fill="none" preserveAspectRatio="none" viewBox="0 0 12 12">
        <g id="bx-check.svg">
          <path d={svgPaths.p23738880} fill="var(--fill-0, white)" id="Vector" />
        </g>
      </svg>
    </div>
  );
}

function StepSuccess() {
  return (
    <div
      className="bg-[#6130df] box-border content-stretch flex flex-col gap-[5px] items-center justify-center p-0 relative rounded-[10px] shrink-0 size-5"
      data-name="Step Success"
    >
      <BxCheckSvg />
    </div>
  );
}

function Frame1618873541() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-start p-0 relative shrink-0 w-full">
      <StepSuccess />
      <div className="basis-0 bg-[#cfd6dc] grow h-px min-h-px min-w-px shrink-0" data-name="Rectangle" />
    </div>
  );
}

function Content() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-4 items-start justify-start p-0 relative shrink-0 w-full"
      data-name="Content"
    >
      <div className="flex flex-col font-['Nohemi:Medium',_sans-serif] justify-center leading-[0] not-italic relative shrink-0 text-[#0d0b26] text-[14px] text-left w-full">
        <p className="block leading-[20px]">Budget</p>
      </div>
    </div>
  );
}

function StepperHorizontal() {
  return (
    <div className="basis-0 grow min-h-px min-w-px relative shrink-0" data-name="Stepper - Horizontal">
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-center pb-2 pl-0 pr-2.5 pt-0 relative w-full">
          <Frame1618873541 />
          <Content />
        </div>
      </div>
    </div>
  );
}

function StepShape() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-[5px] items-center justify-center p-0 relative rounded-[10px] shrink-0 size-5"
      data-name="Step Shape"
    >
      <div
        aria-hidden="true"
        className="absolute border-[#6130df] border-[0.5px] border-solid inset-0 pointer-events-none rounded-[10px]"
      />
      <div className="bg-[#6130df] rounded-[5px] shrink-0 size-2.5" />
    </div>
  );
}

function Frame1618873542() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-start p-0 relative shrink-0 w-full">
      <StepShape />
      <div className="basis-0 bg-[#cfd6dc] grow h-px min-h-px min-w-px shrink-0" data-name="Rectangle" />
    </div>
  );
}

function Content1() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-4 items-start justify-start p-0 relative shrink-0 w-full"
      data-name="Content"
    >
      <div className="flex flex-col font-['Nohemi:Medium',_sans-serif] justify-center leading-[0] not-italic relative shrink-0 text-[#6130df] text-[14px] text-left w-full">
        <p className="block leading-[20px]">Safety Net</p>
      </div>
    </div>
  );
}

function StepperHorizontal1() {
  return (
    <div className="basis-0 grow min-h-px min-w-px relative shrink-0" data-name="Stepper - Horizontal">
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-center pb-2 pl-0 pr-2.5 pt-0 relative w-full">
          <Frame1618873542 />
          <Content1 />
        </div>
      </div>
    </div>
  );
}

function StepNumber() {
  return (
    <div
      className="bg-[rgba(97,48,223,0.1)] box-border content-stretch flex flex-col gap-[5px] items-center justify-center p-0 relative rounded-[10px] shrink-0 size-5"
      data-name="Step Number"
    >
      <div
        className="flex flex-col font-['Roboto:Medium',_sans-serif] font-medium justify-center leading-[0] relative shrink-0 text-[#6130df] text-[9px] text-center text-nowrap"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="block leading-[normal] whitespace-pre">&nbsp;</p>
      </div>
    </div>
  );
}

function Frame1618873543() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-start p-0 relative shrink-0 w-full">
      <StepNumber />
      <div className="basis-0 bg-[#cfd6dc] grow h-px min-h-px min-w-px shrink-0" data-name="Rectangle" />
    </div>
  );
}

function Content2() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-4 items-start justify-start p-0 relative shrink-0 w-full"
      data-name="Content"
    >
      <div className="flex flex-col font-['Nohemi:Medium',_sans-serif] justify-center leading-[0] not-italic relative shrink-0 text-[#abb7c2] text-[14px] text-left w-full">
        <p className="block leading-[20px]">Debt</p>
      </div>
    </div>
  );
}

function StepperHorizontal2() {
  return (
    <div className="basis-0 grow min-h-px min-w-px relative shrink-0" data-name="Stepper - Horizontal">
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-center pb-2 pl-0 pr-2.5 pt-0 relative w-full">
          <Frame1618873543 />
          <Content2 />
        </div>
      </div>
    </div>
  );
}

function StepNumber1() {
  return (
    <div
      className="bg-[rgba(97,48,223,0.1)] box-border content-stretch flex flex-col gap-[5px] items-center justify-center p-0 relative rounded-[10px] shrink-0 size-5"
      data-name="Step Number"
    >
      <div
        className="flex flex-col font-['Roboto:Medium',_sans-serif] font-medium justify-center leading-[0] relative shrink-0 text-[#6130df] text-[9px] text-center text-nowrap"
        style={{ fontVariationSettings: "'wdth' 100" }}
      >
        <p className="block leading-[normal] whitespace-pre">&nbsp;</p>
      </div>
    </div>
  );
}

function Frame1618873544() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-center justify-start p-0 relative shrink-0 w-full">
      <StepNumber1 />
      <div className="basis-0 bg-[#cfd6dc] grow h-px min-h-px min-w-px shrink-0" data-name="Rectangle" />
    </div>
  );
}

function Content3() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-4 items-start justify-start p-0 relative shrink-0 w-full"
      data-name="Content"
    >
      <div className="flex flex-col font-['Nohemi:Medium',_sans-serif] justify-center leading-[0] not-italic relative shrink-0 text-[#abb7c2] text-[14px] text-left w-full">
        <p className="block leading-[20px]">{`Save `}</p>
      </div>
    </div>
  );
}

function StepperHorizontal3() {
  return (
    <div className="basis-0 grow min-h-px min-w-px relative shrink-0" data-name="Stepper - Horizontal">
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-center pb-2 pl-0 pr-2.5 pt-0 relative w-full">
          <Frame1618873544 />
          <Content3 />
        </div>
      </div>
    </div>
  );
}

function Stepper() {
  return (
    <div
      className="box-border content-stretch flex flex-row h-[58px] items-start justify-start p-0 relative rounded-md shrink-0 w-[1100px]"
      data-name="Stepper"
    >
      <StepperHorizontal />
      <StepperHorizontal1 />
      <StepperHorizontal2 />
      <StepperHorizontal3 />
    </div>
  );
}

export default function Frame1618873607() {
  return (
    <div className="bg-[#ffffff] relative size-full">
      <div className="flex flex-col items-center justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col gap-[37px] items-center justify-center px-6 py-[35px] relative size-full">
          <Frame1618873450 />
          <Stepper />
          <div
            className="aspect-[1135/203] bg-bottom-left bg-no-repeat bg-size-[100%_161.58%] shrink-0 w-full"
            data-name="Screenshot 2025-08-12 at 5.18.53 PM 2"
            style={{ backgroundImage: `url('${imgScreenshot20250812At51853Pm2}')` }}
          />
        </div>
      </div>
    </div>
  );
}