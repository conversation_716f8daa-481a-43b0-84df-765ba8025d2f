import svgPaths from "./svg-kviyiv86oi";

function Frame427319527() {
  return (
    <div className="box-border content-stretch flex flex-col gap-2.5 items-start justify-start leading-[0] p-0 relative shrink-0 text-left w-[720px]">
      <div className="font-['Schibsted_Grotesk:Bold',_sans-serif] font-bold relative shrink-0 text-[#6130df] text-[28px] w-full">
        <p className="block leading-[normal]">Optimise Your Monthly Income</p>
      </div>
      <div className="flex flex-col font-['Nohemi:Regular',_sans-serif] justify-center not-italic relative shrink-0 text-[#343434] text-[16px] w-full">
        <p className="block leading-[22px]">{`Here’s how your income should be utilized. Please review the breakdown and click 'Next' to add your transactions and start managing your cash flow and budget.`}</p>
      </div>
    </div>
  );
}

function Edit() {
  return (
    <div className="relative shrink-0 size-[13.143px]" data-name="edit">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 14 14"
      >
        <g id="edit">
          <path
            d={svgPaths.p396d780}
            fill="var(--fill-0, #232323)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Frame1000001191() {
  return (
    <div className="bg-[rgba(255,75,0,0.15)] box-border content-stretch flex flex-row gap-[7.667px] items-center justify-center p-0 relative rounded-[23px] shrink-0 size-[23px]">
      <Edit />
    </div>
  );
}

function Frame1618873499() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-start justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#313131] text-[28px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">$10,000</p>
      </div>
      <Frame1000001191 />
    </div>
  );
}

function Frame1618873500() {
  return (
    <div className="box-border content-stretch flex flex-col gap-2.5 items-end justify-start p-0 relative shrink-0">
      <div className="font-['Nohemi:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#004446] text-[14px] text-left text-nowrap tracking-[-0.14px]">
        <p className="adjustLetterSpacing block leading-[normal] whitespace-pre">
          Net Income per month
        </p>
      </div>
      <Frame1618873499 />
    </div>
  );
}

function Frame1618873553() {
  return (
    <div className="box-border content-stretch flex flex-row items-end justify-between p-0 relative shrink-0 w-full">
      <Frame427319527 />
      <Frame1618873500 />
    </div>
  );
}

function Frame1618873489() {
  return (
    <div className="box-border content-stretch flex flex-row font-['Nohemi:Bold',_sans-serif] items-center justify-between leading-[0] not-italic p-0 relative shrink-0 text-nowrap w-full">
      <div className="relative shrink-0 text-[#4b4848] text-[22px] text-center">
        <p className="block leading-[normal] text-nowrap whitespace-pre">
          Living Expenses
        </p>
      </div>
      <div className="relative shrink-0 text-[#313131] text-[20px] text-left">
        <p className="block leading-[normal] text-nowrap whitespace-pre">
          $7,000
        </p>
      </div>
    </div>
  );
}

function Frame1618873527() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[18px] items-center justify-center p-0 relative shrink-0 w-full">
      <Frame1618873489 />
    </div>
  );
}

function Frame1618873514() {
  return (
    <div className="[grid-area:1_/_1] box-border content-stretch flex flex-col gap-[3px] items-start justify-start ml-0 mt-0 not-italic p-0 relative text-left w-[151px]">
      <div className="font-['Nohemi:Bold',_sans-serif] relative shrink-0 text-[#333333] text-[14px] text-nowrap">
        <p className="block leading-[normal] whitespace-pre">Living Expenses</p>
      </div>
      <div
        className="font-['Inter:Regular',_sans-serif] font-normal min-w-full relative shrink-0 text-[#979797] text-[12px]"
        style={{ width: "min-content" }}
      >
        <p className="block leading-[normal]">$180.82/day</p>
      </div>
    </div>
  );
}

function Group318() {
  return (
    <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid leading-[0] place-items-start relative shrink-0">
      <Frame1618873514 />
    </div>
  );
}

function Frame1618873530() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-3 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
      <Group318 />
    </div>
  );
}

function Frame4() {
  return (
    <div className="bg-[#ffe5db] box-border content-stretch flex flex-row gap-2.5 items-start justify-start pb-px pt-0 px-1 relative rounded shrink-0">
      <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic opacity-[0.85] relative shrink-0 text-[#ff4b00] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">55%</p>
      </div>
    </div>
  );
}

function Frame1618873515() {
  return (
    <div className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0 w-[50px]">
      <div className="font-['Nohemi:Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#333333] text-[14px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">$5,500</p>
      </div>
      <Frame4 />
    </div>
  );
}

function Frame1618873529() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-start justify-start p-0 relative shrink-0">
      <Frame1618873515 />
    </div>
  );
}

function Frame1618873516() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873530 />
      <Frame1618873529 />
    </div>
  );
}

function Frame1618873520() {
  return (
    <div className="[grid-area:1_/_1] box-border content-stretch flex flex-col gap-[3px] items-start justify-start ml-0 mt-0 not-italic p-0 relative text-left w-[151px]">
      <div className="font-['Nohemi:Bold',_sans-serif] relative shrink-0 text-[#333333] text-[14px] text-nowrap">
        <p className="block leading-[normal] whitespace-pre">{`Medical & Insurance`}</p>
      </div>
      <div
        className="font-['Inter:Regular',_sans-serif] font-normal min-w-full relative shrink-0 text-[#979797] text-[12px]"
        style={{ width: "min-content" }}
      >
        <p className="block leading-[normal]">$16.44/day</p>
      </div>
    </div>
  );
}

function Group319() {
  return (
    <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid leading-[0] place-items-start relative shrink-0">
      <Frame1618873520 />
    </div>
  );
}

function Frame1618873531() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-3 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
      <Group319 />
    </div>
  );
}

function Frame5() {
  return (
    <div className="bg-[#ffe5db] box-border content-stretch flex flex-row gap-2.5 items-start justify-start pb-px pt-0 px-1 relative rounded shrink-0">
      <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic opacity-[0.85] relative shrink-0 text-[#ff4b00] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">5%</p>
      </div>
    </div>
  );
}

function Frame1618873521() {
  return (
    <div className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0 w-[50px]">
      <div className="font-['Nohemi:Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#333333] text-[14px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">$5,00</p>
      </div>
      <Frame5 />
    </div>
  );
}

function Frame1618873532() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-start justify-start p-0 relative shrink-0">
      <Frame1618873521 />
    </div>
  );
}

function Frame1618873517() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873531 />
      <Frame1618873532 />
    </div>
  );
}

function Frame1618873522() {
  return (
    <div className="[grid-area:1_/_1] box-border content-stretch flex flex-col gap-[3px] items-start justify-start ml-0 mt-0 not-italic p-0 relative text-left w-[151px]">
      <div className="font-['Nohemi:Bold',_sans-serif] relative shrink-0 text-[#333333] text-[14px] text-nowrap">
        <p className="block leading-[normal] whitespace-pre">{`Fun & Entertainment`}</p>
      </div>
      <div
        className="font-['Inter:Regular',_sans-serif] font-normal min-w-full relative shrink-0 text-[#979797] text-[12px]"
        style={{ width: "min-content" }}
      >
        <p className="block leading-[normal]">$32.88/day</p>
      </div>
    </div>
  );
}

function Group320() {
  return (
    <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid leading-[0] place-items-start relative shrink-0">
      <Frame1618873522 />
    </div>
  );
}

function Frame1618873533() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-3 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
      <Group320 />
    </div>
  );
}

function Frame6() {
  return (
    <div className="bg-[#ffe5db] box-border content-stretch flex flex-row gap-2.5 items-start justify-start pb-px pt-0 px-1 relative rounded shrink-0">
      <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic opacity-[0.85] relative shrink-0 text-[#ff4b00] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">10%</p>
      </div>
    </div>
  );
}

function Frame1618873523() {
  return (
    <div className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0 w-[50px]">
      <div className="font-['Nohemi:Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#333333] text-[14px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">$1,000</p>
      </div>
      <Frame6 />
    </div>
  );
}

function Frame1618873534() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-start justify-start p-0 relative shrink-0">
      <Frame1618873523 />
    </div>
  );
}

function Frame1618873518() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873533 />
      <Frame1618873534 />
    </div>
  );
}

function Frame1618873524() {
  return (
    <div className="[grid-area:1_/_1] box-border content-stretch flex flex-col gap-[3px] items-start justify-start ml-0 mt-0 not-italic p-0 relative text-left w-[151px]">
      <div className="font-['Nohemi:Bold',_sans-serif] relative shrink-0 text-[#333333] text-[14px] text-nowrap">
        <p className="block leading-[normal] whitespace-pre">Debt Repayments</p>
      </div>
      <div
        className="font-['Inter:Regular',_sans-serif] font-normal min-w-full relative shrink-0 text-[#979797] text-[12px]"
        style={{ width: "min-content" }}
      >
        <p className="block leading-[normal]">$0/day</p>
      </div>
    </div>
  );
}

function Group321() {
  return (
    <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid leading-[0] place-items-start relative shrink-0">
      <Frame1618873524 />
    </div>
  );
}

function Frame1618873535() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-3 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
      <Group321 />
    </div>
  );
}

function Frame7() {
  return (
    <div className="bg-[#ffe5db] box-border content-stretch flex flex-row gap-2.5 items-start justify-start pb-px pt-0 px-1 relative rounded shrink-0">
      <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic opacity-[0.85] relative shrink-0 text-[#ff4b00] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">0%</p>
      </div>
    </div>
  );
}

function Frame1618873525() {
  return (
    <div className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0 w-[50px]">
      <div className="font-['Nohemi:Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#333333] text-[14px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">$0</p>
      </div>
      <Frame7 />
    </div>
  );
}

function Frame1618873536() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-start justify-start p-0 relative shrink-0">
      <Frame1618873525 />
    </div>
  );
}

function Frame1618873519() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873535 />
      <Frame1618873536 />
    </div>
  );
}

function Frame1618873526() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3 items-center justify-start p-0 relative shrink-0 w-full">
      <Frame1618873516 />
      <div className="h-0 relative shrink-0 w-full">
        <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 266 1"
          >
            <line
              id="Line 189"
              stroke="var(--stroke-0, #ECECEC)"
              x2="266"
              y1="0.5"
              y2="0.5"
            />
          </svg>
        </div>
      </div>
      <Frame1618873517 />
      <div className="h-0 relative shrink-0 w-full">
        <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 266 1"
          >
            <line
              id="Line 189"
              stroke="var(--stroke-0, #ECECEC)"
              x2="266"
              y1="0.5"
              y2="0.5"
            />
          </svg>
        </div>
      </div>
      <Frame1618873518 />
      <div className="h-0 relative shrink-0 w-full">
        <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 266 1"
          >
            <line
              id="Line 189"
              stroke="var(--stroke-0, #ECECEC)"
              x2="266"
              y1="0.5"
              y2="0.5"
            />
          </svg>
        </div>
      </div>
      <Frame1618873519 />
    </div>
  );
}

function Frame1618873550() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3 items-start justify-start p-0 relative shrink-0 w-[266px]">
      <Frame1618873526 />
    </div>
  );
}

function Frame2() {
  return (
    <div
      className="absolute box-border content-stretch flex flex-row items-center justify-start leading-[0] not-italic p-0 text-left translate-x-[-50%] translate-y-[-50%]"
      style={{ top: "calc(50% + 22.558px)", left: "calc(50% + 2.486px)" }}
    >
      <div className="font-['Nohemi:Medium',_sans-serif] relative shrink-0 text-[#ff4b00] text-[56.422px] text-nowrap">
        <p className="block leading-[normal] whitespace-pre">70</p>
      </div>
      <div className="font-['DM_Sans:Regular',_sans-serif] h-[38.287px] relative shrink-0 text-[33.06px] text-[rgba(255,75,0,0.5)] w-[28.715px]">
        <p className="block leading-[normal]">%</p>
      </div>
    </div>
  );
}

function Group1392() {
  return (
    <div className="h-[170.834px] relative shrink-0 w-[202px]">
      <div className="absolute bottom-[0.23%] left-0 right-[15.62%] top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 171 171"
        >
          <path
            d={svgPaths.p1f273580}
            fill="url(#paint0_linear_38_630)"
            id="Ellipse 11"
          />
          <defs>
            <linearGradient
              gradientUnits="userSpaceOnUse"
              id="paint0_linear_38_630"
              x1="114.954"
              x2="40.2028"
              y1="110.303"
              y2="178.672"
            >
              <stop stopColor="#FF4B00" />
              <stop offset="1" stopColor="#FF4B00" stopOpacity="0" />
            </linearGradient>
          </defs>
        </svg>
      </div>
      <div className="absolute bottom-[0.3%] left-0 right-0 top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 202 171"
        >
          <path
            d={svgPaths.p1d82b080}
            fill="var(--fill-0, #222222)"
            fillOpacity="0.22"
            id="Ellipse 9"
            opacity="0.3"
          />
        </svg>
      </div>
      <div className="absolute inset-[12.84%_11.11%_-5.41%_10.6%]">
        <div className="absolute inset-[-24.39%]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 236 236"
          >
            <g filter="url(#filter0_d_38_628)" id="Ellipse 10">
              <circle
                cx="118.069"
                cy="118.069"
                fill="var(--fill-0, white)"
                fillOpacity="0.01"
                r="79.0685"
                shapeRendering="crispEdges"
              />
            </g>
            <defs>
              <filter
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
                height="235.277"
                id="filter0_d_38_628"
                width="235.277"
                x="0.429981"
                y="0.429981"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feColorMatrix
                  in="SourceAlpha"
                  result="hardAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation="19.285" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix
                  type="matrix"
                  values="0 0 0 0 1 0 0 0 0 0.294118 0 0 0 0 0 0 0 0 0.2 0"
                />
                <feBlend
                  in2="BackgroundImageFix"
                  mode="normal"
                  result="effect1_dropShadow_38_628"
                />
                <feBlend
                  in="SourceGraphic"
                  in2="effect1_dropShadow_38_628"
                  mode="normal"
                  result="shape"
                />
              </filter>
            </defs>
          </svg>
        </div>
      </div>
      <Frame2 />
      <div className="absolute flex inset-[20.63%_18.19%_73.1%_76.51%] items-center justify-center">
        <div className="flex-none h-[8.721px] rotate-[45deg] w-[6.422px]">
          <div className="relative size-full">
            <div className="absolute bottom-0 left-[7.91%] right-[7.91%] top-0">
              <svg
                className="block size-full"
                fill="none"
                preserveAspectRatio="none"
                viewBox="0 0 6 9"
              >
                <path
                  d={svgPaths.p20e1db80}
                  fill="var(--fill-0, #FF4B00)"
                  id="Rectangle 1072"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function Frame1618873554() {
  return (
    <div className="box-border content-stretch flex flex-row items-start justify-between p-0 relative shrink-0 w-full">
      <Frame1618873550 />
      <Group1392 />
    </div>
  );
}

function Frame1618873547() {
  return (
    <div className="relative rounded-tl-[10px] rounded-tr-[10px] shrink-0 w-full">
      <div className="flex flex-col items-center justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col gap-4 items-center justify-center px-3.5 py-[18px] relative w-full">
          <Frame1618873527 />
          <div className="h-0 relative shrink-0 w-full">
            <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
              <svg
                className="block size-full"
                fill="none"
                preserveAspectRatio="none"
                viewBox="0 0 512 1"
              >
                <line
                  id="Line 189"
                  stroke="var(--stroke-0, #CACACA)"
                  x2="512"
                  y1="0.5"
                  y2="0.5"
                />
              </svg>
            </div>
          </div>
          <Frame1618873554 />
        </div>
      </div>
    </div>
  );
}

function Frame1618873549() {
  return (
    <div className="basis-0 grow min-h-px min-w-px relative rounded-[10px] shrink-0">
      <div className="box-border content-stretch flex flex-col items-start justify-start overflow-clip p-0 relative w-full">
        <Frame1618873547 />
      </div>
      <div
        aria-hidden="true"
        className="absolute border border-[rgba(144,144,144,0.3)] border-solid inset-0 pointer-events-none rounded-[10px]"
      />
    </div>
  );
}

function Frame1618873490() {
  return (
    <div className="box-border content-stretch flex flex-row font-['Nohemi:Bold',_sans-serif] items-center justify-between leading-[0] not-italic p-0 relative shrink-0 text-nowrap w-full">
      <div className="relative shrink-0 text-[#4b4848] text-[22px] text-center">
        <p className="block leading-[normal] text-nowrap whitespace-pre">{`Savings & Investment`}</p>
      </div>
      <div className="relative shrink-0 text-[#313131] text-[20px] text-left">
        <p className="block leading-[normal] text-nowrap whitespace-pre">
          $3,000
        </p>
      </div>
    </div>
  );
}

function Frame1618873528() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[18px] items-center justify-center p-0 relative shrink-0 w-full">
      <Frame1618873490 />
    </div>
  );
}

function Frame1618873537() {
  return (
    <div className="[grid-area:1_/_1] box-border content-stretch flex flex-col gap-[3px] items-start justify-start ml-0 mt-0 not-italic p-0 relative text-left w-[151px]">
      <div className="font-['Nohemi:Bold',_sans-serif] relative shrink-0 text-[#333333] text-[14px] text-nowrap">
        <p className="block leading-[normal] whitespace-pre">Savings</p>
      </div>
      <div
        className="font-['Inter:Regular',_sans-serif] font-normal min-w-full relative shrink-0 text-[#979797] text-[12px]"
        style={{ width: "min-content" }}
      >
        <p className="block leading-[normal]">$65.75/day</p>
      </div>
    </div>
  );
}

function Group322() {
  return (
    <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid leading-[0] place-items-start relative shrink-0">
      <Frame1618873537 />
    </div>
  );
}

function Frame1618873538() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-3 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
      <Group322 />
    </div>
  );
}

function Frame8() {
  return (
    <div className="bg-[rgba(39,174,96,0.1)] box-border content-stretch flex flex-row gap-2.5 items-start justify-start pb-px pt-0 px-1 relative rounded shrink-0">
      <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic opacity-[0.85] relative shrink-0 text-[#1aa454] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">20%</p>
      </div>
    </div>
  );
}

function Frame1618873539() {
  return (
    <div className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0 w-[50px]">
      <div className="font-['Nohemi:Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#333333] text-[14px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">$2,000</p>
      </div>
      <Frame8 />
    </div>
  );
}

function Frame1618873540() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-start justify-start p-0 relative shrink-0">
      <Frame1618873539 />
    </div>
  );
}

function Frame1618873541() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873538 />
      <Frame1618873540 />
    </div>
  );
}

function Frame1618873542() {
  return (
    <div className="[grid-area:1_/_1] box-border content-stretch flex flex-col gap-[3px] items-start justify-start ml-0 mt-0 not-italic p-0 relative text-left w-[151px]">
      <div className="font-['Nohemi:Bold',_sans-serif] relative shrink-0 text-[#333333] text-[14px] text-nowrap">
        <p className="block leading-[normal] whitespace-pre">Investment</p>
      </div>
      <div
        className="font-['Inter:Regular',_sans-serif] font-normal min-w-full relative shrink-0 text-[#979797] text-[12px]"
        style={{ width: "min-content" }}
      >
        <p className="block leading-[normal]">$32.88/day</p>
      </div>
    </div>
  );
}

function Group323() {
  return (
    <div className="grid-cols-[max-content] grid-rows-[max-content] inline-grid leading-[0] place-items-start relative shrink-0">
      <Frame1618873542 />
    </div>
  );
}

function Frame1618873543() {
  return (
    <div className="basis-0 box-border content-stretch flex flex-row gap-3 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0">
      <Group323 />
    </div>
  );
}

function Frame9() {
  return (
    <div className="bg-[rgba(39,174,96,0.1)] box-border content-stretch flex flex-row gap-2.5 items-start justify-start pb-px pt-0 px-1 relative rounded shrink-0">
      <div className="font-['Inter:Semi_Bold',_sans-serif] font-semibold leading-[0] not-italic opacity-[0.85] relative shrink-0 text-[#1aa454] text-[12px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">10%</p>
      </div>
    </div>
  );
}

function Frame1618873544() {
  return (
    <div className="box-border content-stretch flex flex-col gap-1 items-start justify-start p-0 relative shrink-0 w-[50px]">
      <div className="font-['Nohemi:Bold',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#333333] text-[14px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">$1,000</p>
      </div>
      <Frame9 />
    </div>
  );
}

function Frame1618873545() {
  return (
    <div className="box-border content-stretch flex flex-row gap-2.5 items-start justify-start p-0 relative shrink-0">
      <Frame1618873544 />
    </div>
  );
}

function Frame1618873546() {
  return (
    <div className="box-border content-stretch flex flex-row gap-3 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873543 />
      <Frame1618873545 />
    </div>
  );
}

function Frame1618873548() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3 items-center justify-start p-0 relative shrink-0 w-full">
      <Frame1618873541 />
      <div className="h-0 relative shrink-0 w-full">
        <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 266 1"
          >
            <line
              id="Line 189"
              stroke="var(--stroke-0, #ECECEC)"
              x2="266"
              y1="0.5"
              y2="0.5"
            />
          </svg>
        </div>
      </div>
      <Frame1618873546 />
    </div>
  );
}

function Frame1618873552() {
  return (
    <div className="box-border content-stretch flex flex-col gap-3 items-start justify-start p-0 relative shrink-0 w-[266px]">
      <Frame1618873548 />
    </div>
  );
}

function Frame3() {
  return (
    <div
      className="absolute box-border content-stretch flex flex-row items-center justify-start leading-[0] not-italic p-0 text-left translate-x-[-50%] translate-y-[-50%]"
      style={{ top: "calc(50% + 22.558px)", left: "calc(50% + 2.486px)" }}
    >
      <div className="font-['Nohemi:Medium',_sans-serif] relative shrink-0 text-[#27ae60] text-[56.422px] text-nowrap">
        <p className="block leading-[normal] whitespace-pre">30</p>
      </div>
      <div className="font-['DM_Sans:Regular',_sans-serif] h-[38.287px] relative shrink-0 text-[33.06px] text-[rgba(39,174,96,0.5)] w-[28.715px]">
        <p className="block leading-[normal]">%</p>
      </div>
    </div>
  );
}

function Group1393() {
  return (
    <div className="h-[170.834px] relative shrink-0 w-[202px]">
      <div className="absolute bottom-[0.23%] left-0 right-[15.62%] top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 171 171"
        >
          <path
            d={svgPaths.p1f273580}
            fill="url(#paint0_linear_38_617)"
            id="Ellipse 11"
          />
          <defs>
            <linearGradient
              gradientUnits="userSpaceOnUse"
              id="paint0_linear_38_617"
              x1="114.954"
              x2="40.2028"
              y1="110.303"
              y2="178.672"
            >
              <stop stopColor="#27AE60" />
              <stop offset="1" stopColor="#27AE60" stopOpacity="0" />
            </linearGradient>
          </defs>
        </svg>
      </div>
      <div className="absolute bottom-[0.3%] left-0 right-0 top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 202 171"
        >
          <path
            d={svgPaths.p1d82b080}
            fill="var(--fill-0, #222222)"
            fillOpacity="0.22"
            id="Ellipse 9"
            opacity="0.3"
          />
        </svg>
      </div>
      <div className="absolute inset-[12.84%_11.11%_-5.41%_10.6%]">
        <div className="absolute inset-[-24.39%]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 236 236"
          >
            <g filter="url(#filter0_d_38_624)" id="Ellipse 10">
              <circle
                cx="118.069"
                cy="118.069"
                fill="var(--fill-0, white)"
                fillOpacity="0.01"
                r="79.0685"
                shapeRendering="crispEdges"
              />
            </g>
            <defs>
              <filter
                colorInterpolationFilters="sRGB"
                filterUnits="userSpaceOnUse"
                height="235.277"
                id="filter0_d_38_624"
                width="235.277"
                x="0.429981"
                y="0.429981"
              >
                <feFlood floodOpacity="0" result="BackgroundImageFix" />
                <feColorMatrix
                  in="SourceAlpha"
                  result="hardAlpha"
                  type="matrix"
                  values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                />
                <feOffset />
                <feGaussianBlur stdDeviation="19.285" />
                <feComposite in2="hardAlpha" operator="out" />
                <feColorMatrix
                  type="matrix"
                  values="0 0 0 0 0.152941 0 0 0 0 0.682353 0 0 0 0 0.376471 0 0 0 0.2 0"
                />
                <feBlend
                  in2="BackgroundImageFix"
                  mode="normal"
                  result="effect1_dropShadow_38_624"
                />
                <feBlend
                  in="SourceGraphic"
                  in2="effect1_dropShadow_38_624"
                  mode="normal"
                  result="shape"
                />
              </filter>
            </defs>
          </svg>
        </div>
      </div>
      <Frame3 />
      <div className="absolute flex inset-[20.63%_18.19%_73.1%_76.51%] items-center justify-center">
        <div className="flex-none h-[8.721px] rotate-[45deg] w-[6.422px]">
          <div className="relative size-full">
            <div className="absolute bottom-0 left-[7.91%] right-[7.91%] top-0">
              <svg
                className="block size-full"
                fill="none"
                preserveAspectRatio="none"
                viewBox="0 0 6 9"
              >
                <path
                  d={svgPaths.p20e1db80}
                  fill="var(--fill-0, #27AE60)"
                  id="Rectangle 1072"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function Frame1618873555() {
  return (
    <div className="box-border content-stretch flex flex-row items-start justify-between p-0 relative shrink-0 w-full">
      <Frame1618873552 />
      <Group1393 />
    </div>
  );
}

function Frame1618873556() {
  return (
    <div className="basis-0 grow min-h-px min-w-px relative rounded-tl-[10px] rounded-tr-[10px] shrink-0 w-full">
      <div className="flex flex-col items-center relative size-full">
        <div className="box-border content-stretch flex flex-col gap-4 items-center justify-start px-3.5 py-[18px] relative size-full">
          <Frame1618873528 />
          <div className="h-0 relative shrink-0 w-full">
            <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
              <svg
                className="block size-full"
                fill="none"
                preserveAspectRatio="none"
                viewBox="0 0 512 1"
              >
                <line
                  id="Line 189"
                  stroke="var(--stroke-0, #CACACA)"
                  x2="512"
                  y1="0.5"
                  y2="0.5"
                />
              </svg>
            </div>
          </div>
          <Frame1618873555 />
        </div>
      </div>
    </div>
  );
}

function Frame1618873551() {
  return (
    <div className="basis-0 grow min-h-px min-w-px relative rounded-[10px] self-stretch shrink-0">
      <div className="box-border content-stretch flex flex-col items-start justify-start overflow-clip p-0 relative size-full">
        <Frame1618873556 />
      </div>
      <div
        aria-hidden="true"
        className="absolute border border-[rgba(144,144,144,0.3)] border-solid inset-0 pointer-events-none rounded-[10px]"
      />
    </div>
  );
}

function Frame1618873557() {
  return (
    <div className="box-border content-stretch flex flex-row gap-5 items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873549 />
      <Frame1618873551 />
    </div>
  );
}

function Base() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-2 items-center justify-center p-0 relative shrink-0"
      data-name="Base"
    >
      <div className="font-['Nohemi:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#232323] text-[16px] text-left text-nowrap">
        <p className="block leading-[24px] whitespace-pre">Back</p>
      </div>
    </div>
  );
}

function Button() {
  return (
    <div className="relative rounded shrink-0" data-name="<Button>">
      <div className="box-border content-stretch flex flex-col items-center justify-center overflow-clip px-5 py-3 relative">
        <Base />
      </div>
      <div
        aria-hidden="true"
        className="absolute border border-[#232323] border-solid inset-0 pointer-events-none rounded"
      />
    </div>
  );
}

function PrimaryBtn() {
  return (
    <div
      className="bg-[#6130df] box-border content-stretch flex flex-row h-[50px] items-center justify-center px-[19px] py-2 relative rounded shrink-0"
      data-name="Primary BTN"
    >
      <div className="font-['Nohemi:Medium',_sans-serif] leading-[0] not-italic relative shrink-0 text-[#ffffff] text-[16px] text-left text-nowrap">
        <p className="block leading-[24px] whitespace-pre">Next</p>
      </div>
    </div>
  );
}

function Frame427319529() {
  return (
    <div className="box-border content-stretch flex flex-row gap-4 items-center justify-end p-0 relative shrink-0 w-full">
      <Button />
      <PrimaryBtn />
    </div>
  );
}

export default function Frame1618873402() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[18px] items-start justify-start p-0 relative size-full">
      <Frame1618873553 />
      <Frame1618873557 />
      <div className="h-0 relative shrink-0 w-full">
        <div className="absolute bottom-0 left-0 right-0 top-[-1px]">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 1100 1"
          >
            <line
              id="Line 185"
              stroke="var(--stroke-0, #ECECEC)"
              x2="1100"
              y1="0.5"
              y2="0.5"
            />
          </svg>
        </div>
      </div>
      <Frame427319529 />
    </div>
  );
}