import svgPaths from "./svg-vd034xtudd";
import imgImage1 from "figma:asset/f8ba7cabbe5fbdee0a0e197c7e109f81fe9eea0e.png";

function Group() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 138 120"
      >
        <g id="Group">
          <path
            d={svgPaths.p32e40200}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group1() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 17 43"
      >
        <g id="Group">
          <path
            d={svgPaths.p6277b80}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group2() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 10 52"
      >
        <g id="Group">
          <path
            d={svgPaths.p1ff7cd00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group3() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 83 49"
      >
        <g id="Group">
          <path
            d={svgPaths.p32bff200}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group4() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 126 98"
      >
        <g id="Group">
          <path
            d={svgPaths.p2ec51100}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group5() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 5 19"
      >
        <g id="Group">
          <path
            d={svgPaths.p1c261100}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group6() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <div className="absolute bottom-0 left-[-0.01%] right-0 top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 4 12"
        >
          <g id="Group">
            <path
              d={svgPaths.p20c44000}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector"
            />
          </g>
        </svg>
      </div>
    </div>
  );
}

function Group7() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 4 12"
      >
        <g id="Group">
          <path
            d={svgPaths.p2d3dfa00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group8() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 4 9"
      >
        <g id="Group">
          <path
            d={svgPaths.p6509400}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group9() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <div className="absolute bottom-0 left-0 right-[-0.01%] top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 5 10"
        >
          <g id="Group">
            <path
              d={svgPaths.p67d9400}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector"
            />
          </g>
        </svg>
      </div>
    </div>
  );
}

function Group10() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <div className="absolute bottom-0 left-[-0.01%] right-0 top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 4 6"
        >
          <g id="Group">
            <path
              d={svgPaths.p268e8200}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector"
            />
          </g>
        </svg>
      </div>
    </div>
  );
}

function Group11() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 4 6"
      >
        <g id="Group">
          <path
            d={svgPaths.pe4d68c0}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group12() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 4 7"
      >
        <g id="Group">
          <path
            d={svgPaths.p1816b100}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group13() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 5 8"
      >
        <g id="Group">
          <path
            d={svgPaths.p3ee7ec0}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group14() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 5 8"
      >
        <g id="Group">
          <path
            d={svgPaths.p276d79f0}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group15() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 5 8"
      >
        <g id="Group">
          <path
            d={svgPaths.p16baaf40}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group16() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 6 8"
      >
        <g id="Group">
          <path
            d={svgPaths.p1fdaca00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group17() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 4 7"
      >
        <g id="Group">
          <path
            d={svgPaths.p2b889f80}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group18() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 6 7"
      >
        <g id="Group">
          <path
            d={svgPaths.p3c1ba780}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group19() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 4 7"
      >
        <g id="Group">
          <path
            d={svgPaths.p2e4ea700}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group20() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <div className="absolute bottom-0 left-0 right-[-0.01%] top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 3 7"
        >
          <g id="Group">
            <path
              d={svgPaths.p2e8ce200}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector"
            />
          </g>
        </svg>
      </div>
    </div>
  );
}

function Group21() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 4 5"
      >
        <g id="Group">
          <path
            d={svgPaths.p7d3e200}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group22() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 6 6"
      >
        <g id="Group">
          <path
            d={svgPaths.p3ae0d600}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group23() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 6 7"
      >
        <g id="Group">
          <path
            d={svgPaths.p3f8bf480}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group24() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 4 5"
      >
        <g id="Group">
          <path
            d={svgPaths.p711b300}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group25() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 5 6"
      >
        <g id="Group">
          <path
            d={svgPaths.p6f53880}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group26() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 4 5"
      >
        <g id="Group">
          <path
            d={svgPaths.p24f34d00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group27() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 4 6"
      >
        <g id="Group">
          <path
            d={svgPaths.p2f26a500}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group28() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 6 7"
      >
        <g id="Group">
          <path
            d={svgPaths.p1b42f100}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group29() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 5 6"
      >
        <g id="Group">
          <path
            d={svgPaths.p38530800}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group30() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 5 5"
      >
        <g id="Group">
          <path
            d={svgPaths.p312f5d00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group31() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 4 6"
      >
        <g id="Group">
          <path
            d={svgPaths.p1946cb80}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group32() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <div className="absolute bottom-0 left-0 right-[-0.01%] top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 4 5"
        >
          <g id="Group">
            <path
              d={svgPaths.p2f80cef2}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector"
            />
          </g>
        </svg>
      </div>
    </div>
  );
}

function Group33() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <div className="absolute bottom-0 left-[-0.01%] right-0 top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 4 5"
        >
          <g id="Group">
            <path
              d={svgPaths.p2ac9d6c0}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector"
            />
          </g>
        </svg>
      </div>
    </div>
  );
}

function Group34() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <div className="absolute bottom-0 left-0 right-[-0.01%] top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 3 5"
        >
          <g id="Group">
            <path
              d={svgPaths.p18c01a00}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector"
            />
          </g>
        </svg>
      </div>
    </div>
  );
}

function Group35() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <div className="absolute bottom-0 left-[-0.01%] right-0 top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 2 4"
        >
          <g id="Group">
            <path
              d={svgPaths.p37f94900}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector"
            />
          </g>
        </svg>
      </div>
    </div>
  );
}

function Group36() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 3 4"
      >
        <g id="Group">
          <path
            d={svgPaths.p3a532180}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group38() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <div className="absolute bottom-0 left-0 right-0 top-[-0.01%]">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 1 2"
        >
          <g id="Group">
            <path
              d={svgPaths.p4a2d900}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector"
            />
          </g>
        </svg>
      </div>
    </div>
  );
}

function Group39() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <div className="absolute bottom-0 left-[-0.01%] right-0 top-0">
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 2 2"
        >
          <g id="Group">
            <path
              d={svgPaths.p34b60f00}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector"
            />
          </g>
        </svg>
      </div>
    </div>
  );
}

function Group40() {
  return (
    <div
      className="absolute contents inset-[27.02%_39.1%_54.19%_48.53%]"
      data-name="Group"
    >
      <div className="absolute flex inset-[27.14%_39.1%_54.19%_48.69%] items-center justify-center">
        <div className="flex-none h-[119.886px] rotate-[26.181deg] w-[137.04px]">
          <Group />
        </div>
      </div>
      <div className="absolute flex inset-[30.3%_47.72%_64.65%_49.95%] items-center justify-center">
        <div className="flex-none h-[42.659px] rotate-[26.181deg] w-[16.355px]">
          <Group1 />
        </div>
      </div>
      <div className="absolute flex inset-[31.79%_48.66%_62.64%_49.18%] items-center justify-center">
        <div className="flex-none h-[51.147px] rotate-[26.181deg] w-[9.561px]">
          <Group2 />
        </div>
      </div>
      <div className="absolute flex inset-[27.83%_42.2%_63.22%_51.13%] items-center justify-center">
        <div className="flex-none h-[48.954px] rotate-[26.181deg] w-[82.971px]">
          <Group3 />
        </div>
      </div>
      <div className="absolute flex inset-[28.18%_39.73%_55.94%_49.47%] items-center justify-center">
        <div className="flex-none h-[97.769px] rotate-[26.181deg] w-[125.179px]">
          <Group4 />
        </div>
      </div>
      <div className="absolute flex inset-[34.51%_49.59%_63.39%_49.54%] items-center justify-center">
        <div className="flex-none h-[18.684px] rotate-[26.181deg] w-[4.77px]">
          <Group5 />
        </div>
      </div>
      <div className="absolute flex inset-[36.21%_49.79%_62.46%_49.63%] items-center justify-center">
        <div className="flex-none h-[11.512px] rotate-[26.181deg] w-[3.757px]">
          <Group6 />
        </div>
      </div>
      <div className="absolute flex inset-[36.58%_49.71%_62.11%_49.7%] items-center justify-center">
        <div className="flex-none h-[11.271px] rotate-[26.181deg] w-[3.888px]">
          <Group7 />
        </div>
      </div>
      <div className="absolute flex inset-[37.29%_49.69%_61.65%_49.8%] items-center justify-center">
        <div className="flex-none h-[8.767px] rotate-[26.181deg] w-[3.759px]">
          <Group8 />
        </div>
      </div>
      <div className="absolute flex inset-[37.65%_49.57%_61.22%_49.89%] items-center justify-center">
        <div className="flex-none h-[9.316px] rotate-[26.181deg] w-[4.167px]">
          <Group9 />
        </div>
      </div>
      <div className="absolute flex inset-[38.49%_49.45%_60.82%_50.2%] items-center justify-center">
        <div className="flex-none h-[5.415px] rotate-[26.181deg] w-[3.02px]">
          <Group10 />
        </div>
      </div>
      <div className="absolute flex inset-[38.74%_49.21%_60.55%_50.42%] items-center justify-center">
        <div className="flex-none h-[5.528px] rotate-[26.181deg] w-[3.183px]">
          <Group11 />
        </div>
      </div>
      <div className="absolute flex inset-[39.46%_48.8%_59.72%_50.8%] items-center justify-center">
        <div className="flex-none h-[6.527px] rotate-[26.181deg] w-[3.356px]">
          <Group12 />
        </div>
      </div>
      <div className="absolute flex inset-[39.7%_48.52%_59.35%_50.96%] items-center justify-center">
        <div className="flex-none h-[7.147px] rotate-[26.181deg] w-[4.831px]">
          <Group13 />
        </div>
      </div>
      <div className="absolute flex inset-[39.85%_48.3%_59.11%_51.15%] items-center justify-center">
        <div className="flex-none h-[7.983px] rotate-[26.181deg] w-[4.848px]">
          <Group14 />
        </div>
      </div>
      <div className="absolute flex inset-[40.05%_48.08%_58.96%_51.39%] items-center justify-center">
        <div className="flex-none h-[7.506px] rotate-[26.181deg] w-[4.94px]">
          <Group15 />
        </div>
      </div>
      <div className="absolute flex inset-[40.33%_47.8%_58.7%_51.65%] items-center justify-center">
        <div className="flex-none h-[7.067px] rotate-[26.181deg] w-[5.373px]">
          <Group16 />
        </div>
      </div>
      <div className="absolute flex inset-[40.66%_47.54%_58.54%_52.03%] items-center justify-center">
        <div className="flex-none h-[6.114px] rotate-[26.181deg] w-[3.907px]">
          <Group17 />
        </div>
      </div>
      <div className="absolute flex inset-[40.89%_47.19%_58.16%_52.27%] items-center justify-center">
        <div className="flex-none h-[6.895px] rotate-[26.181deg] w-[5.284px]">
          <Group18 />
        </div>
      </div>
      <div className="absolute flex inset-[41.29%_46.85%_57.94%_52.75%] items-center justify-center">
        <div className="flex-none h-[6.035px] rotate-[26.181deg] w-[3.464px]">
          <Group19 />
        </div>
      </div>
      <div className="absolute flex inset-[41.59%_46.41%_57.67%_53.23%] items-center justify-center">
        <div className="flex-none h-[6.05px] rotate-[26.181deg] w-[2.834px]">
          <Group20 />
        </div>
      </div>
      <div className="absolute flex inset-[41.77%_46.02%_57.55%_53.59%] items-center justify-center">
        <div className="flex-none h-[4.879px] rotate-[26.181deg] w-[3.85px]">
          <Group21 />
        </div>
      </div>
      <div className="absolute flex inset-[41.82%_45.58%_57.34%_53.9%] items-center justify-center">
        <div className="flex-none h-[5.678px] rotate-[26.181deg] w-[5.623px]">
          <Group22 />
        </div>
      </div>
      <div className="absolute flex inset-[41.9%_45.21%_57.25%_54.29%] items-center justify-center">
        <div className="flex-none h-[6.013px] rotate-[26.181deg] w-[5.081px]">
          <Group23 />
        </div>
      </div>
      <div className="absolute flex inset-[42.09%_44.94%_57.26%_54.68%] items-center justify-center">
        <div className="flex-none h-[4.548px] rotate-[26.181deg] w-[3.92px]">
          <Group24 />
        </div>
      </div>
      <div className="absolute flex inset-[42.11%_44.62%_57.13%_54.94%] items-center justify-center">
        <div className="flex-none h-[5.448px] rotate-[26.181deg] w-[4.375px]">
          <Group25 />
        </div>
      </div>
      <div className="absolute flex inset-[41.96%_44.24%_57.38%_55.39%] items-center justify-center">
        <div className="flex-none h-[4.748px] rotate-[26.181deg] w-[3.681px]">
          <Group26 />
        </div>
      </div>
      <div className="absolute flex inset-[41.97%_43.87%_57.28%_55.71%] items-center justify-center">
        <div className="flex-none h-[5.517px] rotate-[26.181deg] w-[3.985px]">
          <Group27 />
        </div>
      </div>
      <div className="absolute flex inset-[41.85%_43.51%_57.25%_55.97%] items-center justify-center">
        <div className="flex-none h-[6.527px] rotate-[26.181deg] w-[5.176px]">
          <Group28 />
        </div>
      </div>
      <div className="absolute flex inset-[41.81%_43.27%_57.42%_56.29%] items-center justify-center">
        <div className="flex-none h-[5.55px] rotate-[26.181deg] w-[4.411px]">
          <Group29 />
        </div>
      </div>
      <div className="absolute flex inset-[41.77%_42.94%_57.53%_56.63%] items-center justify-center">
        <div className="flex-none h-[4.702px] rotate-[26.181deg] w-[4.609px]">
          <Group30 />
        </div>
      </div>
      <div className="absolute flex inset-[41.55%_42.65%_57.74%_56.94%] items-center justify-center">
        <div className="flex-none h-[5.255px] rotate-[26.181deg] w-[3.883px]">
          <Group31 />
        </div>
      </div>
      <div className="absolute flex inset-[41.29%_42.4%_58.09%_57.26%] items-center justify-center">
        <div className="flex-none h-[4.763px] rotate-[26.181deg] w-[3.08px]">
          <Group32 />
        </div>
      </div>
      <div className="absolute flex inset-[41.14%_42.09%_58.24%_57.53%] items-center justify-center">
        <div className="flex-none h-[4.386px] rotate-[26.181deg] w-[3.849px]">
          <Group33 />
        </div>
      </div>
      <div className="absolute flex inset-[40.8%_41.88%_58.62%_57.82%] items-center justify-center">
        <div className="flex-none h-[4.509px] rotate-[26.181deg] w-[2.589px]">
          <Group34 />
        </div>
      </div>
      <div className="absolute flex inset-[40.49%_41.66%_59.12%_58.13%] items-center justify-center">
        <div className="flex-none h-[3.033px] rotate-[26.181deg] w-[1.731px]">
          <Group35 />
        </div>
      </div>
      <div className="absolute flex inset-[40.28%_41.38%_59.3%_58.4%] items-center justify-center">
        <div className="flex-none h-[3.121px] rotate-[26.181deg] w-[2.028px]">
          <Group36 />
        </div>
      </div>
      <div className="absolute flex inset-[39.71%_41.1%_60.05%_58.78%] items-center justify-center">
        <div className="flex-none h-[1.978px] rotate-[26.181deg] w-[0.957px]">
          <Group38 />
        </div>
      </div>
      <div className="absolute flex inset-[39.47%_40.86%_60.29%_59%] items-center justify-center">
        <div className="flex-none h-[1.639px] rotate-[26.181deg] w-[1.529px]">
          <Group39 />
        </div>
      </div>
    </div>
  );
}

function Group41() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 70 77"
      >
        <g id="Group">
          <path
            d={svgPaths.p2a6e5100}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group43() {
  return (
    <div className="absolute contents inset-[27.02%_39.1%_54.19%_48.53%]">
      <div className="absolute flex inset-[27.17%_39.24%_54.34%_48.66%] items-center justify-center">
        <div className="flex-none h-[118.692px] rotate-[26.181deg] w-[135.701px]">
          <div className="opacity-40 relative size-full" data-name="Vector">
            <svg
              className="block size-full"
              fill="none"
              preserveAspectRatio="none"
              viewBox="0 0 136 119"
            >
              <path
                d={svgPaths.p356bec50}
                fill="var(--fill-0, #6130DF)"
                id="Vector"
              />
            </svg>
          </div>
        </div>
      </div>
      <Group40 />
      <div className="absolute flex inset-[30.23%_41.67%_58.73%_51.64%] items-center justify-center">
        <div className="flex-none h-[76.286px] rotate-[26.181deg] w-[69.885px]">
          <Group41 />
        </div>
      </div>
    </div>
  );
}

function Group37() {
  return (
    <div className="absolute inset-[4.89%_89.2%_82.67%_5.49%]">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 77 112"
      >
        <g id="Group 37" opacity="0.4">
          <path
            d={svgPaths.p1316200}
            fill="var(--fill-0, #0F0935)"
            id="Vector"
          />
          <g id="Group">
            <path
              d={svgPaths.p3ff7f500}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_2"
            />
          </g>
          <g id="Group_2">
            <path
              d={svgPaths.p393c9400}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_3"
            />
          </g>
          <g id="Group_3">
            <path
              d={svgPaths.p2601b100}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_4"
            />
          </g>
          <g id="Group_4">
            <path
              d={svgPaths.pcae3300}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_5"
            />
          </g>
          <g id="Group_5">
            <path
              d={svgPaths.pc9aa980}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_6"
            />
          </g>
          <g id="Group_6">
            <path
              d={svgPaths.p25d74d00}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_7"
            />
          </g>
          <g id="Group_7">
            <path
              d={svgPaths.p32494480}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_8"
            />
          </g>
          <g id="Group_8">
            <path
              d={svgPaths.pf2a95f0}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_9"
            />
          </g>
          <g id="Group_9">
            <path
              d={svgPaths.p3127b600}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_10"
            />
          </g>
          <g id="Group_10">
            <path
              d={svgPaths.p1c26a300}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_11"
            />
          </g>
          <g id="Group_11">
            <path
              d={svgPaths.p52af680}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_12"
            />
          </g>
          <g id="Group_12">
            <path
              d={svgPaths.p2b19f900}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_13"
            />
          </g>
          <g id="Group_13">
            <path
              d={svgPaths.p348119c0}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_14"
            />
          </g>
          <g id="Group_14">
            <path
              d={svgPaths.p20e31000}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_15"
            />
          </g>
          <g id="Group_15">
            <path
              d={svgPaths.p4a22200}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_16"
            />
          </g>
          <g id="Group_16">
            <path
              d={svgPaths.p2ae7ef40}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_17"
            />
          </g>
          <g id="Group_17">
            <path
              d={svgPaths.p23175f00}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_18"
            />
          </g>
          <g id="Group_18">
            <path
              d={svgPaths.p36c22200}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_19"
            />
          </g>
          <g id="Group_19">
            <path
              d={svgPaths.p30f40c70}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_20"
            />
          </g>
          <g id="Group_20">
            <path
              d={svgPaths.p3da20800}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_21"
            />
          </g>
        </g>
      </svg>
    </div>
  );
}

function Group47() {
  return (
    <div className="relative size-full">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 57 79"
      >
        <g id="Group 47" opacity="0.4">
          <path
            d={svgPaths.p34e5fac0}
            fill="var(--fill-0, #FF4B00)"
            id="Vector"
          />
          <g id="Group">
            <path
              d={svgPaths.p11cee580}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_2"
            />
          </g>
          <g id="Group_2">
            <path
              d={svgPaths.p1a87aef2}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_3"
            />
          </g>
          <g id="Group_3">
            <path
              d={svgPaths.p2ba04c0}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_4"
            />
          </g>
          <g id="Group_4">
            <path
              d={svgPaths.p18dada00}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_5"
            />
          </g>
          <g id="Group_5">
            <path
              d={svgPaths.p2f93ac00}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_6"
            />
          </g>
          <g id="Group_6">
            <path
              d={svgPaths.p3ceab300}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_7"
            />
          </g>
          <g id="Group_7">
            <path
              d={svgPaths.p1bf2db80}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_8"
            />
          </g>
          <g id="Group_8">
            <path
              d={svgPaths.p257a6380}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_9"
            />
          </g>
          <g id="Group_9">
            <path
              d={svgPaths.p39be7100}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_10"
            />
          </g>
          <g id="Group_10">
            <path
              d={svgPaths.p2dc6df80}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_11"
            />
          </g>
          <g id="Group_11">
            <path
              d={svgPaths.p3d98cd80}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_12"
            />
          </g>
          <g id="Group_12">
            <path
              d={svgPaths.p24f72480}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_13"
            />
          </g>
          <g id="Group_13">
            <path
              d={svgPaths.p3cd44e00}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_14"
            />
          </g>
          <g id="Group_14">
            <path
              d={svgPaths.p23b18b00}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_15"
            />
          </g>
          <g id="Group_15">
            <path
              d={svgPaths.p2b2464c0}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_16"
            />
          </g>
          <g id="Group_16">
            <path
              d={svgPaths.p9ae4e00}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_17"
            />
          </g>
          <g id="Group_17">
            <path
              d={svgPaths.p764ca80}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_18"
            />
          </g>
          <g id="Group_18">
            <path
              d={svgPaths.p3c4c8700}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_19"
            />
          </g>
          <g id="Group_19">
            <path
              d={svgPaths.p358f7600}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_20"
            />
          </g>
          <g id="Group_20">
            <path
              d={svgPaths.p3ab2680}
              fill="var(--fill-0, #F4F3F2)"
              id="Vector_21"
            />
          </g>
        </g>
      </svg>
    </div>
  );
}

function Group86() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 6 6"
      >
        <g id="Group">
          <path
            d={svgPaths.p2e63a080}
            fill="var(--fill-0, white)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group87() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 8 6"
      >
        <g id="Group">
          <path
            d={svgPaths.p36467e00}
            fill="var(--fill-0, white)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group88() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 6 8"
      >
        <g id="Group">
          <path d={svgPaths.paad3400} fill="var(--fill-0, white)" id="Vector" />
        </g>
      </svg>
    </div>
  );
}

function Group89() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 6 6"
      >
        <g id="Group">
          <path d={svgPaths.pece5800} fill="var(--fill-0, white)" id="Vector" />
        </g>
      </svg>
    </div>
  );
}

function Group90() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 5 7"
      >
        <g id="Group">
          <path
            d={svgPaths.p19e799f0}
            fill="var(--fill-0, white)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group91() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 3 3"
      >
        <g id="Group">
          <path d={svgPaths.p6991500} fill="var(--fill-0, white)" id="Vector" />
        </g>
      </svg>
    </div>
  );
}

function Group92() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 95 130"
      >
        <g id="Group">
          <path
            d={svgPaths.p34ec6b00}
            fill="var(--fill-0, white)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group93() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 66 61"
      >
        <g id="Group">
          <path
            d={svgPaths.p17371880}
            fill="var(--fill-0, white)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group94() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 16 46"
      >
        <g id="Group">
          <path
            d={svgPaths.p179a1400}
            fill="var(--fill-0, white)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group95() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 66 113"
      >
        <g id="Group">
          <path
            d={svgPaths.p2c9dfd00}
            fill="var(--fill-0, white)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group96() {
  return (
    <div
      className="absolute contents inset-[73.67%_83.97%_8.67%_6.04%]"
      data-name="Group"
    >
      <div className="absolute flex inset-[88.59%_89.62%_10.54%_9.83%] items-center justify-center">
        <div className="flex-none h-[5.747px] rotate-[207.153deg] scale-y-[-100%] w-[5.891px]">
          <Group86 />
        </div>
      </div>
      <div className="absolute flex inset-[88.08%_89.86%_11.02%_9.53%] items-center justify-center">
        <div className="flex-none h-[5.5px] rotate-[207.153deg] scale-y-[-100%] w-[7.064px]">
          <Group87 />
        </div>
      </div>
      <div className="absolute flex inset-[86.42%_90.63%_12.59%_8.79%] items-center justify-center">
        <div className="flex-none h-[7.095px] rotate-[207.153deg] scale-y-[-100%] w-[5.696px]">
          <Group88 />
        </div>
      </div>
      <div className="absolute flex inset-[85.79%_90.89%_13.38%_8.59%] items-center justify-center">
        <div className="flex-none h-[5.621px] rotate-[207.153deg] scale-y-[-100%] w-[5.494px]">
          <Group89 />
        </div>
      </div>
      <div className="absolute flex inset-[81.22%_91.23%_17.92%_8.3%] items-center justify-center">
        <div className="flex-none h-[6.468px] rotate-[207.153deg] scale-y-[-100%] w-[4.243px]">
          <Group90 />
        </div>
      </div>
      <div className="absolute flex inset-[78.95%_91.16%_20.63%_8.59%] items-center justify-center">
        <div className="flex-none h-[2.886px] rotate-[207.153deg] scale-y-[-100%] w-[2.598px]">
          <Group91 />
        </div>
      </div>
      <div className="absolute flex inset-[73.67%_83.97%_8.67%_6.04%] items-center justify-center">
        <div className="flex-none h-[129.911px] rotate-[207.153deg] scale-y-[-100%] w-[95px]">
          <Group92 />
        </div>
      </div>
      <div className="absolute flex inset-[75.12%_84.64%_15.58%_9.41%] items-center justify-center">
        <div className="flex-none h-[60.651px] rotate-[207.153deg] scale-y-[-100%] w-[65.264px]">
          <Group93 />
        </div>
      </div>
      <div className="absolute flex inset-[84.41%_86.44%_10.27%_11.14%] items-center justify-center">
        <div className="flex-none h-[45.793px] rotate-[207.153deg] scale-y-[-100%] w-[15.526px]">
          <Group94 />
        </div>
      </div>
      <div className="absolute flex inset-[74.45%_85.62%_11.17%_6.8%] items-center justify-center">
        <div className="flex-none h-[112.036px] rotate-[207.153deg] scale-y-[-100%] w-[65.17px]">
          <Group95 />
        </div>
      </div>
    </div>
  );
}

function Group97() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 57 55"
      >
        <g id="Group">
          <path
            d={svgPaths.p182e06d0}
            fill="var(--fill-0, white)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group98() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 59 13"
      >
        <g id="Group">
          <path
            d={svgPaths.p34d1e8a0}
            fill="var(--fill-0, white)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group99() {
  return (
    <div className="opacity-40 relative size-full" data-name="Group">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 63 13"
      >
        <g id="Group">
          <path
            d={svgPaths.p25065d00}
            fill="var(--fill-0, white)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group46() {
  return (
    <div className="absolute contents inset-[73.67%_83.97%_8.67%_6.04%]">
      <div className="absolute flex inset-[73.79%_84.08%_8.81%_6.08%] items-center justify-center">
        <div className="flex-none h-[128.013px] rotate-[207.153deg] scale-y-[-100%] w-[93.611px]">
          <div className="opacity-40 relative size-full" data-name="Vector">
            <svg
              className="block size-full"
              fill="none"
              preserveAspectRatio="none"
              viewBox="0 0 94 129"
            >
              <path
                d={svgPaths.p32f4b580}
                fill="var(--fill-0, #6130DF)"
                id="Vector"
              />
            </svg>
          </div>
        </div>
      </div>
      <Group96 />
      <div className="absolute flex inset-[77.59%_86.04%_14.15%_8.73%] items-center justify-center">
        <div className="flex-none h-[54.441px] rotate-[207.153deg] scale-y-[-100%] w-[56.699px]">
          <Group97 />
        </div>
      </div>
      <div className="absolute flex inset-[78.34%_86.2%_17.5%_9.81%] items-center justify-center">
        <div className="flex-none h-[12.152px] rotate-[207.153deg] scale-y-[-100%] w-[58.363px]">
          <Group98 />
        </div>
      </div>
      <div className="absolute flex inset-[79.63%_86.44%_16.01%_9.32%] items-center justify-center">
        <div className="flex-none h-[12.114px] rotate-[207.153deg] scale-y-[-100%] w-[62.358px]">
          <Group99 />
        </div>
      </div>
    </div>
  );
}

function Group100() {
  return (
    <div
      className="absolute inset-[84.7%_48.66%_-11.64%_36.67%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 212 243"
      >
        <g id="Group">
          <path
            d={svgPaths.p252eab00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group101() {
  return (
    <div
      className="absolute inset-[84.56%_47.92%_-11.49%_38.06%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 202 243"
      >
        <g id="Group">
          <path
            d={svgPaths.p18d0e400}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group102() {
  return (
    <div
      className="absolute inset-[84.9%_48.73%_-11.56%_36.72%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 210 240"
      >
        <g id="Group">
          <path
            d={svgPaths.p8444900}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group103() {
  return (
    <div
      className="absolute inset-[110.56%_62.05%_-11.42%_37.17%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 12 8"
      >
        <g id="Group">
          <path
            d={svgPaths.pab0c572}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group104() {
  return (
    <div
      className="absolute inset-[110.54%_61.83%_-11.19%_37.55%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 9 6"
      >
        <g id="Group">
          <path
            d={svgPaths.p4f5e000}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group105() {
  return (
    <div
      className="absolute inset-[110.31%_61.45%_-10.87%_37.92%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 10 5"
      >
        <g id="Group">
          <path
            d={svgPaths.p3dc8a580}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group106() {
  return (
    <div
      className="absolute inset-[109.76%_61.07%_-10.27%_38.38%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 8 5"
      >
        <g id="Group">
          <path
            d={svgPaths.pbec9000}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group107() {
  return (
    <div
      className="absolute inset-[109.23%_60.42%_-9.73%_38.9%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 10 5"
      >
        <g id="Group">
          <path
            d={svgPaths.p1c0cd780}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group108() {
  return (
    <div
      className="absolute inset-[108.67%_60.04%_-9.21%_39.33%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 10 5"
      >
        <g id="Group">
          <path
            d={svgPaths.pee80200}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group109() {
  return (
    <div
      className="absolute inset-[108.03%_59.53%_-8.63%_39.79%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 10 6"
      >
        <g id="Group">
          <path
            d={svgPaths.p2ba2fd00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group110() {
  return (
    <div
      className="absolute inset-[107.46%_58.93%_-8.22%_40.21%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 13 7"
      >
        <g id="Group">
          <path
            d={svgPaths.pc958e70}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group111() {
  return (
    <div
      className="absolute inset-[106.84%_58.52%_-7.41%_40.9%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 9 6"
      >
        <g id="Group">
          <path
            d={svgPaths.p171f4700}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group112() {
  return (
    <div
      className="absolute inset-[105.45%_57.84%_-6.24%_41.41%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 11 8"
      >
        <g id="Group">
          <path
            d={svgPaths.p3f366400}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group113() {
  return (
    <div
      className="absolute inset-[104.48%_57.06%_-5.33%_42.13%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 12 8"
      >
        <g id="Group">
          <path
            d={svgPaths.p15ee2b00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group114() {
  return (
    <div
      className="absolute inset-[103.25%_56.32%_-4.08%_43.01%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 10 8"
      >
        <g id="Group">
          <path
            d={svgPaths.p1cb19600}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group115() {
  return (
    <div
      className="absolute inset-[102.1%_55.57%_-3.06%_43.53%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 13 9"
      >
        <g id="Group">
          <path
            d={svgPaths.pf4b9300}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group116() {
  return (
    <div
      className="absolute inset-[101.22%_54.99%_-2%_44.27%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 11 8"
      >
        <g id="Group">
          <path
            d={svgPaths.p2b1dc700}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group117() {
  return (
    <div
      className="absolute inset-[100.12%_54.29%_-0.81%_45.05%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 10 7"
      >
        <g id="Group">
          <path
            d={svgPaths.p37173680}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group118() {
  return (
    <div
      className="absolute inset-[99.25%_53.59%_0.09%_45.83%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 9 6"
      >
        <g id="Group">
          <path
            d={svgPaths.p26cd5580}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group119() {
  return (
    <div
      className="absolute inset-[98.05%_53.38%_1.12%_45.94%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 10 8"
      >
        <g id="Group">
          <path
            d={svgPaths.pf1762f2}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group120() {
  return (
    <div
      className="absolute inset-[97.2%_52.87%_1.88%_46.43%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 11 9"
      >
        <g id="Group">
          <path
            d={svgPaths.p2d5f3380}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group121() {
  return (
    <div
      className="absolute inset-[97%_52.77%_2.46%_46.79%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 7 5"
      >
        <g id="Group">
          <path
            d={svgPaths.p3c20b780}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group122() {
  return (
    <div
      className="absolute inset-[96.18%_52.36%_2.9%_46.94%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 11 9"
      >
        <g id="Group">
          <path
            d={svgPaths.p31536200}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group123() {
  return (
    <div
      className="absolute inset-[95.64%_52.12%_3.63%_47.25%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 9 7"
      >
        <g id="Group">
          <path
            d={svgPaths.p33c1d200}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group124() {
  return (
    <div
      className="absolute inset-[94.41%_51.59%_4.49%_47.55%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 13 10"
      >
        <g id="Group">
          <path
            d={svgPaths.pd0ff400}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group125() {
  return (
    <div
      className="absolute inset-[93.77%_51.18%_5.42%_48.11%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 11 8"
      >
        <g id="Group">
          <path
            d={svgPaths.p25d73c80}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group126() {
  return (
    <div
      className="absolute inset-[92.33%_50.62%_6.74%_48.65%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 11 9"
      >
        <g id="Group">
          <path
            d={svgPaths.p1faadb00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group127() {
  return (
    <div
      className="absolute inset-[90.9%_50.14%_8.19%_49.14%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 11 9"
      >
        <g id="Group">
          <path
            d={svgPaths.p2c5a780}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group128() {
  return (
    <div
      className="absolute inset-[89.76%_49.67%_9.43%_49.63%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 11 8"
      >
        <g id="Group">
          <path
            d={svgPaths.p2487d800}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group129() {
  return (
    <div
      className="absolute inset-[88.42%_49.21%_10.93%_50.13%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 10 6"
      >
        <g id="Group">
          <path
            d={svgPaths.p104da0f0}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group130() {
  return (
    <div
      className="absolute inset-[87.55%_49.02%_11.87%_50.56%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 7 6"
      >
        <g id="Group">
          <path
            d={svgPaths.pfe79d00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group131() {
  return (
    <div
      className="absolute inset-[86.92%_48.65%_12.38%_50.73%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 9 7"
      >
        <g id="Group">
          <path
            d={svgPaths.p181c5e00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group132() {
  return (
    <div
      className="absolute inset-[86.33%_48.54%_12.93%_50.98%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 7 7"
      >
        <g id="Group">
          <path
            d={svgPaths.p7635a00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group133() {
  return (
    <div
      className="absolute inset-[85.58%_48.14%_13.69%_51.31%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 8 7"
      >
        <g id="Group">
          <path
            d={svgPaths.pb116e00}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group134() {
  return (
    <div
      className="absolute inset-[85.58%_48.17%_14.04%_51.63%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 3 4"
      >
        <g id="Group">
          <path
            d={svgPaths.p1d116d80}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group135() {
  return (
    <div
      className="absolute inset-[86.28%_49.65%_-9.97%_37.3%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 188 214"
      >
        <g id="Group">
          <path
            d={svgPaths.p1b466280}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group136() {
  return (
    <div
      className="absolute contents inset-[84.56%_47.92%_-11.64%_36.67%]"
      data-name="Group"
    >
      <Group100 />
      <Group101 />
      <Group102 />
      <Group103 />
      <Group104 />
      <Group105 />
      <Group106 />
      <Group107 />
      <Group108 />
      <Group109 />
      <Group110 />
      <Group111 />
      <Group112 />
      <Group113 />
      <Group114 />
      <Group115 />
      <Group116 />
      <Group117 />
      <Group118 />
      <Group119 />
      <Group120 />
      <Group121 />
      <Group122 />
      <Group123 />
      <Group124 />
      <Group125 />
      <Group126 />
      <Group127 />
      <Group128 />
      <Group129 />
      <Group130 />
      <Group131 />
      <Group132 />
      <Group133 />
      <Group134 />
      <Group135 />
    </div>
  );
}

function Group137() {
  return (
    <div
      className="absolute inset-[90.37%_52.05%_-5.27%_39.85%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 117 135"
      >
        <g id="Group">
          <path
            d={svgPaths.p19f6dd80}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group138() {
  return (
    <div
      className="absolute inset-[90.99%_52.92%_-5.12%_39.37%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 111 128"
      >
        <g id="Group">
          <path
            d={svgPaths.p1c72200}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group139() {
  return (
    <div
      className="absolute inset-[90.6%_52.19%_-5.57%_39.62%] opacity-40"
      data-name="Group"
    >
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 118 135"
      >
        <g id="Group">
          <path
            d={svgPaths.p3c2f4e80}
            fill="var(--fill-0, #F4F3F2)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Group45() {
  return (
    <div className="absolute contents inset-[84.56%_47.92%_-11.74%_36.67%]">
      <div
        className="absolute inset-[84.94%_48.15%_-11.74%_36.83%] opacity-40"
        data-name="Vector"
      >
        <svg
          className="block size-full"
          fill="none"
          preserveAspectRatio="none"
          viewBox="0 0 217 242"
        >
          <path
            d={svgPaths.p28e68620}
            fill="var(--fill-0, #FCC047)"
            id="Vector"
          />
        </svg>
      </div>
      <Group136 />
      <Group137 />
      <Group138 />
      <Group139 />
    </div>
  );
}

function Component2() {
  return (
    <div
      className="absolute bg-[#ffffff] h-[900px] left-0 overflow-clip top-px w-[1440px]"
      data-name="Component 2"
    >
      <Group43 />
      <Group37 />
      <div className="absolute flex inset-[4%_54.54%_85.89%_40.21%] items-center justify-center">
        <div className="flex-none h-[78.571px] rotate-[195.935deg] w-[56.18px]">
          <Group47 />
        </div>
      </div>
      <Group46 />
      <Group45 />
    </div>
  );
}

function Frame1() {
  return (
    <div className="box-border content-stretch flex flex-col gap-2 items-start justify-start p-0 relative shrink-0 w-full">
      <div className="flex flex-col font-['Schibsted_Grotesk:Bold',_sans-serif] font-bold justify-center leading-[0] relative shrink-0 text-[#100937] text-[30px] text-center w-full">
        <p className="block leading-[normal]">Create your Obiemoney account</p>
      </div>
    </div>
  );
}

function Google() {
  return (
    <div className="relative shrink-0 size-6" data-name="google">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="google">
          <circle
            cx="12"
            cy="12"
            fill="var(--fill-0, white)"
            id="Ellipse"
            r="12"
          />
          <g id="Logo">
            <path
              clipRule="evenodd"
              d={svgPaths.p191cc400}
              fill="var(--fill-0, #4285F4)"
              fillRule="evenodd"
              id="Vector"
            />
            <path
              clipRule="evenodd"
              d={svgPaths.p3ac5f1f0}
              fill="var(--fill-0, #34A853)"
              fillRule="evenodd"
              id="Vector_2"
            />
            <path
              clipRule="evenodd"
              d={svgPaths.p387fcf40}
              fill="var(--fill-0, #FBBC05)"
              fillRule="evenodd"
              id="Vector_3"
            />
            <path
              clipRule="evenodd"
              d={svgPaths.p34b65e00}
              fill="var(--fill-0, #EA4335)"
              fillRule="evenodd"
              id="Vector_4"
            />
            <g id="Vector_5"></g>
          </g>
        </g>
      </svg>
    </div>
  );
}

function Span() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-2 items-center justify-center p-0 relative shrink-0"
      data-name="span"
    >
      <Google />
      <div className="font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal leading-[0] relative shrink-0 text-[#424242] text-[16px] text-left text-nowrap">
        <p className="block leading-[25.6px] whitespace-pre">
          Sign up with Google
        </p>
      </div>
    </div>
  );
}

function BtnOutlineTemplate() {
  return (
    <div
      className="bg-[#f2eefc] box-border content-stretch flex flex-col gap-[15px] h-[50px] items-center justify-center p-0 relative rounded-[50px] shrink-0 w-full"
      data-name="btn-outline/template/"
    >
      <Span />
    </div>
  );
}

function Frame1618873381() {
  return (
    <div className="bg-[#f4f4f4] box-border content-stretch flex flex-row gap-2.5 items-center justify-center p-[4px] relative rounded shrink-0">
      <div className="font-['Schibsted_Grotesk:Medium',_sans-serif] font-medium leading-[0] relative shrink-0 text-[#616161] text-[12.8px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">OR</p>
      </div>
    </div>
  );
}

function DividerLabel() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-2.5 items-center justify-center p-0 relative shrink-0 w-full"
      data-name="divider-label"
    >
      <div
        className="basis-0 grow h-0.5 min-h-px min-w-px relative shrink-0"
        data-name="line-divider"
      >
        <div className="absolute bottom-1/4 left-[-0.33%] right-[-0.33%] top-1/4">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 153 2"
          >
            <path
              d="M1 1H152.5"
              id="line-divider"
              stroke="var(--stroke-0, #E0E0E0)"
              strokeLinecap="square"
            />
          </svg>
        </div>
      </div>
      <Frame1618873381 />
      <div
        className="basis-0 grow h-0.5 min-h-px min-w-px relative shrink-0"
        data-name="line-divider"
      >
        <div className="absolute bottom-1/4 left-[-0.33%] right-[-0.33%] top-1/4">
          <svg
            className="block size-full"
            fill="none"
            preserveAspectRatio="none"
            viewBox="0 0 153 2"
          >
            <path
              d="M1 1H152.5"
              id="line-divider"
              stroke="var(--stroke-0, #E0E0E0)"
              strokeLinecap="square"
            />
          </svg>
        </div>
      </div>
    </div>
  );
}

function Content() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-6 min-w-6 overflow-clip px-0 py-3.5 relative shrink-0 w-full"
      data-name="Content"
    >
      <div className="basis-0 font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal grow leading-[0] min-h-px min-w-px relative shrink-0 text-[14px] text-left text-neutral-500">
        <p className="block leading-[20px]">Name</p>
      </div>
    </div>
  );
}

function Input() {
  return (
    <div
      className="basis-0 grow min-h-px min-w-px relative rounded shrink-0 w-full"
      data-name="Input"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#d0d0d2] border-solid inset-0 pointer-events-none rounded"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-center px-4 py-0 relative size-full">
          <Content />
        </div>
      </div>
    </div>
  );
}

function TextField() {
  return (
    <div
      className="box-border content-stretch flex flex-col h-12 items-start justify-start p-0 relative shrink-0 w-full"
      data-name="TextField"
    >
      <Input />
    </div>
  );
}

function Col() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-col gap-4 grow items-start justify-start min-h-px min-w-px p-0 relative shrink-0"
      data-name="Col"
    >
      <TextField />
    </div>
  );
}

function CalendarToday() {
  return (
    <div className="relative shrink-0 size-5" data-name="calendar_today">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 20 20"
      >
        <g id="calendar_today">
          <path
            d={svgPaths.p19839300}
            fill="var(--fill-0, #515151)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function AdornEndContainer() {
  return (
    <div
      className="box-border content-stretch flex flex-row items-center justify-center p-0 relative shrink-0"
      data-name="Adorn. End Container"
    >
      <CalendarToday />
    </div>
  );
}

function Content1() {
  return (
    <div
      className="box-border content-stretch flex flex-row items-center justify-start min-h-6 overflow-clip px-0 py-4 relative shrink-0 w-full"
      data-name="Content"
    >
      <div className="basis-0 font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal grow leading-[0] min-h-px min-w-px relative shrink-0 text-[14px] text-left text-neutral-500">
        <p className="block leading-[20px]">Date of birth</p>
      </div>
      <AdornEndContainer />
    </div>
  );
}

function Input1() {
  return (
    <div
      className="basis-0 grow min-h-px min-w-px relative rounded shrink-0 w-full"
      data-name="Input"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#d0d0d2] border-solid inset-0 pointer-events-none rounded"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-center px-3 py-0 relative size-full">
          <Content1 />
        </div>
      </div>
    </div>
  );
}

function Dropdown() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-col grow h-12 items-start justify-start min-h-px min-w-px p-0 relative shrink-0"
      data-name="Dropdown"
    >
      <Input1 />
    </div>
  );
}

function Frame1618873382() {
  return (
    <div className="box-border content-stretch flex flex-row gap-[18px] items-start justify-start p-0 relative shrink-0 w-full">
      <Col />
      <Dropdown />
    </div>
  );
}

function Content2() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-6 min-w-6 overflow-clip px-0 py-3.5 relative shrink-0 w-full"
      data-name="Content"
    >
      <div className="basis-0 font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal grow leading-[0] min-h-px min-w-px relative shrink-0 text-[14px] text-left text-neutral-500">
        <p className="block leading-[20px]">Email address</p>
      </div>
    </div>
  );
}

function Input2() {
  return (
    <div
      className="basis-0 grow min-h-px min-w-px relative rounded shrink-0 w-full"
      data-name="Input"
    >
      <div
        aria-hidden="true"
        className="absolute border border-[#d0d0d2] border-solid inset-0 pointer-events-none rounded"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col items-start justify-center px-4 py-0 relative size-full">
          <Content2 />
        </div>
      </div>
    </div>
  );
}

function TextField1() {
  return (
    <div
      className="box-border content-stretch flex flex-col h-12 items-start justify-start p-0 relative shrink-0 w-full"
      data-name="TextField"
    >
      <Input2 />
    </div>
  );
}

function Col1() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-4 items-start justify-start p-0 relative shrink-0 w-full"
      data-name="Col"
    >
      <TextField1 />
    </div>
  );
}

function Visibility() {
  return (
    <div className="relative shrink-0 size-6" data-name="visibility">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="visibility">
          <path
            d={svgPaths.pa272300}
            fill="var(--fill-0, #737373)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Content3() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-6 min-w-6 overflow-clip px-0 py-3.5 relative shrink-0 w-full"
      data-name="Content"
    >
      <div className="basis-0 font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal grow leading-[0] min-h-px min-w-px relative shrink-0 text-[14px] text-left text-neutral-500">
        <p className="block leading-[20px]">Password</p>
      </div>
      <Visibility />
    </div>
  );
}

function Input3() {
  return (
    <div className="h-12 relative rounded shrink-0 w-full" data-name="Input">
      <div
        aria-hidden="true"
        className="absolute border border-[#d0d0d2] border-solid inset-0 pointer-events-none rounded"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col h-12 items-start justify-center px-4 py-0 relative w-full">
          <Content3 />
        </div>
      </div>
    </div>
  );
}

function TextField2() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-full"
      data-name="TextField"
    >
      <Input3 />
    </div>
  );
}

function Visibility1() {
  return (
    <div className="relative shrink-0 size-6" data-name="visibility">
      <svg
        className="block size-full"
        fill="none"
        preserveAspectRatio="none"
        viewBox="0 0 24 24"
      >
        <g id="visibility">
          <path
            d={svgPaths.pa272300}
            fill="var(--fill-0, #737373)"
            id="Vector"
          />
        </g>
      </svg>
    </div>
  );
}

function Content4() {
  return (
    <div
      className="basis-0 box-border content-stretch flex flex-row grow items-center justify-start min-h-6 min-w-6 overflow-clip px-0 py-3.5 relative shrink-0 w-full"
      data-name="Content"
    >
      <div className="basis-0 font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal grow leading-[0] min-h-px min-w-px relative shrink-0 text-[14px] text-left text-neutral-500">
        <p className="block leading-[20px]">Confirm password</p>
      </div>
      <Visibility1 />
    </div>
  );
}

function Input4() {
  return (
    <div className="h-12 relative rounded shrink-0 w-full" data-name="Input">
      <div
        aria-hidden="true"
        className="absolute border border-[#d0d0d2] border-solid inset-0 pointer-events-none rounded"
      />
      <div className="flex flex-col justify-center relative size-full">
        <div className="box-border content-stretch flex flex-col h-12 items-start justify-center px-4 py-0 relative w-full">
          <Content4 />
        </div>
      </div>
    </div>
  );
}

function TextField3() {
  return (
    <div
      className="box-border content-stretch flex flex-col items-start justify-start p-0 relative shrink-0 w-full"
      data-name="TextField"
    >
      <Input4 />
    </div>
  );
}

function Base() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-2 items-center justify-center p-0 relative shrink-0"
      data-name="Base"
    >
      <div className="font-['Schibsted_Grotesk:Medium',_sans-serif] font-medium leading-[0] relative shrink-0 text-[#ffffff] text-[14px] text-left text-nowrap">
        <p className="block leading-[normal] whitespace-pre">Create Account</p>
      </div>
    </div>
  );
}

function Button() {
  return (
    <div
      className="bg-[#6130df] h-12 relative rounded-lg shrink-0 w-full"
      data-name="Button"
    >
      <div className="flex flex-col items-center justify-center overflow-clip relative size-full">
        <div className="box-border content-stretch flex flex-col h-12 items-center justify-center px-[19px] py-2 relative w-full">
          <Base />
        </div>
      </div>
    </div>
  );
}

function Frame3() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[18px] items-start justify-start p-0 relative shrink-0 w-full">
      <Frame1618873382 />
      <Col1 />
      <TextField2 />
      <TextField3 />
      <Button />
    </div>
  );
}

function Frame4() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[7px] items-start justify-start p-0 relative shrink-0 w-full">
      <Frame3 />
      <div className="font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal leading-[0] relative shrink-0 text-[#757575] text-[12px] text-center w-full">
        <p className="leading-[18px]">
          <span>{`By signing up to Obiemoney, means you agree to our `}</span>
          <span className="[text-decoration-line:underline] [text-decoration-skip-ink:none] [text-decoration-style:solid] [text-underline-position:from-font]">
            Privacy Policy
          </span>
          <span>{` and `}</span>
          <span className="[text-decoration-line:underline] [text-decoration-skip-ink:none] [text-decoration-style:solid] [text-underline-position:from-font]">
            Terms of Service
          </span>
        </p>
      </div>
    </div>
  );
}

function Frame1618873383() {
  return (
    <div className="box-border content-stretch flex flex-row gap-1 items-start justify-center leading-[0] p-0 relative shrink-0 text-[12.8px] text-center text-nowrap">
      <div className="font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal relative shrink-0 text-[#616161]">
        <p className="block leading-[normal] text-nowrap whitespace-pre">
          Already a Member?
        </p>
      </div>
      <div className="font-['Schibsted_Grotesk:Bold',_sans-serif] font-bold relative shrink-0 text-[#212121]">
        <p className="[text-decoration-line:underline] [text-decoration-skip-ink:none] [text-decoration-style:solid] [text-underline-position:from-font] block leading-[normal] text-nowrap whitespace-pre">
          Log In
        </p>
      </div>
    </div>
  );
}

function Frame5() {
  return (
    <div className="box-border content-stretch flex flex-col gap-10 items-center justify-center p-0 relative shrink-0 w-full">
      <Frame4 />
      <Frame1618873383 />
    </div>
  );
}

function Frame6() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[25px] items-start justify-start p-0 relative shrink-0 w-[350px]">
      <Frame1 />
      <BtnOutlineTemplate />
      <DividerLabel />
      <Frame5 />
    </div>
  );
}

function Frame1618873380() {
  return (
    <div
      className="absolute bg-[#ffffff] box-border content-stretch flex flex-row gap-2.5 items-center justify-center p-[50px] rounded-2xl top-1/2 translate-x-[-50%] translate-y-[-50%] w-[450px]"
      style={{ left: "calc(50% + 386px)" }}
    >
      <Frame6 />
    </div>
  );
}

function UbrandLogo() {
  return (
    <div
      className="grid-cols-[max-content] grid-rows-[max-content] inline-grid leading-[0] place-items-start relative shrink-0"
      data-name="ubrand-logo"
    >
      <div
        className="[grid-area:1_/_1] bg-center bg-cover bg-no-repeat h-[52.946px] ml-0 mt-0 w-56"
        data-name="image 1"
        style={{ backgroundImage: `url('${imgImage1}')` }}
      />
    </div>
  );
}

function Frame1618873356() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[18px] items-start justify-start leading-[0] p-0 relative shrink-0 text-center w-full">
      <div className="flex flex-col font-['Schibsted_Grotesk:ExtraBold',_sans-serif] font-extrabold justify-end relative shrink-0 text-[#6130df] text-[62px] w-full">
        <p className="block leading-[68px]">
          Build yourself to wealth in 9 simple steps
        </p>
      </div>
      <div className="flex flex-col font-['Schibsted_Grotesk:Regular',_sans-serif] font-normal justify-center relative shrink-0 text-[#0f0935] text-[20px] w-full">
        <p className="block leading-[normal]">
          Learn to budget like Barefoot and build wealth like a Buffet.
        </p>
      </div>
    </div>
  );
}

function SlideText() {
  return (
    <div
      className="box-border content-stretch flex flex-col gap-[74px] items-center justify-center overflow-clip p-0 relative shrink-0 w-[450px]"
      data-name="Slide - Text"
    >
      <Frame1618873356 />
    </div>
  );
}

function SliderDot() {
  return (
    <div
      className="box-border content-stretch flex flex-row gap-1.5 items-center justify-start p-0 relative shrink-0"
      data-name="Slider - Dot"
    >
      <div className="bg-[#ee672d] h-1.5 rounded-[31px] shrink-0 w-4" />
      <div className="bg-[rgba(18,18,18,0.46)] rounded-[55px] shrink-0 size-1.5" />
      <div className="bg-[rgba(18,18,18,0.46)] rounded-[55px] shrink-0 size-1.5" />
    </div>
  );
}

function Frame1618873387() {
  return (
    <div className="box-border content-stretch flex flex-col gap-[74px] items-center justify-start overflow-clip p-[20px] relative shrink-0">
      <SlideText />
      <SliderDot />
    </div>
  );
}

function Frame1618873385() {
  return (
    <div
      className="absolute box-border content-stretch flex flex-col gap-[110px] items-center justify-start left-[196px] p-0 translate-y-[-50%]"
      style={{ top: "calc(50% + 0.473px)" }}
    >
      <UbrandLogo />
      <Frame1618873387 />
    </div>
  );
}

export default function SignUp() {
  return (
    <div className="relative size-full" data-name="Sign up">
      <Component2 />
      <Frame1618873380 />
      <Frame1618873385 />
    </div>
  );
}