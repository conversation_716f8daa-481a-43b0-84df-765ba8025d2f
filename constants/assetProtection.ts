import { AssetCategoryInfo, ProtectionGuideline, AssetProtectionEducation } from '../types/assetProtection';

export const assetCategories: AssetCategoryInfo[] = [
  {
    id: 'real_estate',
    name: 'Real Estate',
    description: 'Property investments including primary residence, investment properties, and land',
    protectionMethods: ['Family Trust', 'Corporate Ownership', 'Joint Tenancy', 'Insurance'],
    commonRisks: ['Legal claims', 'Market downturns', 'Natural disasters', 'Creditor action'],
    icon: 'Home',
    color: '#3B82F6',
    examples: ['Family home', 'Investment property', 'Commercial property', 'Vacant land']
  },
  {
    id: 'vehicles',
    name: 'Vehicles',
    description: 'Cars, motorcycles, boats, planes, and other transportation assets',
    protectionMethods: ['Comprehensive Insurance', 'Asset Finance', 'Corporate Ownership'],
    commonRisks: ['Accidents', 'Theft', 'Depreciation', 'Liability claims'],
    icon: 'Car',
    color: '#10B981',
    examples: ['Family car', 'Investment vehicle', 'Boat', 'Motorcycle', 'RV']
  },
  {
    id: 'investments',
    name: 'Financial Investments',
    description: 'Shares, bonds, managed funds, and other financial instruments',
    protectionMethods: ['Diversification', 'Trust Structures', 'Corporate Wrapper', 'SMSF'],
    commonRisks: ['Market volatility', 'Company failure', 'Creditor claims', 'Tax implications'],
    icon: 'TrendingUp',
    color: '#8B5CF6',
    examples: ['Share portfolio', 'Managed funds', 'Bonds', 'ETFs', 'Term deposits']
  },
  {
    id: 'business_assets',
    name: 'Business Assets',
    description: 'Business equipment, inventory, intellectual property, and goodwill',
    protectionMethods: ['Corporate Structure', 'Asset Protection Trust', 'Insurance', 'Leasing'],
    commonRisks: ['Business failure', 'Legal action', 'Key person risk', 'Economic downturns'],
    icon: 'Briefcase',
    color: '#F59E0B',
    examples: ['Business equipment', 'Inventory', 'Customer lists', 'Goodwill', 'Licenses']
  },
  {
    id: 'personal_property',
    name: 'Personal Property',
    description: 'Furniture, artwork, antiques, and valuable personal belongings',
    protectionMethods: ['Contents Insurance', 'Separate Storage', 'Documentation', 'Security'],
    commonRisks: ['Theft', 'Damage', 'Loss', 'Depreciation'],
    icon: 'Package',
    color: '#EF4444',
    examples: ['Furniture', 'Artwork', 'Antiques', 'Musical instruments', 'Sports equipment']
  },
  {
    id: 'intellectual_property',
    name: 'Intellectual Property',
    description: 'Patents, trademarks, copyrights, and trade secrets',
    protectionMethods: ['Legal Registration', 'Licensing', 'Corporate Ownership', 'Confidentiality'],
    commonRisks: ['Infringement', 'Expiry', 'Competition', 'Technology changes'],
    icon: 'Lightbulb',
    color: '#06B6D4',
    examples: ['Patents', 'Trademarks', 'Copyrights', 'Software', 'Trade secrets']
  },
  {
    id: 'cash_deposits',
    name: 'Cash & Deposits',
    description: 'Bank accounts, term deposits, and cash holdings',
    protectionMethods: ['Government Guarantee', 'Bank Diversification', 'Joint Accounts', 'Trust'],
    commonRisks: ['Bank failure', 'Inflation', 'Creditor claims', 'Currency risk'],
    icon: 'DollarSign',
    color: '#84CC16',
    examples: ['Savings account', 'Term deposits', 'Cash management', 'Foreign currency']
  },
  {
    id: 'collectibles',
    name: 'Collectibles',
    description: 'Art, coins, stamps, wine, and other collectible items',
    protectionMethods: ['Specialist Insurance', 'Secure Storage', 'Professional Valuation', 'Documentation'],
    commonRisks: ['Market changes', 'Damage', 'Theft', 'Authentication issues'],
    icon: 'Star',
    color: '#EC4899',
    examples: ['Art collection', 'Rare coins', 'Wine cellar', 'Stamps', 'Vintage items']
  },
  {
    id: 'jewelry',
    name: 'Jewelry & Valuables',
    description: 'Precious jewelry, watches, and valuable accessories',
    protectionMethods: ['Specialist Insurance', 'Bank Vault', 'Home Safe', 'Regular Valuation'],
    commonRisks: ['Theft', 'Loss', 'Damage', 'Market fluctuation'],
    icon: 'Gem',
    color: '#F97316',
    examples: ['Diamond rings', 'Gold jewelry', 'Luxury watches', 'Precious stones']
  },
  {
    id: 'electronics',
    name: 'Electronics & Technology',
    description: 'Computers, phones, cameras, and electronic equipment',
    protectionMethods: ['Technology Insurance', 'Backup Systems', 'Security Software', 'Warranties'],
    commonRisks: ['Obsolescence', 'Damage', 'Theft', 'Data loss'],
    icon: 'Smartphone',
    color: '#6366F1',
    examples: ['Computers', 'Smartphones', 'Cameras', 'Audio equipment', 'Gaming systems']
  }
];

export const protectionGuidelines: ProtectionGuideline[] = [
  {
    category: 'real_estate',
    title: 'Real Estate Protection Strategies',
    description: 'Protect your property investments from legal claims and market risks',
    methods: [
      {
        name: 'Family Discretionary Trust',
        description: 'Hold property in a trust structure to protect from personal creditors',
        suitability: ['High net worth', 'Business owners', 'Professionals'],
        cost: 'high',
        effectiveness: 'high',
        complexity: 'complex'
      },
      {
        name: 'Joint Tenancy',
        description: 'Own property jointly with spouse for automatic inheritance',
        suitability: ['Married couples', 'Simple structures'],
        cost: 'low',
        effectiveness: 'medium',
        complexity: 'simple'
      },
      {
        name: 'Comprehensive Insurance',
        description: 'Protect against damage, theft, and liability claims',
        suitability: ['All property owners'],
        cost: 'medium',
        effectiveness: 'high',
        complexity: 'simple'
      }
    ],
    riskFactors: [
      'Personal liability claims',
      'Business creditor action',
      'Relationship breakdown',
      'Professional indemnity claims'
    ],
    tips: [
      'Consider trust structures before purchasing',
      'Review ownership regularly',
      'Ensure adequate insurance coverage',
      'Document all improvements and expenses'
    ]
  },
  {
    category: 'business_assets',
    title: 'Business Asset Protection',
    description: 'Shield business assets from personal and business risks',
    methods: [
      {
        name: 'Corporate Structure',
        description: 'Use company structure to separate business and personal assets',
        suitability: ['Active businesses', 'High risk industries'],
        cost: 'medium',
        effectiveness: 'high',
        complexity: 'moderate'
      },
      {
        name: 'Asset Protection Trust',
        description: 'Hold business assets in protective trust structure',
        suitability: ['Valuable assets', 'High risk exposure'],
        cost: 'high',
        effectiveness: 'high',
        complexity: 'complex'
      }
    ],
    riskFactors: [
      'Business failure',
      'Professional liability',
      'Key person dependency',
      'Industry-specific risks'
    ],
    tips: [
      'Separate high-risk and low-risk assets',
      'Use appropriate business structures',
      'Maintain proper corporate records',
      'Regular risk assessments'
    ]
  }
];

export const assetProtectionEducation: AssetProtectionEducation = {
  title: 'Asset Protection: Safeguard Your Wealth',
  description: 'Asset protection is about legally structuring your affairs to protect wealth from potential creditors, legal claims, and unforeseen risks while maintaining control and flexibility over your assets.',
  keyPrinciples: [
    'Early Planning: Protection strategies work best when implemented before problems arise',
    'Legal Compliance: All strategies must be legal and comply with relevant laws',
    'Genuine Purpose: Structures should have legitimate business or investment purposes',
    'Professional Advice: Complex strategies require professional legal and tax advice',
    'Balance: Balance protection with accessibility and tax efficiency'
  ],
  commonStrategies: [
    'Trust Structures: Family trusts, unit trusts, and hybrid trusts',
    'Corporate Ownership: Companies to hold assets and limit liability',
    'Insurance: Comprehensive coverage for various risks and liabilities',
    'Diversification: Spread assets across different jurisdictions and structures',
    'Joint Ownership: Strategic ownership arrangements with family members'
  ],
  warningFlags: [
    'Transferring assets after legal action has commenced',
    'Using structures solely to avoid legitimate debts',
    'Hiding assets from creditors or authorities',
    'Creating structures without professional advice',
    'Ignoring tax consequences of protection strategies'
  ],
  professionalAdvice: [
    'Seek legal advice before implementing protection strategies',
    'Ensure tax compliance with all structures',
    'Regular review of protection arrangements',
    'Document legitimate reasons for all structures',
    'Consider ongoing costs and complexity'
  ]
};

export const riskLevels = [
  { id: 'low', label: 'Low Risk', color: '#10B981', description: 'Generally stable assets with minimal exposure' },
  { id: 'medium', label: 'Medium Risk', color: '#F59E0B', description: 'Some exposure to market or legal risks' },
  { id: 'high', label: 'High Risk', color: '#EF4444', description: 'Significant exposure requiring active protection' }
];

export const protectionStatusOptions = [
  { id: true, label: 'Protected', color: '#10B981', icon: 'Shield' },
  { id: false, label: 'Not Protected', color: '#EF4444', icon: 'AlertTriangle' }
];

export const commonProtectionMethods = [
  'Family Trust',
  'Corporate Ownership',
  'Insurance Coverage',
  'Joint Ownership',
  'Asset Finance',
  'Professional Structure',
  'Diversification',
  'Legal Documentation',
  'Separate Entity',
  'Other'
];