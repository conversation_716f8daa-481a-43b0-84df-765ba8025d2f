import { EstatePlanningEducation, EstateComponent, WillGuidelines, ChecklistItem } from '../types/estatePlanning';

export const estatePlanningEducation: EstatePlanningEducation = {
  title: 'Estate Planning: Secure Your Legacy',
  description: 'Estate planning ensures your assets are distributed according to your wishes, protects your family, and minimizes tax implications while providing clear instructions for when you cannot make decisions yourself.',
  keyComponents: [
    {
      name: 'Will',
      description: 'Legal document specifying how your assets should be distributed after death',
      importance: 'essential',
      whoNeedsIt: ['Everyone over 18', 'Property owners', 'Parents', 'Business owners'],
      timeframe: '2-4 weeks',
      cost: 'low',
      complexity: 'simple'
    },
    {
      name: 'Power of Attorney',
      description: 'Authorizes someone to make financial and medical decisions if you become incapacitated',
      importance: 'essential',
      whoNeedsIt: ['Adults of all ages', 'Property owners', 'Business owners'],
      timeframe: '1-2 weeks',
      cost: 'low',
      complexity: 'simple'
    },
    {
      name: 'Testamentary Trust',
      description: 'Trust created through your will to protect assets and provide ongoing support',
      importance: 'important',
      whoNeedsIt: ['High net worth individuals', 'Parents with young children', 'Blended families'],
      timeframe: '4-8 weeks',
      cost: 'medium',
      complexity: 'moderate'
    },
    {
      name: 'Binding Death Benefit Nomination',
      description: 'Directs superannuation benefits to specific beneficiaries',
      importance: 'important',
      whoNeedsIt: ['Anyone with superannuation', 'Those with dependents'],
      timeframe: '1-2 weeks',
      cost: 'low',
      complexity: 'simple'
    },
    {
      name: 'Business Succession Plan',
      description: 'Plan for transfer or sale of business interests upon death or incapacity',
      importance: 'essential',
      whoNeedsIt: ['Business owners', 'Partners in business'],
      timeframe: '8-12 weeks',
      cost: 'high',
      complexity: 'complex'
    },
    {
      name: 'Asset Protection Structures',
      description: 'Trusts and structures to protect assets from creditors and tax',
      importance: 'important',
      whoNeedsIt: ['High net worth individuals', 'Business owners', 'Professionals'],
      timeframe: '6-12 weeks',
      cost: 'high',
      complexity: 'complex'
    }
  ],
  importanceFactors: [
    'Ensures your wishes are followed after death',
    'Protects your family from financial hardship',
    'Minimizes estate taxes and legal complications',
    'Provides for incapacity planning',
    'Protects minor children and dependents',
    'Maintains family harmony and reduces disputes',
    'Supports charitable giving goals',
    'Protects business continuity'
  ],
  commonMistakes: [
    'Not having a will at all',
    'Using DIY will kits for complex situations',
    'Not updating will after major life events',
    'Failing to name backup executors',
    'Not coordinating with superannuation nominations',
    'Ignoring tax implications',
    'Not informing family about arrangements',
    'Storing will in inaccessible location'
  ],
  reviewFrequency: [
    'After marriage, divorce, or relationship changes',
    'After birth or adoption of children',
    'After significant changes in assets or income',
    'After death of beneficiaries or executors',
    'When moving to different state or country',
    'At least every 3-5 years as a routine review',
    'After starting or selling a business',
    'When tax laws change significantly'
  ],
  professionalHelp: [
    'Estate planning lawyer for complex situations',
    'Tax advisor for tax-efficient structures',
    'Financial planner for wealth management',
    'Accountant for business succession planning',
    'Insurance advisor for life insurance needs',
    'Trustee services for ongoing management'
  ]
};

export const willGuidelines: WillGuidelines = {
  basicRequirements: [
    'Must be in writing (typed or handwritten)',
    'Must be signed by you in the presence of two witnesses',
    'Witnesses must be over 18 and not beneficiaries',
    'Must clearly identify you and state it is your will',
    'Must appoint an executor to manage your estate',
    'Should be dated to ensure it supersedes previous wills',
    'Must be signed by witnesses in your presence and each other\'s presence',
    'Should revoke all previous wills explicitly'
  ],
  commonMistakes: [
    'Using witnesses who are also beneficiaries',
    'Not signing in proper sequence with witnesses',
    'Leaving ambiguous or unclear instructions',
    'Not accounting for all assets and debts',
    'Failing to consider tax implications',
    'Not providing for young children adequately',
    'Using outdated or incorrect legal language',
    'Not storing the original in a safe place'
  ],
  whenToUpdate: [
    'Marriage or entering a de facto relationship',
    'Divorce or end of relationship',
    'Birth or adoption of children',
    'Death of beneficiaries, executors, or guardians',
    'Significant changes in financial circumstances',
    'Purchase of major assets (property, business)',
    'Moving to a different state or country',
    'Changes in family relationships or circumstances'
  ],
  professionalAdvice: [
    'Complex family situations (blended families, estranged children)',
    'Significant assets requiring tax planning',
    'Business ownership requiring succession planning',
    'International assets or beneficiaries',
    'Charitable giving intentions',
    'Special needs beneficiaries',
    'Disputes or challenges expected from family',
    'Assets in multiple jurisdictions'
  ]
};

export const defaultChecklistItems: ChecklistItem[] = [
  {
    id: '1',
    title: 'Draft a Valid Will',
    description: 'Create a legally valid will that clearly states your wishes for asset distribution',
    category: 'will',
    isCompleted: false,
    priority: 'high',
    notes: ''
  },
  {
    id: '2',
    title: 'Appoint Executor and Backup',
    description: 'Choose a trusted person to execute your will and a backup executor',
    category: 'will',
    isCompleted: false,
    priority: 'high',
    notes: ''
  },
  {
    id: '3',
    title: 'Name Guardians for Minor Children',
    description: 'If you have children under 18, appoint guardians to care for them',
    category: 'will',
    isCompleted: false,
    priority: 'high',
    notes: ''
  },
  {
    id: '4',
    title: 'Create Financial Power of Attorney',
    description: 'Appoint someone to make financial decisions if you become incapacitated',
    category: 'power_of_attorney',
    isCompleted: false,
    priority: 'high',
    notes: ''
  },
  {
    id: '5',
    title: 'Create Medical Power of Attorney',
    description: 'Appoint someone to make medical decisions if you cannot',
    category: 'power_of_attorney',
    isCompleted: false,
    priority: 'high',
    notes: ''
  },
  {
    id: '6',
    title: 'Update Superannuation Beneficiaries',
    description: 'Ensure your super fund has current binding death benefit nominations',
    category: 'beneficiaries',
    isCompleted: false,
    priority: 'high',
    notes: ''
  },
  {
    id: '7',
    title: 'Review Life Insurance Beneficiaries',
    description: 'Update beneficiaries on all life insurance policies',
    category: 'beneficiaries',
    isCompleted: false,
    priority: 'medium',
    notes: ''
  },
  {
    id: '8',
    title: 'Create Asset Inventory',
    description: 'Document all assets, debts, and important account information',
    category: 'assets',
    isCompleted: false,
    priority: 'medium',
    notes: ''
  },
  {
    id: '9',
    title: 'Consider Tax Implications',
    description: 'Review potential estate taxes and consider tax-efficient strategies',
    category: 'tax',
    isCompleted: false,
    priority: 'medium',
    notes: ''
  },
  {
    id: '10',
    title: 'Store Documents Safely',
    description: 'Ensure will and other documents are stored securely and family knows location',
    category: 'assets',
    isCompleted: false,
    priority: 'medium',
    notes: ''
  },
  {
    id: '11',
    title: 'Inform Key People',
    description: 'Tell your executor, family, and lawyer about your estate plan',
    category: 'review',
    isCompleted: false,
    priority: 'medium',
    notes: ''
  },
  {
    id: '12',
    title: 'Schedule Regular Reviews',
    description: 'Set reminders to review your estate plan every 3-5 years',
    category: 'review',
    isCompleted: false,
    priority: 'low',
    notes: ''
  }
];

export const willTypes = [
  {
    id: 'simple',
    name: 'Simple Will',
    description: 'Basic will for straightforward situations',
    suitableFor: ['Single assets', 'Simple family structures', 'Small estates'],
    cost: 'Low ($200-$800)',
    complexity: 'Simple'
  },
  {
    id: 'testamentary_trust',
    name: 'Will with Testamentary Trust',
    description: 'Will that creates trusts for beneficiaries',
    suitableFor: ['Young children', 'Tax planning', 'Asset protection'],
    cost: 'Medium ($1,000-$3,000)',
    complexity: 'Moderate'
  },
  {
    id: 'complex',
    name: 'Complex Will',
    description: 'Comprehensive will for complex situations',
    suitableFor: ['Business ownership', 'Multiple properties', 'Blended families'],
    cost: 'High ($2,000-$10,000+)',
    complexity: 'Complex'
  }
];

export const documentTypes = [
  { id: 'will', name: 'Will', description: 'Your main will document' },
  { id: 'power_of_attorney', name: 'Power of Attorney', description: 'Financial and medical POA documents' },
  { id: 'beneficiary_form', name: 'Beneficiary Forms', description: 'Superannuation and insurance nominations' },
  { id: 'trust_document', name: 'Trust Documents', description: 'Testamentary or family trust deeds' },
  { id: 'other', name: 'Other', description: 'Other estate planning documents' }
];

export const relationshipTypes = [
  'Spouse/Partner',
  'Child',
  'Parent',
  'Sibling',
  'Grandchild',
  'Grandparent',
  'Uncle/Aunt',
  'Cousin',
  'Friend',
  'Charity',
  'Other'
];

export const priorityLevels = [
  { id: 'high', name: 'High Priority', color: '#EF4444', description: 'Critical items that should be completed immediately' },
  { id: 'medium', name: 'Medium Priority', color: '#F59E0B', description: 'Important items to complete within 3 months' },
  { id: 'low', name: 'Low Priority', color: '#10B981', description: 'Items to complete when convenient' }
];

export const categories = [
  { id: 'will', name: 'Will & Testament', icon: 'FileText', color: '#3B82F6' },
  { id: 'power_of_attorney', name: 'Power of Attorney', icon: 'Users', color: '#8B5CF6' },
  { id: 'beneficiaries', name: 'Beneficiaries', icon: 'Heart', color: '#EF4444' },
  { id: 'assets', name: 'Assets & Documents', icon: 'Folder', color: '#F59E0B' },
  { id: 'tax', name: 'Tax Planning', icon: 'Calculator', color: '#10B981' },
  { id: 'review', name: 'Review & Updates', icon: 'RefreshCw', color: '#6B7280' }
];