import { InsuranceTypeInfo, InsuranceEducationContent } from '../types/insurance';

export const insuranceTypes: InsuranceTypeInfo[] = [
  {
    id: 'life',
    name: 'Life Insurance',
    description: 'Provides financial support to your family in the event of your death',
    importance: 'essential',
    averageCost: '$50-200/month',
    keyBenefits: [
      'Financial security for dependents',
      'Mortgage protection',
      'Funeral and estate costs coverage',
      'Income replacement for family'
    ],
    color: '#EF4444',
    icon: 'Heart'
  },
  {
    id: 'tpd',
    name: 'Total & Permanent Disability (TPD)',
    description: 'Covers you if you become permanently disabled and unable to work',
    importance: 'essential',
    averageCost: '$30-150/month',
    keyBenefits: [
      'Lump sum payment for disability',
      'Medical expenses coverage',
      'Home modification costs',
      'Ongoing care expenses'
    ],
    color: '#F59E0B',
    icon: 'Shield'
  },
  {
    id: 'income_protection',
    name: 'Income Protection',
    description: 'Replaces up to 75% of your income if you cannot work due to illness or injury',
    importance: 'essential',
    averageCost: '$40-120/month',
    keyBenefits: [
      'Monthly income replacement',
      'Short and long-term coverage',
      'Medical expenses support',
      'Rehabilitation costs'
    ],
    color: '#10B981',
    icon: 'DollarSign'
  },
  {
    id: 'health',
    name: 'Private Health Insurance',
    description: 'Covers private hospital and medical treatments beyond Medicare',
    importance: 'recommended',
    averageCost: '$100-300/month',
    keyBenefits: [
      'Private hospital coverage',
      'Dental and optical',
      'Physiotherapy and allied health',
      'No waiting lists'
    ],
    color: '#3B82F6',
    icon: 'Plus'
  },
  {
    id: 'home_building',
    name: 'Home & Building Insurance',
    description: 'Protects your home structure against damage from fire, storm, theft',
    importance: 'essential',
    averageCost: '$80-250/month',
    keyBenefits: [
      'Building damage coverage',
      'Natural disaster protection',
      'Temporary accommodation',
      'Rebuild costs coverage'
    ],
    color: '#8B5CF6',
    icon: 'Home'
  },
  {
    id: 'home_contents',
    name: 'Contents Insurance',
    description: 'Covers your personal belongings inside your home',
    importance: 'recommended',
    averageCost: '$30-80/month',
    keyBenefits: [
      'Personal belongings coverage',
      'Theft protection',
      'Accidental damage',
      'Temporary accommodation'
    ],
    color: '#06B6D4',
    icon: 'Package'
  },
  {
    id: 'car',
    name: 'Car Insurance',
    description: 'Protects your vehicle and covers third-party liability',
    importance: 'essential',
    averageCost: '$60-200/month',
    keyBenefits: [
      'Vehicle damage coverage',
      'Third-party liability',
      'Theft protection',
      'Roadside assistance'
    ],
    color: '#EC4899',
    icon: 'Car'
  },
  {
    id: 'travel',
    name: 'Travel Insurance',
    description: 'Covers medical emergencies, trip cancellations, and lost luggage when traveling',
    importance: 'recommended',
    averageCost: '$50-150/trip',
    keyBenefits: [
      'Medical emergency coverage',
      'Trip cancellation protection',
      'Lost luggage compensation',
      'Emergency evacuation'
    ],
    color: '#F97316',
    icon: 'Plane'
  },
  {
    id: 'professional_indemnity',
    name: 'Professional Indemnity',
    description: 'Protects professionals against claims of negligence or breach of duty',
    importance: 'optional',
    averageCost: '$100-500/month',
    keyBenefits: [
      'Professional liability coverage',
      'Legal defense costs',
      'Client compensation',
      'Reputation protection'
    ],
    color: '#84CC16',
    icon: 'Briefcase'
  },
  {
    id: 'public_liability',
    name: 'Public Liability',
    description: 'Covers compensation claims if you accidentally injure someone or damage property',
    importance: 'optional',
    averageCost: '$50-200/month',
    keyBenefits: [
      'Third-party injury coverage',
      'Property damage protection',
      'Legal costs coverage',
      'Business protection'
    ],
    color: '#06B6D4',
    icon: 'Users'
  }
];

export const popularInsuranceCompanies = [
  'Allianz',
  'AMP',
  'ANZ',
  'AIA Australia',
  'Budget Direct',
  'CGU',
  'CommInsure',
  'GIO',
  'HCF',
  'IAG',
  'Medibank',
  'NRMA',
  'QBE',
  'RAC',
  'RACV',
  'SGIO',
  'TAL',
  'Westpac',
  'Zurich',
  'Other'
];

export const insuranceEducationContent: InsuranceEducationContent = {
  title: 'Insurance: Your Financial Safety Net',
  description: 'Insurance protects your wealth and provides peace of mind. It\'s the foundation of Stage 3: Protect - ensuring that life\'s unexpected events don\'t derail your financial journey.',
  keyPoints: [
    'Insurance transfers financial risk from you to the insurance company',
    'The right coverage protects your family, assets, and income',
    'Insurance inside super can be tax-effective but may have limitations',
    'Regular reviews ensure your coverage keeps pace with your life changes',
    'Prevention is better than cure - but insurance is your backup plan'
  ],
  tips: [
    'Review your insurance annually or after major life events',
    'Compare policies from multiple providers to get the best value',
    'Consider increasing coverage as your assets and income grow',
    'Keep all policy documents in a safe, accessible place',
    'Understand what\'s covered and what\'s excluded in each policy',
    'Set up automatic premium payments to avoid policy lapses'
  ]
};

export const coverageRecommendations = {
  life: {
    rule: 'Income x 5-10 years',
    factors: ['Dependents', 'Mortgage', 'Lifestyle maintenance', 'Education costs']
  },
  tpd: {
    rule: 'Income x 3-5 years',
    factors: ['Medical costs', 'Home modifications', 'Ongoing care', 'Lost income']
  },
  incomeProtection: {
    rule: '75% of gross income',
    factors: ['Monthly expenses', 'Benefit period', 'Waiting period', 'Occupation']
  },
  home: {
    rule: 'Full replacement value',
    factors: ['Rebuild costs', 'Land value exclusion', 'Contents value', 'Location risks']
  }
};

export const renewalReminderPeriods = [
  { value: 30, label: '30 days before' },
  { value: 60, label: '60 days before' },
  { value: 90, label: '90 days before' }
];

export const documentTypes = [
  { id: 'policy', name: 'Policy Document', icon: 'FileText' },
  { id: 'certificate', name: 'Certificate of Insurance', icon: 'Award' },
  { id: 'claim_form', name: 'Claim Form', icon: 'ClipboardList' },
  { id: 'receipt', name: 'Premium Receipt', icon: 'Receipt' },
  { id: 'correspondence', name: 'Correspondence', icon: 'Mail' },
  { id: 'other', name: 'Other Document', icon: 'File' }
];