import { InsurancePolicy, RecommendedCoverage } from '../types/insurance';

export const calculateMonthlyPremium = (amount: number, frequency: string): number => {
  switch (frequency) {
    case 'weekly': return amount * 4.33;
    case 'quarterly': return amount / 3;
    case 'yearly': return amount / 12;
    default: return amount; // monthly
  }
};

export const calculateTotalPremiums = (policies: InsurancePolicy[]): number => {
  return policies.reduce((total, policy) => {
    if (!policy.isActive) return total;
    return total + calculateMonthlyPremium(policy.premiumAmount, policy.premiumFrequency);
  }, 0);
};

export const calculateTotalCoverage = (policies: InsurancePolicy[]): number => {
  return policies.reduce((total, policy) => {
    if (!policy.isActive) return total;
    return total + policy.amountInsured;
  }, 0);
};

export const getUpcomingRenewals = (policies: InsurancePolicy[], daysAhead: number = 60): InsurancePolicy[] => {
  const cutoffDate = new Date();
  cutoffDate.setDate(cutoffDate.getDate() + daysAhead);
  
  return policies.filter(policy => {
    if (!policy.isActive) return false;
    const renewalDate = new Date(policy.renewalDate);
    return renewalDate <= cutoffDate && renewalDate >= new Date();
  }).sort((a, b) => new Date(a.renewalDate).getTime() - new Date(b.renewalDate).getTime());
};

export const calculateRecommendedCoverage = (
  monthlyIncome: number,
  assets: number,
  dependents: number,
  age: number
): RecommendedCoverage => {
  const annualIncome = monthlyIncome * 12;
  
  // Life insurance: 5-10x annual income based on dependents and age
  const lifeMultiplier = dependents > 0 ? (age < 40 ? 10 : age < 50 ? 8 : 6) : 3;
  const lifeInsurance = annualIncome * lifeMultiplier;
  
  // TPD: 3-5x annual income
  const tpdMultiplier = age < 40 ? 5 : age < 50 ? 4 : 3;
  const tpdInsurance = annualIncome * tpdMultiplier;
  
  // Income protection: 75% of gross income
  const incomeProtection = monthlyIncome * 0.75;
  
  // Home insurance: Based on assets (simplified)
  const homeInsurance = Math.max(assets * 0.3, 300000); // Assume 30% of assets is property value
  
  // Car insurance: Simplified calculation
  const carInsurance = Math.min(assets * 0.05, 80000); // Assume 5% of assets in vehicles, max $80k
  
  return {
    lifeInsurance,
    tpdInsurance,
    incomeProtection,
    homeInsurance,
    carInsurance
  };
};

export const analyzeCoverageGaps = (
  policies: InsurancePolicy[],
  recommended: RecommendedCoverage,
  monthlyIncome: number
): string[] => {
  const gaps: string[] = [];
  const activePolicies = policies.filter(p => p.isActive);
  
  // Check life insurance
  const lifePolicy = activePolicies.find(p => p.type === 'life');
  if (!lifePolicy) {
    gaps.push('No life insurance coverage detected');
  } else if (lifePolicy.amountInsured < recommended.lifeInsurance * 0.5) {
    gaps.push('Life insurance coverage may be insufficient');
  }
  
  // Check TPD insurance
  const tpdPolicy = activePolicies.find(p => p.type === 'tpd');
  if (!tpdPolicy) {
    gaps.push('No Total & Permanent Disability insurance');
  } else if (tpdPolicy.amountInsured < recommended.tpdInsurance * 0.5) {
    gaps.push('TPD insurance coverage may be insufficient');
  }
  
  // Check income protection
  const incomePolicy = activePolicies.find(p => p.type === 'income_protection');
  if (!incomePolicy) {
    gaps.push('No income protection insurance');
  } else if (incomePolicy.amountInsured < recommended.incomeProtection * 0.5) {
    gaps.push('Income protection coverage may be insufficient');
  }
  
  // Check home insurance
  const homePolicy = activePolicies.find(p => p.type === 'home_building' || p.type === 'home_contents');
  if (!homePolicy) {
    gaps.push('No home or contents insurance detected');
  }
  
  // Check car insurance
  const carPolicy = activePolicies.find(p => p.type === 'car');
  if (!carPolicy) {
    gaps.push('No car insurance detected');
  }
  
  // Check health insurance
  const healthPolicy = activePolicies.find(p => p.type === 'health');
  if (!healthPolicy && monthlyIncome > 7500) { // High earners should consider private health
    gaps.push('Consider private health insurance for higher income earners');
  }
  
  return gaps;
};

export const formatAmount = (amount: number): string => `$${amount.toLocaleString()}`;

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-AU', { 
    day: 'numeric', 
    month: 'short', 
    year: 'numeric' 
  });
};

export const getDaysUntilRenewal = (renewalDate: string): number => {
  const renewal = new Date(renewalDate);
  const today = new Date();
  const diffTime = renewal.getTime() - today.getTime();
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

export const getInsurancePriority = (type: string): number => {
  const priorities: Record<string, number> = {
    'life': 1,
    'tpd': 2,
    'income_protection': 3,
    'car': 4,
    'home_building': 5,
    'health': 6,
    'home_contents': 7,
    'travel': 8,
    'professional_indemnity': 9,
    'public_liability': 10,
    'business': 11,
    'cyber': 12,
    'other': 13
  };
  return priorities[type] || 99;
};

export const isRenewalUpcoming = (renewalDate: string, daysThreshold: number = 30): boolean => {
  const days = getDaysUntilRenewal(renewalDate);
  return days <= daysThreshold && days >= 0;
};

export const getInsuranceHealth = (
  policies: InsurancePolicy[],
  recommended: RecommendedCoverage
): { score: number; status: 'excellent' | 'good' | 'fair' | 'poor'; message: string } => {
  const activePolicies = policies.filter(p => p.isActive);
  const essentialTypes = ['life', 'tpd', 'income_protection', 'car'];
  
  let score = 0;
  let maxScore = 0;
  
  essentialTypes.forEach(type => {
    maxScore += 25;
    const policy = activePolicies.find(p => p.type === type);
    if (policy) {
      score += 15; // Base points for having the insurance
      
      // Additional points based on coverage adequacy
      const recommended_amount = type === 'life' ? recommended.lifeInsurance :
                                type === 'tpd' ? recommended.tpdInsurance :
                                type === 'income_protection' ? recommended.incomeProtection :
                                recommended.carInsurance;
      
      if (policy.amountInsured >= recommended_amount * 0.8) {
        score += 10; // Adequate coverage
      } else if (policy.amountInsured >= recommended_amount * 0.5) {
        score += 5; // Partial coverage
      }
    }
  });
  
  const percentage = (score / maxScore) * 100;
  
  if (percentage >= 80) {
    return { score: percentage, status: 'excellent', message: 'Excellent insurance coverage!' };
  } else if (percentage >= 60) {
    return { score: percentage, status: 'good', message: 'Good coverage with room for improvement' };
  } else if (percentage >= 40) {
    return { score: percentage, status: 'fair', message: 'Fair coverage, consider additional protection' };
  } else {
    return { score: percentage, status: 'poor', message: 'Poor coverage, urgent attention needed' };
  }
};