import { EstatePlanningData, ChecklistItem, Will, PowerOfAttorney } from '../types/estatePlanning';

export const calculateCompletionScore = (checklistItems: ChecklistItem[]): number => {
  if (checklistItems.length === 0) return 0;
  
  const completedItems = checklistItems.filter(item => item.isCompleted);
  const totalScore = checklistItems.reduce((score, item) => {
    const weight = item.priority === 'high' ? 3 : item.priority === 'medium' ? 2 : 1;
    return score + weight;
  }, 0);
  
  const completedScore = completedItems.reduce((score, item) => {
    const weight = item.priority === 'high' ? 3 : item.priority === 'medium' ? 2 : 1;
    return score + weight;
  }, 0);
  
  return totalScore > 0 ? Math.round((completedScore / totalScore) * 100) : 0;
};

export const calculateEstatePlanningData = (
  will: Will,
  powerOfAttorney: PowerOfAttorney,
  checklistItems: ChecklistItem[],
  totalEstateValue: number
): EstatePlanningData => {
  const completionScore = calculateCompletionScore(checklistItems);
  const hasCompletedChecklist = completionScore >= 80;
  const recommendations = generateRecommendations(will, powerOfAttorney, checklistItems, completionScore);
  const nextReviewDate = calculateNextReviewDate(will);
  const professionalAdvice = shouldSeekProfessionalAdvice(totalEstateValue, checklistItems);

  return {
    will,
    powerOfAttorney,
    beneficiaries: [], // This would be populated from actual data
    totalEstateValue,
    hasCompletedChecklist,
    checklistItems,
    completionScore,
    recommendations,
    nextReviewDate,
    professionalAdvice
  };
};

export const generateRecommendations = (
  will: Will,
  powerOfAttorney: PowerOfAttorney,
  checklistItems: ChecklistItem[],
  completionScore: number
): string[] => {
  const recommendations: string[] = [];

  // Will-related recommendations
  if (!will.hasWill) {
    recommendations.push('Create a valid will immediately - this is essential for everyone over 18');
  } else if (will.lastUpdated) {
    const lastUpdate = new Date(will.lastUpdated);
    const threeYearsAgo = new Date();
    threeYearsAgo.setFullYear(threeYearsAgo.getFullYear() - 3);
    
    if (lastUpdate < threeYearsAgo) {
      recommendations.push('Review and update your will - it has been more than 3 years since last update');
    }
  }

  // Power of Attorney recommendations
  if (!powerOfAttorney.hasFinancialPOA) {
    recommendations.push('Create a financial power of attorney to protect against incapacity');
  }
  
  if (!powerOfAttorney.hasMedicalPOA) {
    recommendations.push('Create a medical power of attorney for healthcare decisions');
  }

  // High priority incomplete items
  const highPriorityIncomplete = checklistItems.filter(
    item => item.priority === 'high' && !item.isCompleted
  );
  
  if (highPriorityIncomplete.length > 0) {
    recommendations.push(`Complete ${highPriorityIncomplete.length} high-priority estate planning task${highPriorityIncomplete.length !== 1 ? 's' : ''}`);
  }

  // Completion score recommendations
  if (completionScore < 50) {
    recommendations.push('Focus on completing essential estate planning documents first');
  } else if (completionScore < 80) {
    recommendations.push('You\'re making good progress - focus on medium priority items next');
  } else if (completionScore >= 80) {
    recommendations.push('Excellent progress! Schedule regular reviews to keep your plan current');
  }

  // Document verification
  if (will.hasWill && !will.isVerified) {
    recommendations.push('Have your will reviewed by a qualified lawyer to ensure it meets legal requirements');
  }

  // Backup and storage
  const storageItem = checklistItems.find(item => item.title.includes('Store Documents'));
  if (storageItem && !storageItem.isCompleted) {
    recommendations.push('Ensure your will and estate documents are stored safely and family knows the location');
  }

  return recommendations;
};

export const calculateNextReviewDate = (will: Will): string => {
  const today = new Date();
  let nextReview = new Date(today);
  
  if (will.lastUpdated) {
    const lastUpdate = new Date(will.lastUpdated);
    nextReview = new Date(lastUpdate);
    nextReview.setFullYear(nextReview.getFullYear() + 3);
    
    // If the calculated date is in the past, set it to next year
    if (nextReview < today) {
      nextReview = new Date(today);
      nextReview.setFullYear(nextReview.getFullYear() + 1);
    }
  } else {
    // If no will exists, recommend review in 6 months after creation
    nextReview.setMonth(nextReview.getMonth() + 6);
  }
  
  return nextReview.toISOString().split('T')[0];
};

export const shouldSeekProfessionalAdvice = (
  totalEstateValue: number,
  checklistItems: ChecklistItem[]
): boolean => {
  // High net worth individuals should seek professional advice
  if (totalEstateValue > 1000000) {
    return true;
  }
  
  // If many items are incomplete and complex, suggest professional help
  const highPriorityIncomplete = checklistItems.filter(
    item => item.priority === 'high' && !item.isCompleted
  ).length;
  
  const mediumPriorityIncomplete = checklistItems.filter(
    item => item.priority === 'medium' && !item.isCompleted
  ).length;
  
  if (highPriorityIncomplete > 3 || (highPriorityIncomplete + mediumPriorityIncomplete) > 6) {
    return true;
  }
  
  return false;
};

export const validateWillRequirements = (will: Will): string[] => {
  const errors: string[] = [];
  
  if (!will.hasWill) {
    return ['A will is required for estate planning'];
  }
  
  if (!will.executorName?.trim()) {
    errors.push('An executor must be appointed');
  }
  
  if (!will.witnessNames || will.witnessNames.length < 2) {
    errors.push('Two witnesses are required for a valid will');
  }
  
  if (!will.lastUpdated) {
    errors.push('Will creation date should be recorded');
  }
  
  return errors;
};

export const getEstatePlanningStatus = (completionScore: number): {
  status: 'not_started' | 'in_progress' | 'good' | 'excellent';
  message: string;
  color: string;
} => {
  if (completionScore === 0) {
    return {
      status: 'not_started',
      message: 'Estate planning not started',
      color: '#EF4444'
    };
  } else if (completionScore < 50) {
    return {
      status: 'in_progress',
      message: 'Estate planning in progress',
      color: '#F59E0B'
    };
  } else if (completionScore < 80) {
    return {
      status: 'good',
      message: 'Good estate planning progress',
      color: '#3B82F6'
    };
  } else {
    return {
      status: 'excellent',
      message: 'Excellent estate planning coverage',
      color: '#10B981'
    };
  }
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export const isValidFileType = (fileName: string): boolean => {
  const allowedExtensions = ['.pdf', '.doc', '.docx', '.jpg', '.jpeg', '.png'];
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  return allowedExtensions.includes(extension);
};

export const getFileTypeIcon = (fileName: string): string => {
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
  
  switch (extension) {
    case '.pdf':
      return '📄';
    case '.doc':
    case '.docx':
      return '📝';
    case '.jpg':
    case '.jpeg':
    case '.png':
      return '🖼️';
    default:
      return '📁';
  }
};

export const sortChecklistItems = (items: ChecklistItem[]): ChecklistItem[] => {
  return [...items].sort((a, b) => {
    // First sort by completion status (incomplete first)
    if (a.isCompleted !== b.isCompleted) {
      return a.isCompleted ? 1 : -1;
    }
    
    // Then sort by priority
    const priorityOrder = { high: 3, medium: 2, low: 1 };
    const aPriority = priorityOrder[a.priority];
    const bPriority = priorityOrder[b.priority];
    
    if (aPriority !== bPriority) {
      return bPriority - aPriority;
    }
    
    // Finally sort by category
    return a.category.localeCompare(b.category);
  });
};

export const getCompletionMessage = (completionScore: number): string => {
  if (completionScore === 0) {
    return "Start your estate planning journey today - every step matters for your family's future.";
  } else if (completionScore < 25) {
    return "You've taken the first steps! Focus on the high-priority items to protect your loved ones.";
  } else if (completionScore < 50) {
    return "Good progress! You're building a solid foundation for your estate plan.";
  } else if (completionScore < 75) {
    return "Great work! You're more than halfway to a comprehensive estate plan.";
  } else if (completionScore < 90) {
    return "Excellent progress! Just a few more items to complete your estate planning.";
  } else {
    return "Outstanding! Your estate plan is comprehensive and your family is well protected.";
  }
};

export const formatAmount = (amount: number): string => {
  if (amount >= 1000000) {
    return `$${(amount / 1000000).toFixed(1)}M`;
  } else if (amount >= 1000) {
    return `$${(amount / 1000).toFixed(0)}K`;
  } else {
    return `$${amount.toLocaleString()}`;
  }
};