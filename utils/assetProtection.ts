import { Asset, AssetProtectionData, RiskAssessment } from '../types/assetProtection';

export const calculateAssetProtectionData = (assets: Asset[]): AssetProtectionData => {
  const totalAssetValue = assets.reduce((sum, asset) => sum + (asset.estimatedValue || 0), 0);
  const protectedAssets = assets.filter(asset => asset.isProtected).length;
  const unprotectedAssets = assets.filter(asset => !asset.isProtected).length;
  
  // Calculate protection score based on protected assets and their values
  const protectedValue = assets
    .filter(asset => asset.isProtected)
    .reduce((sum, asset) => sum + (asset.estimatedValue || 0), 0);
  
  const protectionScore = totalAssetValue > 0 ? Math.round((protectedValue / totalAssetValue) * 100) : 0;
  
  const riskAssessment = calculateRiskAssessment(assets);
  const recommendations = generateRecommendations(assets, riskAssessment);

  return {
    assets,
    totalAssetValue,
    protectedAssets,
    unprotectedAssets,
    protectionScore,
    riskAssessment,
    recommendations
  };
};

export const calculateRiskAssessment = (assets: Asset[]): RiskAssessment => {
  const highRiskAssets = assets.filter(asset => asset.riskLevel === 'high');
  const mediumRiskAssets = assets.filter(asset => asset.riskLevel === 'medium');
  const lowRiskAssets = assets.filter(asset => asset.riskLevel === 'low');
  
  // Calculate total risk exposure based on unprotected high-value assets
  const totalRiskExposure = assets
    .filter(asset => !asset.isProtected && (asset.riskLevel === 'high' || asset.riskLevel === 'medium'))
    .reduce((sum, asset) => sum + (asset.estimatedValue || 0), 0);
  
  const criticalGaps = identifyCriticalGaps(assets);

  return {
    highRiskAssets,
    mediumRiskAssets,
    lowRiskAssets,
    totalRiskExposure,
    criticalGaps
  };
};

export const identifyCriticalGaps = (assets: Asset[]): string[] => {
  const gaps: string[] = [];
  
  // Check for unprotected high-risk assets
  const unprotectedHighRisk = assets.filter(asset => asset.riskLevel === 'high' && !asset.isProtected);
  if (unprotectedHighRisk.length > 0) {
    gaps.push(`${unprotectedHighRisk.length} high-risk asset${unprotectedHighRisk.length !== 1 ? 's' : ''} without protection`);
  }
  
  // Check for high-value unprotected assets
  const highValueUnprotected = assets.filter(asset => 
    (asset.estimatedValue || 0) > 100000 && !asset.isProtected
  );
  if (highValueUnprotected.length > 0) {
    gaps.push(`${highValueUnprotected.length} high-value asset${highValueUnprotected.length !== 1 ? 's' : ''} over $100,000 unprotected`);
  }
  
  // Check for business assets without corporate protection
  const businessAssets = assets.filter(asset => 
    asset.category === 'business_assets' && !asset.isProtected
  );
  if (businessAssets.length > 0) {
    gaps.push('Business assets may need corporate structure protection');
  }
  
  // Check for real estate without trust protection
  const realEstateAssets = assets.filter(asset => 
    asset.category === 'real_estate' && !asset.isProtected && (asset.estimatedValue || 0) > 500000
  );
  if (realEstateAssets.length > 0) {
    gaps.push('High-value real estate may benefit from trust structures');
  }
  
  // Check for lack of diversification in protection methods
  const protectedAssets = assets.filter(asset => asset.isProtected);
  const protectionMethods = new Set(protectedAssets.map(asset => asset.protectionMethod));
  if (protectionMethods.size === 1 && protectedAssets.length > 3) {
    gaps.push('Consider diversifying protection methods across different strategies');
  }

  return gaps;
};

export const generateRecommendations = (assets: Asset[], riskAssessment: RiskAssessment): string[] => {
  const recommendations: string[] = [];
  
  // High-risk asset recommendations
  if (riskAssessment.highRiskAssets.length > 0) {
    const unprotectedHighRisk = riskAssessment.highRiskAssets.filter(asset => !asset.isProtected);
    if (unprotectedHighRisk.length > 0) {
      recommendations.push('Prioritize protection for high-risk assets through appropriate structures');
    }
  }
  
  // High-value asset recommendations
  const highValueAssets = assets.filter(asset => (asset.estimatedValue || 0) > 200000);
  const unprotectedHighValue = highValueAssets.filter(asset => !asset.isProtected);
  if (unprotectedHighValue.length > 0) {
    recommendations.push('Consider trust structures or corporate ownership for high-value assets');
  }
  
  // Business asset recommendations
  const businessAssets = assets.filter(asset => asset.category === 'business_assets');
  if (businessAssets.length > 0) {
    recommendations.push('Review business structure for optimal asset protection and tax efficiency');
  }
  
  // Real estate recommendations
  const realEstateAssets = assets.filter(asset => asset.category === 'real_estate');
  const unprotectedProperty = realEstateAssets.filter(asset => !asset.isProtected);
  if (unprotectedProperty.length > 0) {
    recommendations.push('Evaluate family trust structures for real estate holdings');
  }
  
  // Insurance recommendations
  const assetsNeedingInsurance = assets.filter(asset => 
    ['vehicles', 'personal_property', 'real_estate', 'collectibles', 'jewelry'].includes(asset.category) 
    && !asset.protectionMethod?.toLowerCase().includes('insurance')
  );
  if (assetsNeedingInsurance.length > 0) {
    recommendations.push('Ensure adequate insurance coverage for physical assets');
  }
  
  // Professional advice recommendation
  if (riskAssessment.totalRiskExposure > 500000) {
    recommendations.push('Consider professional advice for comprehensive asset protection strategy');
  }
  
  // Regular review recommendation
  const oldAssets = assets.filter(asset => {
    if (!asset.lastReviewDate) return true;
    const lastReview = new Date(asset.lastReviewDate);
    const oneYearAgo = new Date();
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1);
    return lastReview < oneYearAgo;
  });
  if (oldAssets.length > 0) {
    recommendations.push('Schedule regular reviews of asset protection arrangements');
  }

  return recommendations;
};

export const formatAmount = (amount: number): string => {
  if (amount >= 1000000) {
    return `$${(amount / 1000000).toFixed(1)}M`;
  } else if (amount >= 1000) {
    return `$${(amount / 1000).toFixed(0)}K`;
  } else {
    return `$${amount.toLocaleString()}`;
  }
};

export const getAssetRiskColor = (riskLevel: string): string => {
  switch (riskLevel) {
    case 'high': return '#EF4444';
    case 'medium': return '#F59E0B';
    case 'low': return '#10B981';
    default: return '#6B7280';
  }
};

export const getProtectionStatusColor = (isProtected: boolean): string => {
  return isProtected ? '#10B981' : '#EF4444';
};

export const calculateProtectionEffectiveness = (assets: Asset[]): {
  score: number;
  status: 'excellent' | 'good' | 'fair' | 'poor';
  message: string;
} => {
  if (assets.length === 0) {
    return { score: 0, status: 'poor', message: 'No assets registered' };
  }
  
  const totalValue = assets.reduce((sum, asset) => sum + (asset.estimatedValue || 0), 0);
  const protectedValue = assets
    .filter(asset => asset.isProtected)
    .reduce((sum, asset) => sum + (asset.estimatedValue || 0), 0);
  
  const protectionPercentage = totalValue > 0 ? (protectedValue / totalValue) * 100 : 0;
  
  // Factor in risk levels
  const highRiskUnprotected = assets.filter(asset => 
    asset.riskLevel === 'high' && !asset.isProtected
  ).length;
  
  let adjustedScore = protectionPercentage;
  
  // Penalize for high-risk unprotected assets
  if (highRiskUnprotected > 0) {
    adjustedScore -= (highRiskUnprotected * 10);
  }
  
  adjustedScore = Math.max(0, Math.min(100, adjustedScore));
  
  if (adjustedScore >= 80) {
    return { 
      score: adjustedScore, 
      status: 'excellent', 
      message: 'Excellent asset protection coverage' 
    };
  } else if (adjustedScore >= 60) {
    return { 
      score: adjustedScore, 
      status: 'good', 
      message: 'Good protection with room for improvement' 
    };
  } else if (adjustedScore >= 40) {
    return { 
      score: adjustedScore, 
      status: 'fair', 
      message: 'Fair protection, consider additional strategies' 
    };
  } else {
    return { 
      score: adjustedScore, 
      status: 'poor', 
      message: 'Poor protection, urgent attention needed' 
    };
  }
};

export const sortAssetsByPriority = (assets: Asset[]): Asset[] => {
  return [...assets].sort((a, b) => {
    // Prioritize by: 1) Risk level, 2) Protection status, 3) Value
    const riskOrder = { 'high': 3, 'medium': 2, 'low': 1 };
    const aRisk = riskOrder[a.riskLevel];
    const bRisk = riskOrder[b.riskLevel];
    
    if (aRisk !== bRisk) return bRisk - aRisk;
    
    // Unprotected assets come first
    if (a.isProtected !== b.isProtected) {
      return a.isProtected ? 1 : -1;
    }
    
    // Higher value assets come first
    return (b.estimatedValue || 0) - (a.estimatedValue || 0);
  });
};