import { RetirementReadiness } from '../types/superannuation';

export const calculateSuperProjection = (
  currentBalance: number,
  extraContributions: number,
  contributionFrequency: string,
  expectedReturnRate: number,
  yearsToRetirement: number,
  currentAge: number,
  annualSalary: number
): number => {
  const monthlyRate = expectedReturnRate / 100 / 12;
  const months = yearsToRetirement * 12;
  
  // Calculate monthly extra contributions
  let monthlyExtra = 0;
  switch (contributionFrequency) {
    case 'weekly':
      monthlyExtra = extraContributions * 4.33;
      break;
    case 'fortnightly':
      monthlyExtra = extraContributions * 2.17;
      break;
    case 'monthly':
      monthlyExtra = extraContributions;
      break;
    case 'quarterly':
      monthlyExtra = extraContributions / 3;
      break;
    case 'yearly':
      monthlyExtra = extraContributions / 12;
      break;
  }
  
  // Add mandatory employer contributions (9.5% of salary)
  const monthlyEmployerContribution = (annualSalary * 0.095) / 12;
  const totalMonthlyContribution = monthlyExtra + monthlyEmployerContribution;
  
  // Calculate compound growth
  if (monthlyRate === 0) {
    return currentBalance + totalMonthlyContribution * months;
  }
  
  const futureValue = currentBalance * Math.pow(1 + monthlyRate, months) +
    totalMonthlyContribution * ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate);
  
  return futureValue;
};

export const calculateRetirementIncome = (superBalance: number, retirementAge: number): number => {
  // Simple calculation: 4% withdrawal rate for sustainable retirement income
  return superBalance * 0.04;
};

export const getMonthlyFromFrequency = (amount: number, frequency: string): number => {
  switch (frequency) {
    case 'weekly': return amount * 4.33;
    case 'fortnightly': return amount * 2.17;
    case 'quarterly': return amount / 3;
    case 'yearly': return amount / 12;
    default: return amount; // monthly
  }
};

export const formatAmount = (amount: number): string => `$${amount.toLocaleString()}`;

export const getRetirementReadiness = (projectedIncome: number, targetIncome: number): RetirementReadiness => {
  const ratio = projectedIncome / targetIncome;
  
  if (ratio >= 1.2) {
    return {
      status: 'excellent',
      message: 'Exceeding retirement goals! 🎉',
      color: 'text-green-600'
    };
  } else if (ratio >= 1.0) {
    return {
      status: 'good', 
      message: 'On track for retirement 👍',
      color: 'text-green-600'
    };
  } else if (ratio >= 0.7) {
    return {
      status: 'fair',
      message: 'Close, but could use improvement 💪',
      color: 'text-yellow-600'
    };
  } else {
    return {
      status: 'poor',
      message: 'Needs significant improvement 🚨',
      color: 'text-red-600'
    };
  }
};

export const getRetirementAgeOptions = () => [
  { value: '60', label: '60 years (Early retirement)' },
  { value: '65', label: '65 years (Traditional)' },
  { value: '67', label: '67 years (Pension age)' },
  { value: '70', label: '70 years (Later retirement)' }
];