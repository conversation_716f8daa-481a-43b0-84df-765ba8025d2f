export const calculateCompoundGrowth = (
  principal: number,
  monthlyContribution: number,
  annualRate: number,
  years: number
): number => {
  const monthlyRate = annualRate / 100 / 12;
  const months = years * 12;
  
  // Future value of principal
  const principalFV = principal * Math.pow(1 + monthlyRate, months);
  
  // Future value of monthly contributions (annuity)
  const contributionFV = monthlyContribution * 
    ((Math.pow(1 + monthlyRate, months) - 1) / monthlyRate);
  
  return principalFV + contributionFV;
};

export const calculateTimeToGoal = (
  targetAmount: number,
  currentAmount: number,
  monthlyContribution: number,
  annualRate: number
): number => {
  if (monthlyContribution <= 0 && currentAmount >= targetAmount) return 0;
  if (monthlyContribution <= 0) return Infinity;
  
  const monthlyRate = annualRate / 100 / 12;
  
  if (monthlyRate === 0) {
    return Math.ceil((targetAmount - currentAmount) / monthlyContribution);
  }
  
  const months = Math.log(
    (targetAmount * monthlyRate + monthlyContribution) / 
    (currentAmount * monthlyRate + monthlyContribution)
  ) / Math.log(1 + monthlyRate);
  
  return Math.max(0, Math.ceil(months));
};

export const getMonthlyFromFrequency = (
  amount: number, 
  frequency: 'weekly' | 'monthly' | 'quarterly'
): number => {
  switch (frequency) {
    case 'weekly': return amount * 4.33;
    case 'quarterly': return amount / 3;
    default: return amount;
  }
};

export const formatAmount = (amount: number): string => `$${amount.toLocaleString()}`;

export const formatDate = (months: number): string => {
  if (months === Infinity) return 'Never';
  const years = Math.floor(months / 12);
  const remainingMonths = months % 12;
  if (years === 0) return `${remainingMonths} months`;
  if (remainingMonths === 0) return `${years} year${years > 1 ? 's' : ''}`;
  return `${years}y ${remainingMonths}m`;
};