export interface Asset {
  id: string;
  name: string;
  category: AssetCategory;
  estimatedValue?: number;
  description?: string;
  isProtected: boolean;
  protectionMethod?: string;
  protectionDetails?: string;
  riskLevel: 'low' | 'medium' | 'high';
  lastReviewDate?: string;
  notes?: string;
  createdDate: string;
  updatedDate: string;
}

export interface NewAsset {
  name: string;
  category: AssetCategory;
  estimatedValue: string;
  description: string;
  riskLevel: 'low' | 'medium' | 'high';
  notes: string;
}

export interface AssetProtectionData {
  assets: Asset[];
  totalAssetValue: number;
  protectedAssets: number;
  unprotectedAssets: number;
  protectionScore: number;
  riskAssessment: RiskAssessment;
  recommendations: string[];
}

export interface RiskAssessment {
  highRiskAssets: Asset[];
  mediumRiskAssets: Asset[];
  lowRiskAssets: Asset[];
  totalRiskExposure: number;
  criticalGaps: string[];
}

export type AssetCategory = 
  | 'real_estate'
  | 'vehicles'
  | 'investments'
  | 'business_assets'
  | 'personal_property'
  | 'intellectual_property'
  | 'cash_deposits'
  | 'collectibles'
  | 'jewelry'
  | 'electronics'
  | 'other';

export interface AssetCategoryInfo {
  id: AssetCategory;
  name: string;
  description: string;
  protectionMethods: string[];
  commonRisks: string[];
  icon: string;
  color: string;
  examples: string[];
}

export interface ProtectionGuideline {
  category: AssetCategory;
  title: string;
  description: string;
  methods: ProtectionMethod[];
  riskFactors: string[];
  tips: string[];
}

export interface ProtectionMethod {
  name: string;
  description: string;
  suitability: string[];
  cost: 'low' | 'medium' | 'high';
  effectiveness: 'low' | 'medium' | 'high';
  complexity: 'simple' | 'moderate' | 'complex';
}

export interface AssetProtectionEducation {
  title: string;
  description: string;
  keyPrinciples: string[];
  commonStrategies: string[];
  warningFlags: string[];
  professionalAdvice: string[];
}