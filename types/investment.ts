export interface InvestmentGoal {
  id: string;
  name: string;
  investmentType: 'stocks' | 'etfs' | 'bonds' | 'reits' | 'crypto' | 'index-funds' | 'mutual-funds';
  targetAmount: number;
  currentAmount: number;
  monthlyContribution: number;
  expectedReturnRate: number; // Annual percentage
  contributionFrequency: 'weekly' | 'monthly' | 'quarterly';
  estimatedCompletionDate: string;
  isCompleted: boolean;
  lastContributionDate?: string;
}

export interface InvestmentGoalsData {
  goals: InvestmentGoal[];
  totalTargetAmount: number;
  totalInvestedAmount: number;
  totalMonthlyContribution: number;
  projectedValue: number;
}

export interface NewInvestmentGoal {
  name: string;
  investmentType: 'stocks' | 'etfs' | 'bonds' | 'reits' | 'crypto' | 'index-funds' | 'mutual-funds';
  targetAmount: string;
  expectedReturnRate: string;
  contributionFrequency: 'weekly' | 'monthly' | 'quarterly';
  initialInvestment: number;
  monthlyContribution: number;
}

export interface InvestmentType {
  id: string;
  name: string;
  description: string;
  expectedReturn: string;
  riskLevel: string;
  color: string;
}