export interface InsurancePolicy {
  id: string;
  type: InsuranceType;
  policyNumber: string;
  itemOrMemberInsured: string;
  insuranceCompany: string;
  amountInsured: number;
  insideSuper: boolean;
  premiumAmount: number;
  premiumFrequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  renewalDate: string;
  contactNumber: string;
  claimFormLink?: string;
  documents?: PolicyDocument[];
  isActive: boolean;
  lastUpdated: string;
  notes?: string;
}

export interface PolicyDocument {
  id: string;
  name: string;
  type: 'policy' | 'certificate' | 'claim_form' | 'receipt' | 'correspondence' | 'other';
  uploadDate: string;
  fileSize?: string;
  url?: string; // In a real app, this would be a file URL
}

export interface NewInsurancePolicy {
  type: InsuranceType;
  policyNumber: string;
  itemOrMemberInsured: string;
  insuranceCompany: string;
  amountInsured: string;
  insideSuper: boolean;
  premiumAmount: string;
  premiumFrequency: 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  renewalDate: string;
  contactNumber: string;
  claimFormLink: string;
  notes: string;
}

export interface InsuranceData {
  policies: InsurancePolicy[];
  totalCoverage: number;
  totalPremiums: number;
  upcomingRenewals: InsurancePolicy[];
  coverageGaps: string[];
  recommendedCoverage: RecommendedCoverage;
}

export interface RecommendedCoverage {
  lifeInsurance: number;
  tpdInsurance: number;
  incomeProtection: number;
  homeInsurance: number;
  carInsurance: number;
}

export type InsuranceType = 
  | 'life'
  | 'tpd' // Total and Permanent Disability
  | 'income_protection'
  | 'health'
  | 'home_contents'
  | 'home_building'
  | 'car'
  | 'travel'
  | 'professional_indemnity'
  | 'public_liability'
  | 'business'
  | 'cyber'
  | 'other';

export interface InsuranceTypeInfo {
  id: InsuranceType;
  name: string;
  description: string;
  importance: 'essential' | 'recommended' | 'optional';
  averageCost: string;
  keyBenefits: string[];
  color: string;
  icon: string;
}

export interface InsuranceEducationContent {
  title: string;
  description: string;
  keyPoints: string[];
  tips: string[];
}