export interface SuperFund {
  id: string;
  fundName: string;
  currentBalance: number;
  extraContributions: number;
  contributionFrequency: 'weekly' | 'fortnightly' | 'monthly' | 'quarterly' | 'yearly';
  expectedReturnRate: number; // Annual percentage
  lastContributionDate?: string;
}

export interface SuperannuationData {
  retirementAge: number;
  targetRetirementIncome: number; // Annual amount
  currentAge: number;
  superFunds: SuperFund[];
  totalCurrentBalance: number;
  totalExtraContributions: number;
  projectedRetirementBalance: number;
  projectedRetirementIncome: number;
  isOnTrack: boolean;
}

export interface NewSuperFund {
  fundName: string;
  currentBalance: string;
  extraContributions: string;
  contributionFrequency: 'weekly' | 'fortnightly' | 'monthly' | 'quarterly' | 'yearly';
  expectedReturnRate: string;
}

export interface RetirementLifestyle {
  id: string;
  amount: number;
  title: string;
  description: string;
  weeklyAmount: string;
  color: string;
}

export interface RetirementReadiness {
  status: 'excellent' | 'good' | 'fair' | 'poor';
  message: string;
  color: string;
}