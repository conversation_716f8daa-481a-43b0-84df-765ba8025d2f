export interface Will {
  id: string;
  hasWill: boolean;
  willType: 'simple' | 'testamentary_trust' | 'complex' | 'not_applicable';
  lastUpdated?: string;
  lawyerName?: string;
  lawyerContact?: string;
  executorName?: string;
  executorContact?: string;
  witnessNames?: string[];
  documentUrl?: string;
  fileName?: string;
  uploadDate?: string;
  isVerified: boolean;
  notes?: string;
  createdDate: string;
  updatedDate: string;
}

export interface PowerOfAttorney {
  id: string;
  hasFinancialPOA: boolean;
  hasMedicalPOA: boolean;
  financialAttorneyName?: string;
  financialAttorneyContact?: string;
  medicalAttorneyName?: string;
  medicalAttorneyContact?: string;
  documentUrls?: string[];
  lastUpdated?: string;
  isCompleted: boolean;
  notes?: string;
}

export interface Beneficiary {
  id: string;
  name: string;
  relationship: string;
  contactInfo: string;
  sharePercentage?: number;
  specificAssets?: string[];
  isContingent: boolean;
  notes?: string;
}

export interface EstatePlanningData {
  will: Will;
  powerOfAttorney: PowerOfAttorney;
  beneficiaries: Beneficiary[];
  totalEstateValue: number;
  hasCompletedChecklist: boolean;
  checklistItems: ChecklistItem[];
  completionScore: number;
  recommendations: string[];
  nextReviewDate?: string;
  professionalAdvice: boolean;
}

export interface ChecklistItem {
  id: string;
  title: string;
  description: string;
  category: 'will' | 'power_of_attorney' | 'beneficiaries' | 'assets' | 'tax' | 'review';
  isCompleted: boolean;
  priority: 'high' | 'medium' | 'low';
  dueDate?: string;
  completedDate?: string;
  notes?: string;
}

export interface WillGuidelines {
  basicRequirements: string[];
  commonMistakes: string[];
  whenToUpdate: string[];
  professionalAdvice: string[];
}

export interface EstatePlanningEducation {
  title: string;
  description: string;
  keyComponents: EstateComponent[];
  importanceFactors: string[];
  commonMistakes: string[];
  reviewFrequency: string[];
  professionalHelp: string[];
}

export interface EstateComponent {
  name: string;
  description: string;
  importance: 'essential' | 'important' | 'optional';
  whoNeedsIt: string[];
  timeframe: string;
  cost: 'low' | 'medium' | 'high';
  complexity: 'simple' | 'moderate' | 'complex';
}

export interface DocumentUpload {
  id: string;
  fileName: string;
  fileType: string;
  fileSize: number;
  uploadDate: string;
  documentType: 'will' | 'power_of_attorney' | 'beneficiary_form' | 'trust_document' | 'other';
  isVerified: boolean;
  notes?: string;
}

export interface EstatePlanningSettings {
  reminderFrequency: 'yearly' | 'bi_yearly' | 'every_3_years' | 'custom';
  autoReminders: boolean;
  shareWithFamily: boolean;
  encryptDocuments: boolean;
  backupLocation: string[];
}